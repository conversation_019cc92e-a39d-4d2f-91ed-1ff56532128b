# stsvpo-backend

##  Docker MySQL 8 docker compose for local development

Place file under root of the project.
file: `./docker-compose.yaml`
command: `docker compose up -d`

```yaml
services:
  mysql:
    image: mysql:8.0
    container_name: wangchen_mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: wangchen_db
      MYSQL_USER: admin
      MYSQL_PASSWORD: admin
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql # This will be fresh since we used -v with down
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: [ "CMD", "mysqladmin", "ping", "-h", "localhost" ]
      timeout: 5s
      retries: 10

volumes:
  mysql_data:
    name: mysql_data
```

# Prisma DB

verion: >= 6.5

## Init Prisma DB

```bash
npx prisma migrate reset --force
npx prisma migrate dev
npx prisma generate
```

## Web UI for database

```
npx prisma generate
```


# Deploy

部署到 Server 指令, 參考 Deploy.md

# Start development server

Node version: >= 22 LTS

```bash
npm run dev
```

## Starting the Application with PM2

Development mode:

```bash
pm2 start pm2.js
```

Production mode:

```bash
pm2 start pm2.js --env production
```

## PM2 Common Commands

```bash
# Start application
pm2 start pm2.js

# Restart application
pm2 restart stsvpo-server

# Stop application
pm2 stop stsvpo-server

# View logs
pm2 logs stsvpo-server

# Monitor application
pm2 monit

# List running applications
pm2 list

# View application details
pm2 show stsvpo-server

# Save PM2 process list
pm2 save

# Setup PM2 to start on system boot
pm2 startup
```

# Env data

## Copy file `env.example` and modify it.

```bash
cp env.example .env
```

## Local development `.env` data

```text
# Server Configuration
PORT=3000
NODE_ENV=development

# Database Configuration
DATABASE_URL="mysql://root:root@127.0.0.1:3306/wangchen_db"
DB_HOST=127.0.0.1
DB_PORT=3306
DB_NAME=wangchen_db
DB_USER=root
DB_PASSWORD=admin

# Session Configuration
SESSION_SECRET=your_secure_session_secret

# File Upload Configuration
UPLOAD_PATH=uploads/
MAX_FILE_SIZE=5242880 # 5MB in bytes

# Logging Configuration
LOG_LEVEL=debug
LOG_DIR=./logs
LOG_FILE_ERROR=error.log
LOG_FILE_COMBINED=combined.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=7

```