{"name": "stsvpo-backend", "version": "1.0.1", "main": "src/app.js", "prisma": {"seed": "node prisma/seed.js"}, "scripts": {"start": "node -r ./tracing.js src/app.js", "dev": "nodemon src/app.js", "test-otel": "node test-otel-logging.js", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:studio": "prisma studio", "init-db": "node prisma/seed.js"}, "dependencies": {"@opentelemetry/api-logs": "^0.200.0", "@opentelemetry/exporter-logs-otlp-http": "^0.200.0", "@opentelemetry/exporter-prometheus": "^0.200.0", "@opentelemetry/exporter-trace-otlp-http": "^0.200.0", "@opentelemetry/resources": "^2.0.0", "@opentelemetry/sdk-logs": "^0.200.0", "@opentelemetry/sdk-node": "^0.200.0", "@opentelemetry/semantic-conventions": "^1.30.0", "@opentelemetry/winston-transport": "^0.11.0", "@prisma/client": "^6.6.0", "bcryptjs": "^2.4.3", "connect-flash": "^0.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^5.1.0", "express-ejs-layouts": "^2.5.1", "express-prom-bundle": "^8.0.0", "express-rate-limit": "^7.5.0", "express-session": "^1.18.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "method-override": "^3.0.0", "moment": "^2.30.1", "multer": "^1.4.5-lts.1", "mysql2": "^3.12.0", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "prom-client": "^15.1.3", "quill-delta-to-html": "^0.12.1", "slugify": "^1.6.6", "winston": "^3.17.0"}, "devDependencies": {"@opentelemetry/auto-instrumentations-node": "^0.57.0", "@opentelemetry/instrumentation-express": "^0.48.0", "nodemon": "^3.0.3", "prisma": "^6.8.0"}}