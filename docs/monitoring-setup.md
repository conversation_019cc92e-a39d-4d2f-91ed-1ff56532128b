# Monitoring Setup for Express Application

This guide explains how to set up monitoring for the Express application using Prometheus, node-exporter, and Grafana.

## Overview

The monitoring stack consists of:

1. **Prometheus**: For metrics collection and storage with 1-hour retention and remote write capabilities
2. **node-exporter**: For system-level metrics (CPU, memory, disk, network)
3. **Grafana**: For metrics visualization and dashboards

## Prerequisites

- Docker and Docker Compose installed
- The Express application running with metrics enabled (`ENABLE_METRICS=true` in .env)

## Setup Instructions

### 1. Start the Monitoring Stack

```bash
docker-compose -f docker-compose-monitoring.yml up -d
```

This will start Prometheus, node-exporter, and Grafana.

### 2. Access the Services

- **Prometheus**: http://localhost:9090
- **Grafana**: http://localhost:3030 (username: admin, password: admin)
- **node-exporter**: http://localhost:9100/metrics

### 3. Configure Grafana

1. Log in to Grafana at http://localhost:3030
2. Go to Configuration > Data Sources
3. Add a new Prometheus data source:
   - Name: Prometheus
   - URL: http://prometheus:9090
   - Access: Server (default)
   - Save & Test

### 4. Import Dashboards

For node-exporter metrics:
1. Go to Create > Import
2. Enter dashboard ID 1860 (Node Exporter Full)
3. Select your Prometheus data source
4. Click Import

For Express.js metrics:
1. Go to Create > Import
2. Click "Upload JSON file" and select the `express-dashboard.json` file (if available)
3. Or create a new dashboard with panels for:
   - HTTP request rate
   - HTTP request duration
   - Database query rate
   - Database query duration
   - Memory usage

## Configuration Details

### Prometheus Configuration

Key configurations in Prometheus:

- **Local storage TTL**: 1 hour (`--storage.tsdb.retention.time=1h`)
- **Remote write endpoint**: http://tianyen-service.com:8428/api/v1/write
- **Scrape targets**:
  - Express application: host.docker.internal:3000
  - node-exporter: node-exporter:9100

### Environment-Specific Settings

The monitoring stack can be used in different environments:

- **Development**: Uses basic auth with username/password from `.env`
- **Production**: Uses more secure credentials from `.env.production`

## Extending the Monitoring

### Adding More Metrics

To add custom metrics to your Express application:

1. Define new metrics in `src/config/metrics.js`
2. Use them in your application code
3. They will automatically appear in Prometheus

### Adding More Exporters

You can extend the `docker-compose-monitoring.yml` file to include additional exporters:

```yaml
services:
  # Existing services...
  
  mysql-exporter:
    image: prom/mysqld-exporter
    # configuration...
```

## Troubleshooting

### Common Issues

1. **Can't connect to Express app metrics**:
   - Ensure the Express app is running
   - Check that `ENABLE_METRICS=true` in .env
   - Verify the correct host and port in prometheus-config.yml

2. **Data not being sent to remote write endpoint**:
   - Check network connectivity to tianyen-service.com
   - Verify the URL is correct
   - Look for errors in Prometheus logs: `docker-compose logs prometheus`

3. **node-exporter not collecting metrics**:
   - Check if node-exporter container is running
   - Verify volume mounts are correct 