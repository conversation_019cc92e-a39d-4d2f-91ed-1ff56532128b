# Prometheus Metrics Implementation

This document describes the Prometheus metrics implementation in the Express.js application.

## Overview

We have integrated Prometheus metrics to monitor various aspects of our application, including:

- HTTP request counts and durations
- Database query counts and durations
- Node.js memory usage
- Default Node.js metrics (CPU, event loop, etc.)

## Configuration

Metrics are configured via environment variables:

```env
# Prometheus Metrics Configuration
ENABLE_METRICS=true                # Enable/disable metrics collection
METRICS_USER=prometheus            # Username for basic auth (production)
METRICS_PASSWORD=prometheus123     # Password for basic auth (production)
METRICS_COLLECT_INTERVAL=10000     # Collection interval in milliseconds
```

## Available Metrics

| Metric | Type | Description | Labels |
|--------|------|-------------|--------|
| http_request_duration_seconds | Histogram | Duration of HTTP requests | method, route, status_code |
| http_requests_total | Counter | Total number of HTTP requests | method, route, status_code |
| database_queries_total | Counter | Total number of database queries | operation, model |
| database_query_duration_seconds | Histogram | Duration of database queries | operation, model |
| nodejs_memory_usage_bytes | Gauge | Memory usage of Node.js process | type (rss, heapTotal, heapUsed, external) |
| up | Gauge | Indicates if the service is up (1) or down (0) | - |
| nodejs_* | Various | Default Node.js metrics | Various |

## Accessing Metrics

Metrics are exposed on the `/metrics` endpoint:

- **Development**: Metrics are publicly accessible at `http://localhost:3000/metrics`
- **Production**: Metrics are protected with Basic Authentication:
  - URL: `http://your-server/metrics`
  - Username and password are set via environment variables

## Prometheus Configuration

A sample Prometheus configuration is available in `prometheus-config.yml` at the root of the project. To use it with Prometheus:

1. Copy the configuration to your Prometheus server
2. Update the target hosts and credentials if needed
3. Restart Prometheus

Example:

```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'stsvpo-server'
    scrape_interval: 10s
    metrics_path: /metrics
    scheme: http
    basic_auth:
      username: prometheus
      password: prometheus123
    static_configs:
      - targets: ['localhost:3000']
```

## Security Considerations

In production:
- The metrics endpoint is secured with basic authentication
- Use strong, unique credentials for the metrics endpoint
- Consider implementing network-level restrictions (firewall, private subnets)
- Change default credentials in `.env.production`

## Extending Metrics

To add custom application metrics:

1. Define new metrics in `src/config/metrics.js`
2. Register the metrics with the registry
3. Export the metrics in the `metrics` object
4. Use them in your application code

Example:

```javascript
// Define a new metric
const myNewMetric = new promClient.Counter({
  name: 'my_custom_metric',
  help: 'Description of my custom metric',
  labelNames: ['label1', 'label2']
});

// Register it
register.registerMetric(myNewMetric);

// Add to exports
module.exports = {
  // ...
  metrics: {
    // ...
    myNewMetric
  }
};
```

Then use it in your code:

```javascript
const { metrics } = require('./config/metrics');

// Increment counter
metrics.myNewMetric.inc({ label1: 'value1', label2: 'value2' });
``` 