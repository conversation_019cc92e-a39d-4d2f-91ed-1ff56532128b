name: CI/CD Pipeline

on:
  workflow_dispatch:
    # Manual trigger only

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: No-op step
        run: echo "Workflow triggered but no actions will be performed"

#   deploy-test:
#     needs: test
#     runs-on: ubuntu-latest
#     if: github.ref == 'refs/heads/frontend' && github.event_name == 'push'
    
#     steps:
#       # Checkout with full history to detect changed files
#       - name: Checkout code
#         uses: actions/checkout@v4
#         with:
#           fetch-depth: 2

#       # Setup SSH and deploy changed files to Test server
#       - name: Deploy Changed Files to Test Server
#         env:
#           EC2_UBUNTU_PRIVATE_KEY: ${{ secrets.TEST_SSH_KEY }}
#           EC2_HOST: "**********"
#           EC2_PORT: "22"
#         run: |
#           export DEBIAN_FRONTEND=noninteractive
#           which ssh-agent || ( apt-get update -yq && apt-get install openssh-client -yq )
#           apt install zip unzip git -yq
#           eval $(ssh-agent -s)
#           echo "$EC2_UBUNTU_PRIVATE_KEY" | tr -d '\r' | ssh-add -
#           mkdir -p ~/.ssh
#           chmod 700 ~/.ssh
#           echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
#           cat ~/.ssh/config 
#           touch ~/.ssh/known_hosts
#           ssh-keyscan -p $EC2_PORT $EC2_HOST >> ~/.ssh/known_hosts
#           chmod 644 ~/.ssh/known_hosts
#           echo "Detecting changed files..."
#           CHANGED_FILES=$(git diff-tree --no-commit-id --name-only -r HEAD)
#           echo "$CHANGED_FILES"
#           echo "Starting to deploy changed files..."
#           IFS=$'\n'
#           for file in $CHANGED_FILES; do
#             echo "Processing file: $file"
#             if [ -f "$file" ]; then
#               ssh -p $EC2_PORT -o StrictHostKeyChecking=no okz5289@$EC2_HOST "sudo mkdir -p /data/wwwroot/stsvpo.bun.tw/$(dirname $file)"
#               echo "Copying $file to EC2 instance..."
#               scp -P $EC2_PORT -o StrictHostKeyChecking=no $file okz5289@$EC2_HOST:/tmp/$file
#               ssh -p $EC2_PORT -o StrictHostKeyChecking=no okz5289@$EC2_HOST "sudo mv /tmp/$file /data/wwwroot/stsvpo.bun.tw/$file"
#             else
#               echo "Skipping $file as it is not a regular file."
#             fi
#           done
#           echo "Deployment of changed files completed."

#       # Restart application after deployment
#       - name: Restart Application on Test Server
#         env:
#           EC2_UBUNTU_PRIVATE_KEY: ${{ secrets.TEST_SSH_KEY }}
#           EC2_HOST: ${{ secrets.TEST_HOST }}
#           EC2_PORT: ${{ secrets.TEST_PORT || 22 }}
#         run: |
#           eval $(ssh-agent -s)
#           echo "$EC2_UBUNTU_PRIVATE_KEY" | tr -d '\r' | ssh-add -
#           ssh -p $EC2_PORT -o StrictHostKeyChecking=no okz5289@$EC2_HOST "
#             sudo su - root -c 'cd /data/wwwroot/stsvpo.bun.tw && npm ci --only=production'
#             sudo su - root -c 'cd /data/wwwroot/stsvpo.bun.tw && npm run prisma:generate'
#             sudo su - root -c 'cd /data/wwwroot/stsvpo.bun.tw && npm run prisma:migrate'
#             sudo su - root -c 'pm2 restart 0 --update-env'
#           "

#   # deploy-prod:
#   #   needs: test
#   #   runs-on: ubuntu-latest
#   #   if: github.ref == 'refs/heads/master' && github.event_name == 'pull_request'
    
#   #   steps:
#   #     # Checkout with full history to detect changed files
#   #     - name: Checkout code
#   #       uses: actions/checkout@v4
#   #       with:
#   #         fetch-depth: 2

#   #     # Setup SSH and deploy changed files to Production server
#   #     - name: Deploy Changed Files to Production Server
#   #       env:
#   #         EC2_UBUNTU_PRIVATE_KEY: ${{ secrets.PROD_SSH_KEY }}
#   #         EC2_HOST: ${{ secrets.PROD_HOST }}
#   #         EC2_PORT: ${{ secrets.PROD_PORT || 22 }}
#   #       run: |
#   #         export DEBIAN_FRONTEND=noninteractive
#   #         which ssh-agent || ( apt-get update -yq && apt-get install openssh-client -yq )
#   #         apt install zip unzip git -yq
#   #         eval $(ssh-agent -s)
#   #         echo "$EC2_UBUNTU_PRIVATE_KEY" | tr -d '\r' | ssh-add -
#   #         mkdir -p ~/.ssh
#   #         chmod 700 ~/.ssh
#   #         echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
#   #         cat ~/.ssh/config 
#   #         touch ~/.ssh/known_hosts
#   #         ssh-keyscan -p $EC2_PORT $EC2_HOST >> ~/.ssh/known_hosts
#   #         chmod 644 ~/.ssh/known_hosts
#   #         echo "Detecting changed files..."
#   #         CHANGED_FILES=$(git diff-tree --no-commit-id --name-only -r HEAD)
#   #         echo "$CHANGED_FILES"
#   #         echo "Starting to deploy changed files..."
#   #         IFS=$'\n'
#   #         for file in $CHANGED_FILES; do
#   #           echo "Processing file: $file"
#   #           if [ -f "$file" ]; then
#   #             ssh -p $EC2_PORT -o StrictHostKeyChecking=no ubuntu@$EC2_HOST "mkdir -p /home/<USER>/wangchen-backend/$(dirname $file)"
#   #             echo "Copying $file to EC2 instance..."
#   #             scp -P $EC2_PORT -o StrictHostKeyChecking=no $file ubuntu@$EC2_HOST:/home/<USER>/wangchen-backend/$file
#   #           else
#   #             echo "Skipping $file as it is not a regular file."
#   #           fi
#   #         done
#   #         echo "Deployment of changed files completed."

#   #     # Restart application after deployment
#   #     - name: Restart Application on Production Server
#   #       env:
#   #         EC2_UBUNTU_PRIVATE_KEY: ${{ secrets.PROD_SSH_KEY }}
#   #         EC2_HOST: ${{ secrets.PROD_HOST }}
#   #         EC2_PORT: ${{ secrets.PROD_PORT || 22 }}
#   #       run: |
#   #         eval $(ssh-agent -s)
#   #         echo "$EC2_UBUNTU_PRIVATE_KEY" | tr -d '\r' | ssh-add -
#   #         ssh -p $EC2_PORT -o StrictHostKeyChecking=no ubuntu@$EC2_HOST "
#   #           cd /home/<USER>/wangchen-backend
#   #           npm ci --only=production
#   #           npm run prisma:generate
#   #           npm run prisma:migrate
#   #           pm2 restart wangchen-backend-prod || pm2 start src/app.js --name wangchen-backend-prod
#   #         "

