---
title: Stsvpo Backend Architecture
---
%%{init: {'theme': 'default'}}%%
flowchart TB
    %% Main Application Entry
    APP[app.js] --> ROUTES[Routes]
    APP --> MIDDLEWARE[Middleware]
    APP --> CONFIG[Configuration]
    
    %% Routes Layer
    ROUTES --> ADMIN[Admin Routes]
    ROUTES --> FRONTEND[Frontend Routes]
    
    %% Admin Routes
    ADMIN --> ADMIN_AUTH["`admin/auth/*`"]
    ADMIN --> ADMIN_DASHBOARD["`admin/dashboard`"]
    ADMIN --> ADMIN_USERS["`admin/users/*`"]
    ADMIN --> ADMIN_BANNERS["`admin/banners/*`"]
    ADMIN --> ADMIN_NEWS["`admin/news/*`"]
    ADMIN --> ADMIN_PLATFORMS["`admin/platforms/*`"]
    ADMIN --> ADMIN_LINKS["`admin/links/*`"]
    ADMIN --> ADMIN_FAQ["`admin/faq/*`"]
    ADMIN --> ADMIN_DOWNLOADS["`admin/downloads/*`"]
    ADMIN --> ADMIN_CONTACT["`admin/contact/*`"]
    ADMIN --> ADMIN_PAGES["`admin/pages/*`"]
    ADMIN --> ADMIN_MEDIA["`admin/media/*`"]
    ADMIN --> ADMIN_ARTICLES["`admin/articles/*`"]
    ADMIN --> ADMIN_CATEGORIES["`admin/categories/*`"]
    ADMIN --> ADMIN_PROMOTIONS["`admin/promotions/*`"]
    ADMIN --> ADMIN_ABOUT["`admin/about/*`"]
    ADMIN --> ADMIN_PARTNERS["`admin/partners/*`"]
    ADMIN --> ADMIN_PROJECTS["`admin/projects/*`"]
    ADMIN --> ADMIN_SITE_SETTINGS["`admin/site-settings`"]
    ADMIN --> ADMIN_PAGE_IMAGES["`admin/pageImages/*`"]
    ADMIN --> ADMIN_FRONTPAGE["`admin/frontpage/*`"]
    ADMIN --> ADMIN_VISITS["`admin/visits/*`"]
    
    %% Frontend Routes with Language Parameter
    FRONTEND --> HOME["`/:language`"]
    FRONTEND --> NEWS["`/:language/news`"]
    FRONTEND --> NEWS_ITEM["`/:language/news/:id`"]
    FRONTEND --> NEWS_CAT["`/:language/news/category/:id`"]
    FRONTEND --> PLATFORMS["`/:language/platforms`"]
    FRONTEND --> PLATFORM_ITEM["`/:language/platform/:slug`"]
    FRONTEND --> FAQ["`/:language/faq`"]
    FRONTEND --> DOWNLOADS["`/:language/downloads`"]
    FRONTEND --> DOWNLOAD_ITEM["`/:language/downloads/:id`"]
    FRONTEND --> CONTACT["`/:language/contact`"]
    FRONTEND --> ABOUT["`/:language/about`"]
    FRONTEND --> PROMOTIONS["`/:language/promotions`"]
    FRONTEND --> PROMOTION_ITEM["`/:language/promotions/:id`"]
    FRONTEND --> PAGE["`/:language/page/:slug`"]
    FRONTEND --> ARTICLES["`/:language/articles`"]
    FRONTEND --> ARTICLE["`/:language/articles/:id`"]
    FRONTEND --> CATEGORY["`/:language/category/:id`"]
    FRONTEND --> PARTNERS["`/:language/partners`"]
    FRONTEND --> PROJECTS["`/:language/projects`"]
    FRONTEND --> PROJECT_ITEM["`/:language/projects/:id`"]
    
    %% Controllers Layer
    subgraph Controllers["**Controllers**"]
        direction TB
        AUTH_CTRL[AuthController]
        USER_CTRL[UserController]
        BANNER_CTRL[BannerController]
        NEWS_CTRL[NewsController]
        PLATFORM_CTRL[PlatformController]
        LINK_CTRL[LinksController]
        FAQ_CTRL[FAQController]
        DOWNLOAD_CTRL[DownloadController]
        CONTACT_CTRL[ContactController]
        DASH_CTRL[DashboardController]
        ARTICLE_CTRL[ArticleController]
        CATEGORY_CTRL[CategoryController]
        CAT_PERM_CTRL[CategoryPermissionController]
        MEDIA_CTRL[MediaController]
        PAGE_CTRL[PageController]
        PAGE_IMG_CTRL[PageImageController]
        DOWNLOAD_CAT_CTRL[DownloadCategoryController]
        PROMOTION_CTRL[PromotionController]
        ABOUT_CTRL[AboutController]
        CONTACT_CAT_CTRL[ContactCategoryController]
        SITE_SETTINGS_CTRL[SiteSettingsController]
        PARTNER_CTRL[PartnerController]
        CONTACT_AGREEMENT_CTRL[ContactAgreementController]
        PROJECT_CTRL[ProjectController]
        FRONTEND_CTRL[FrontendController]
        FRONTPAGE_CTRL[FrontpageController]
        VISIT_CTRL[VisitController]
    end
    
    %% Models Layer via Prisma
    subgraph Database["**Database Models**"]
        direction TB
        USER[User]
        ROLE[Role]
        BANNER_MODEL[Banner]
        NEWS_MODEL[News]
        NEWS_CAT_MODEL[NewsCategory]
        PLATFORM_MODEL[Platform]
        PLATFORM_CAT_MODEL[PlatformCategory]
        LINK_MODEL[Link]
        FAQ_MODEL[FAQ]
        FAQ_CAT_MODEL[FAQCategory]
        DOWNLOAD_MODEL[Download]
        DOWNLOAD_CAT_MODEL[DownloadCategory]
        CONTACT_MODEL[Contact]
        CONTACT_CAT_MODEL[ContactCategory]
        ARTICLE_MODEL[Article]
        CATEGORY_MODEL[Category]
        CATEGORY_PERMISSION_MODEL[CategoryPermission]
        MEDIA_MODEL[Media]
        PAGE_MODEL[Page]
        PAGE_IMAGE_MODEL[PageImage]
        PROMOTION_MODEL[Promotion]
        PROMOTION_CAT_MODEL[PromotionCategory]
        ABOUT_MODEL[About]
        SITE_SETTINGS_MODEL[SiteSettings]
        PARTNER_MODEL[Partner]
        PARTNER_CAT_MODEL[PartnerCategory]
        PROJECT_MODEL[Project]
        CONTACT_AGREEMENT_MODEL[ContactAgreement]
        FRONTPAGE_MODEL[FrontpageItem]
        FRONTPAGE_CAT_MODEL[FrontpageCategory]
        VISIT_MODEL[Visit]
    end
    
    %% Permission System
    subgraph Permissions["**Permission System**"]
        direction TB
        ROLE_PERM[Role-based Permissions]
        CAT_PERM[Category-specific Permissions]
        
        ROLE_PERM --> SUPER_ADMIN[Super Admin]
        ROLE_PERM --> ADMIN_ROLE[Admin]
        ROLE_PERM --> EDITOR_ROLE[Editor]
        ROLE_PERM --> GUEST_ROLE[Guest]
        
        CAT_PERM --> VIEW_PERM[View Permission]
        CAT_PERM --> CREATE_PERM[Create Permission]
        CAT_PERM --> EDIT_PERM[Edit Permission]
        CAT_PERM --> DELETE_PERM[Delete Permission]
    end
    
    %% Middleware Components
    subgraph Middleware["**Middleware**"]
        direction TB
        AUTH_MID[auth.js]
        UPLOAD[upload.js]
        BANNER_UPLOAD[bannerUpload.js]
        NEWS_UPLOAD[newsImageUpload.js]
        PLATFORM_UPLOAD[platformImageUpload.js]
        DOWNLOAD_UPLOAD[downloadFileUpload.js]
        PAGE_ATTACHMENT_UPLOAD[pageAttachmentUpload.js]
        PAGE_IMAGE_UPLOAD[pageImageUpload.js]
        PROMOTION_UPLOAD[promotionImageUpload.js]
        SITE_LOGO_UPLOAD[siteLogoUpload.js]
        LINK_IMAGE_UPLOAD[linkImageUpload.js]
        LANGUAGE_MID[languageMiddleware.js]
        ERROR[error.js]
    end
    
    %% Views Layer
    subgraph Views["**Views**"]
        direction TB
        LAYOUTS[Layouts] --> ADMIN_LAYOUT[Admin Layout]
        LAYOUTS --> FRONTEND_LAYOUT[Frontend Layout]
        
        PAGES[Pages] --> ADMIN_VIEWS[Admin Views]
        ADMIN_VIEWS --> BANNER_VIEWS[Banner Views]
        ADMIN_VIEWS --> NEWS_VIEWS[News Views]
        ADMIN_VIEWS --> PLATFORM_VIEWS[Platform Views]
        ADMIN_VIEWS --> LINK_VIEWS[Link Views]
        ADMIN_VIEWS --> FAQ_VIEWS[FAQ Views]
        ADMIN_VIEWS --> DOWNLOAD_VIEWS[Download Views]
        ADMIN_VIEWS --> CONTACT_VIEWS[Contact Views]
        ADMIN_VIEWS --> PAGE_VIEWS[Page Views]
        ADMIN_VIEWS --> ARTICLE_VIEWS[Article Views]
        ADMIN_VIEWS --> CATEGORY_VIEWS[Category Views]
        ADMIN_VIEWS --> USER_VIEWS[User Views]
        ADMIN_VIEWS --> MEDIA_VIEWS[Media Views]
        ADMIN_VIEWS --> PROMOTION_VIEWS[Promotion Views]
        ADMIN_VIEWS --> ABOUT_VIEWS[About Views]
        ADMIN_VIEWS --> PARTNER_VIEWS[Partner Views]
        ADMIN_VIEWS --> PROJECT_VIEWS[Project Views]
        ADMIN_VIEWS --> SITE_SETTINGS_VIEWS[Site Settings Views]
        ADMIN_VIEWS --> FRONTPAGE_VIEWS[Frontpage Views]
        ADMIN_VIEWS --> VISIT_VIEWS[Visit Views]
        
        PAGES --> FRONTEND_VIEWS[Frontend Views]
        FRONTEND_VIEWS --> BANNER_CAROUSEL[Banner Carousel]
        FRONTEND_VIEWS --> NEWS_LIST[News List]
        FRONTEND_VIEWS --> NEWS_DETAIL[News Detail]
        FRONTEND_VIEWS --> PLATFORM_LIST[Platform List]
        FRONTEND_VIEWS --> PLATFORM_DETAIL[Platform Detail]
        FRONTEND_VIEWS --> FAQ_LIST[FAQ List]
        FRONTEND_VIEWS --> DOWNLOAD_LIST[Download List]
        FRONTEND_VIEWS --> CONTACT_FORM[Contact Form]
        FRONTEND_VIEWS --> ABOUT_PAGE[About Page]
        FRONTEND_VIEWS --> PROMOTION_LIST[Promotion List]
        FRONTEND_VIEWS --> PROMOTION_DETAIL[Promotion Detail]
        FRONTEND_VIEWS --> ARTICLE_LIST[Article List]
        FRONTEND_VIEWS --> ARTICLE_DETAIL[Article Detail]
        FRONTEND_VIEWS --> DYNAMIC_PAGE[Dynamic Page]
        FRONTEND_VIEWS --> PARTNER_LIST[Partner List]
        FRONTEND_VIEWS --> PROJECT_LIST[Project List]
        FRONTEND_VIEWS --> PROJECT_DETAIL[Project Detail]
        
        PARTIALS[Partials] --> NAV[Navigation]
        PARTIALS --> FORMS[Forms]
        PARTIALS --> LANGUAGE_SWITCHER[Language Switcher]
    end
    
    %% Authentication Flow
    subgraph AuthFlow["**Authentication Flow**"]
        direction TB
        LOGIN[Login]
        SESSION[Session Management]
        PERMISSIONS_CHECK[Permissions Check]
        
        LOGIN --> SESSION
        SESSION --> PERMISSIONS_CHECK
        PERMISSIONS_CHECK --> IS_AUTHENTICATED[isAuthenticated]
        PERMISSIONS_CHECK --> HAS_ROLE[hasRole]
        PERMISSIONS_CHECK --> HAS_PERMISSION[hasPermission]
        PERMISSIONS_CHECK --> IS_OWNER_OR_HAS_PERMISSION[isOwnerOrHasPermission]
    end
    
    %% Relationships - Admin Routes to Controllers
    ADMIN_AUTH --> AUTH_CTRL
    ADMIN_USERS --> USER_CTRL
    ADMIN_BANNERS --> BANNER_CTRL
    ADMIN_NEWS --> NEWS_CTRL
    ADMIN_PLATFORMS --> PLATFORM_CTRL
    ADMIN_LINKS --> LINK_CTRL
    ADMIN_FAQ --> FAQ_CTRL
    ADMIN_DOWNLOADS --> DOWNLOAD_CTRL
    ADMIN_CONTACT --> CONTACT_CTRL
    ADMIN_DASHBOARD --> DASH_CTRL
    ADMIN_ARTICLES --> ARTICLE_CTRL
    ADMIN_CATEGORIES --> CATEGORY_CTRL
    ADMIN_MEDIA --> MEDIA_CTRL
    ADMIN_PAGES --> PAGE_CTRL
    ADMIN_PROMOTIONS --> PROMOTION_CTRL
    ADMIN_ABOUT --> ABOUT_CTRL
    ADMIN_PARTNERS --> PARTNER_CTRL
    ADMIN_PROJECTS --> PROJECT_CTRL
    ADMIN_SITE_SETTINGS --> SITE_SETTINGS_CTRL
    ADMIN_PAGE_IMAGES --> PAGE_IMG_CTRL
    ADMIN_FRONTPAGE --> FRONTPAGE_CTRL
    ADMIN_VISITS --> VISIT_CTRL
    
    %% Relationships - Controllers to Models
    AUTH_CTRL --> USER
    AUTH_CTRL --> ROLE
    USER_CTRL --> USER
    USER_CTRL --> ROLE
    BANNER_CTRL --> BANNER_MODEL
    NEWS_CTRL --> NEWS_MODEL
    NEWS_CTRL --> NEWS_CAT_MODEL
    PLATFORM_CTRL --> PLATFORM_MODEL
    PLATFORM_CTRL --> PLATFORM_CAT_MODEL
    LINK_CTRL --> LINK_MODEL
    FAQ_CTRL --> FAQ_MODEL
    FAQ_CTRL --> FAQ_CAT_MODEL
    DOWNLOAD_CTRL --> DOWNLOAD_MODEL
    DOWNLOAD_CTRL --> DOWNLOAD_CAT_MODEL
    CONTACT_CTRL --> CONTACT_MODEL
    CONTACT_CTRL --> CONTACT_CAT_MODEL
    ARTICLE_CTRL --> ARTICLE_MODEL
    CATEGORY_CTRL --> CATEGORY_MODEL
    CAT_PERM_CTRL --> CATEGORY_PERMISSION_MODEL
    MEDIA_CTRL --> MEDIA_MODEL
    PAGE_CTRL --> PAGE_MODEL
    DOWNLOAD_CAT_CTRL --> DOWNLOAD_CAT_MODEL
    PROMOTION_CTRL --> PROMOTION_MODEL
    PROMOTION_CTRL --> PROMOTION_CAT_MODEL
    ABOUT_CTRL --> ABOUT_MODEL
    CONTACT_CAT_CTRL --> CONTACT_CAT_MODEL
    SITE_SETTINGS_CTRL --> SITE_SETTINGS_MODEL
    PARTNER_CTRL --> PARTNER_MODEL
    PARTNER_CTRL --> PARTNER_CAT_MODEL
    PROJECT_CTRL --> PROJECT_MODEL
    CONTACT_AGREEMENT_CTRL --> CONTACT_AGREEMENT_MODEL
    PAGE_IMG_CTRL --> PAGE_IMAGE_MODEL
    FRONTPAGE_CTRL --> FRONTPAGE_MODEL
    FRONTPAGE_CTRL --> FRONTPAGE_CAT_MODEL
    VISIT_CTRL --> VISIT_MODEL
    
    %% Middleware Relationships
    ADMIN_AUTH --> AUTH_MID
    BANNER_CTRL --> BANNER_UPLOAD
    NEWS_CTRL --> NEWS_UPLOAD
    PLATFORM_CTRL --> PLATFORM_UPLOAD
    DOWNLOAD_CTRL --> DOWNLOAD_UPLOAD
    PAGE_CTRL --> PAGE_ATTACHMENT_UPLOAD
    PAGE_IMG_CTRL --> PAGE_IMAGE_UPLOAD
    PROMOTION_CTRL --> PROMOTION_UPLOAD
    SITE_SETTINGS_CTRL --> SITE_LOGO_UPLOAD
    LINK_CTRL --> LINK_IMAGE_UPLOAD
    
    %% Permission Relationships
    USER --> ROLE
    USER --> CATEGORY_PERMISSION_MODEL
    CATEGORY_PERMISSION_MODEL --> CATEGORY_MODEL
    ROLE --> ROLE_PERM
    CAT_PERM_CTRL --> CAT_PERM
    AUTH_MID --> PERMISSIONS_CHECK
    
    %% Frontend Routes to Controllers
    HOME --> FRONTEND_CTRL
    HOME --> BANNER_CTRL
    NEWS --> NEWS_CTRL
    NEWS_ITEM --> NEWS_CTRL
    NEWS_CAT --> NEWS_CTRL
    PLATFORMS --> PLATFORM_CTRL
    PLATFORM_ITEM --> PLATFORM_CTRL
    FAQ --> FAQ_CTRL
    DOWNLOADS --> DOWNLOAD_CTRL
    DOWNLOAD_ITEM --> DOWNLOAD_CTRL
    CONTACT --> CONTACT_CTRL
    ABOUT --> ABOUT_CTRL
    PROMOTIONS --> PROMOTION_CTRL
    PROMOTION_ITEM --> PROMOTION_CTRL
    PAGE --> PAGE_CTRL
    ARTICLES --> ARTICLE_CTRL
    ARTICLE --> ARTICLE_CTRL
    CATEGORY --> CATEGORY_CTRL
    PARTNERS --> PARTNER_CTRL
    PROJECTS --> PROJECT_CTRL
    PROJECT_ITEM --> PROJECT_CTRL
    
    %% Multilingual Support
    subgraph Multilingual["**Multilingual Support**"]
        direction TB
        EN[English Content]
        TW[Traditional Chinese Content]
    end
    
    NEWS_MODEL --> EN
    NEWS_MODEL --> TW
    PLATFORM_MODEL --> EN
    PLATFORM_MODEL --> TW
    FAQ_MODEL --> EN
    FAQ_MODEL --> TW
    DOWNLOAD_MODEL --> EN
    DOWNLOAD_MODEL --> TW
    BANNER_MODEL --> EN
    BANNER_MODEL --> TW
    ARTICLE_MODEL --> EN
    ARTICLE_MODEL --> TW
    PAGE_MODEL --> EN
    PAGE_MODEL --> TW
    PROMOTION_MODEL --> EN
    PROMOTION_MODEL --> TW
    ABOUT_MODEL --> EN
    ABOUT_MODEL --> TW
    PARTNER_MODEL --> EN
    PARTNER_MODEL --> TW
    PROJECT_MODEL --> EN
    PROJECT_MODEL --> TW
    
    %% Style Definitions
    classDef controller fill:#f9f,stroke:#333,stroke-width:2px
    classDef model fill:#bbf,stroke:#333,stroke-width:2px
    classDef route fill:#bfb,stroke:#333,stroke-width:2px
    classDef middleware fill:#fbb,stroke:#333,stroke-width:2px
    classDef view fill:#ffb,stroke:#333,stroke-width:2px
    classDef multilingual fill:#ffd,stroke:#333,stroke-width:2px
    classDef permission fill:#ddf,stroke:#333,stroke-width:2px
    classDef auth fill:#fdf,stroke:#333,stroke-width:2px
    
    %% Apply Styles
    class AUTH_CTRL,USER_CTRL,BANNER_CTRL,NEWS_CTRL,PLATFORM_CTRL,LINK_CTRL,FAQ_CTRL,DOWNLOAD_CTRL,CONTACT_CTRL,DASH_CTRL,ARTICLE_CTRL,CATEGORY_CTRL,CAT_PERM_CTRL,MEDIA_CTRL,PAGE_CTRL,PAGE_IMG_CTRL,DOWNLOAD_CAT_CTRL,PROMOTION_CTRL,ABOUT_CTRL,CONTACT_CAT_CTRL,SITE_SETTINGS_CTRL,PARTNER_CTRL,CONTACT_AGREEMENT_CTRL,PROJECT_CTRL,FRONTEND_CTRL,FRONTPAGE_CTRL,VISIT_CTRL controller
    class USER,ROLE,BANNER_MODEL,NEWS_MODEL,NEWS_CAT_MODEL,PLATFORM_MODEL,PLATFORM_CAT_MODEL,LINK_MODEL,FAQ_MODEL,FAQ_CAT_MODEL,DOWNLOAD_MODEL,DOWNLOAD_CAT_MODEL,CONTACT_MODEL,CONTACT_CAT_MODEL,ARTICLE_MODEL,CATEGORY_MODEL,CATEGORY_PERMISSION_MODEL,MEDIA_MODEL,PAGE_MODEL,PAGE_IMAGE_MODEL,PROMOTION_MODEL,PROMOTION_CAT_MODEL,ABOUT_MODEL,SITE_SETTINGS_MODEL,PARTNER_MODEL,PARTNER_CAT_MODEL,PROJECT_MODEL,CONTACT_AGREEMENT_MODEL,FRONTPAGE_MODEL,FRONTPAGE_CAT_MODEL,VISIT_MODEL model
    class ADMIN_AUTH,ADMIN_USERS,ADMIN_BANNERS,ADMIN_NEWS,ADMIN_PLATFORMS,ADMIN_LINKS,ADMIN_FAQ,ADMIN_DOWNLOADS,ADMIN_CONTACT,ADMIN_PAGES,ADMIN_MEDIA,ADMIN_ARTICLES,ADMIN_CATEGORIES,ADMIN_PROMOTIONS,ADMIN_ABOUT,ADMIN_PARTNERS,ADMIN_PROJECTS,ADMIN_SITE_SETTINGS,ADMIN_PAGE_IMAGES,ADMIN_FRONTPAGE,ADMIN_VISITS,HOME,NEWS,NEWS_ITEM,NEWS_CAT,PLATFORMS,PLATFORM_ITEM,FAQ,DOWNLOADS,DOWNLOAD_ITEM,CONTACT,ABOUT,PROMOTIONS,PROMOTION_ITEM,PAGE,ARTICLES,ARTICLE,CATEGORY,PARTNERS,PROJECTS,PROJECT_ITEM route
    class AUTH_MID,UPLOAD,BANNER_UPLOAD,NEWS_UPLOAD,PLATFORM_UPLOAD,DOWNLOAD_UPLOAD,PAGE_ATTACHMENT_UPLOAD,PAGE_IMAGE_UPLOAD,PROMOTION_UPLOAD,SITE_LOGO_UPLOAD,LINK_IMAGE_UPLOAD,LANGUAGE_MID,ERROR middleware
    class LAYOUTS,PAGES,PARTIALS,ADMIN_VIEWS,FRONTEND_VIEWS,BANNER_VIEWS,NEWS_VIEWS,PLATFORM_VIEWS,LINK_VIEWS,FAQ_VIEWS,DOWNLOAD_VIEWS,CONTACT_VIEWS,PAGE_VIEWS,ARTICLE_VIEWS,CATEGORY_VIEWS,USER_VIEWS,MEDIA_VIEWS,PROMOTION_VIEWS,ABOUT_VIEWS,PARTNER_VIEWS,PROJECT_VIEWS,SITE_SETTINGS_VIEWS,FRONTPAGE_VIEWS,VISIT_VIEWS,BANNER_CAROUSEL,NEWS_LIST,NEWS_DETAIL,PLATFORM_LIST,PLATFORM_DETAIL,FAQ_LIST,DOWNLOAD_LIST,CONTACT_FORM,ABOUT_PAGE,PROMOTION_LIST,PROMOTION_DETAIL,ARTICLE_LIST,ARTICLE_DETAIL,DYNAMIC_PAGE,PARTNER_LIST,PROJECT_LIST,PROJECT_DETAIL,NAV,FORMS,LANGUAGE_SWITCHER view
    class EN,TW multilingual
    class ROLE_PERM,CAT_PERM,SUPER_ADMIN,ADMIN_ROLE,EDITOR_ROLE,GUEST_ROLE,VIEW_PERM,CREATE_PERM,EDIT_PERM,DELETE_PERM permission
    class LOGIN,SESSION,PERMISSIONS_CHECK,IS_AUTHENTICATED,HAS_ROLE,HAS_PERMISSION,IS_OWNER_OR_HAS_PERMISSION auth
