generator client {
  provider        = "prisma-client-js"
  previewFeatures = ["metrics"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                  Int                  @id @default(autoincrement())
  username            String               @unique
  email               String?               
  password            String
  isActive            Boolean              @default(true)
  lastLogin           DateTime?
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @updatedAt
  roleId              Int
  contactName_zh      String?
  contactName_en      String?
  contactPhone        String?
  organizationName_zh String?
  organizationName_en String?
  businessId          String?
  projectId           Int?
  aboutItems          AboutItem[]
  banners             Banner[]
  categoryPermissions CategoryPermission[] @relation("categoryPermissions")
  downloads           Download[]
  faqItems            FaqItem[]
  FrontpageItem       FrontpageItem[]
  uploads             Media[]              @relation("uploads")
  newsItems           NewsItem[]
  pageImages          PageImage[]          @relation("pageImageCreator")
  pages               Page[]
  platforms           Platform[]
  promotionItems      PromotionItem[]
  role                Role                 @relation(fields: [roleId], references: [id])
  project             Project?             @relation(fields: [projectId], references: [id])
  articles            Article[]
  partners            Partner[]
  visits              Visit[]

  @@index([roleId], map: "Users_roleId_fkey")
  @@index([projectId], map: "Users_projectId_fkey")
  @@map("Users")
}

model Role {
  id          Int      @id @default(autoincrement())
  name        String   @unique
  description String?
  permissions Json
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  users       User[]

  @@map("Roles")
}

model Article {
  id                 Int              @id @default(autoincrement())
  status             Status           @default(draft)
  publishedAt        DateTime?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  deletedAt          DateTime?
  authorId           Int
  categoryId         Int?
  featuredImage      Int?
  content_en         String           @db.LongText
  content_tw         String           @db.LongText
  excerpt_en         String?          @db.Text
  excerpt_tw         String?          @db.Text
  metaDescription_en String?          @db.Text
  metaDescription_tw String?          @db.Text
  metaKeywords_en    String?
  metaKeywords_tw    String?
  metaTitle_en       String?
  metaTitle_tw       String?
  title_en           String
  title_tw           String
  ArticleToMedia     ArticleToMedia[]
  author             User             @relation(fields: [authorId], references: [id])
  category           Category?        @relation(fields: [categoryId], references: [id])
  featured           Media?           @relation("FeaturedImage", fields: [featuredImage], references: [id])

  @@index([authorId], map: "articles_authorId_fkey")
  @@index([categoryId], map: "articles_categoryId_fkey")
  @@index([featuredImage], map: "articles_featuredImage_fkey")
  @@map("articles")
}

model Category {
  id             Int                  @id @default(autoincrement())
  order          Int                  @default(0)
  createdAt      DateTime             @default(now())
  updatedAt      DateTime             @updatedAt
  deletedAt      DateTime?
  parentId       Int?
  description_en String?              @db.Text
  description_tw String?              @db.Text
  name_en        String
  name_tw        String
  parent         Category?            @relation("CategoryToCategory", fields: [parentId], references: [id])
  children       Category[]           @relation("CategoryToCategory")
  permissions    CategoryPermission[]
  articles       Article[]

  @@index([parentId], map: "Categories_parentId_fkey")
  @@map("Categories")
}

model Media {
  id                 Int              @id @default(autoincrement())
  filename           String
  originalName       String
  mimeType           String
  size               Int
  path               String
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  deletedAt          DateTime?
  uploaderId         Int
  alt_en             String?
  alt_tw             String?
  caption_en         String?          @db.Text
  caption_tw         String?          @db.Text
  uploader           User             @relation("uploads", fields: [uploaderId], references: [id])
  ArticleToMedia     ArticleToMedia[]
  featuredInArticles Article[]        @relation("FeaturedImage")

  @@index([uploaderId], map: "Media_uploaderId_fkey")
  @@map("Media")
}

model CategoryPermission {
  id         Int      @id @default(autoincrement())
  canView    Boolean  @default(true)
  canCreate  Boolean  @default(false)
  canEdit    Boolean  @default(false)
  canDelete  Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  userId     Int
  categoryId Int
  category   Category @relation(fields: [categoryId], references: [id])
  user       User     @relation("categoryPermissions", fields: [userId], references: [id])

  @@unique([userId, categoryId])
  @@index([categoryId], map: "CategoryPermissions_categoryId_fkey")
  @@map("CategoryPermissions")
}

model Banner {
  id               Int      @id @default(autoincrement())
  url              String?
  mediaPath        String
  mediaType        String
  isActive         Boolean  @default(false)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  userId           Int
  description_en   String?  @db.Text
  description_tw   String?  @db.Text
  title_en         String
  title_tw         String
  mediaPathDesktop String?
  mediaPathMobile  String?
  mediaPathTablet  String?
  order            Int      @default(0)
  createdBy        User     @relation(fields: [userId], references: [id])

  @@index([userId], map: "Banners_userId_fkey")
  @@map("Banners")
}

model Page {
  id                 Int              @id @default(autoincrement())
  slug               String           @unique
  status             Status           @default(draft)
  showInNavigation   Boolean          @default(false)
  navigationOrder    Int?
  publishedAt        DateTime?
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  deletedAt          DateTime?
  authorId           Int
  editorMode         String           @default("editor")
  content_en         String           @db.LongText
  content_tw         String           @db.LongText
  metaDescription_en String?          @db.Text
  metaDescription_tw String?          @db.Text
  metaKeywords_en    String?
  metaKeywords_tw    String?
  metaTitle_en       String?
  metaTitle_tw       String?
  title_en           String
  title_tw           String
  attachments        PageAttachment[]
  author             User             @relation(fields: [authorId], references: [id])

  @@index([authorId], map: "Pages_authorId_fkey")
  @@map("Pages")
}

model PageAttachment {
  id           Int      @id @default(autoincrement())
  filename     String
  originalName String
  mimeType     String
  size         Int
  path         String
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  pageId       Int
  page         Page     @relation(fields: [pageId], references: [id], onDelete: Cascade)

  @@index([pageId], map: "PageAttachments_pageId_fkey")
  @@map("PageAttachments")
}

model PageImage {
  id                  Int      @id @default(autoincrement())
  filename            String
  originalName        String
  path                String
  targetPage          String
  isActive            Boolean  @default(true)
  createdAt           DateTime @default(now())
  updatedAt           DateTime @updatedAt
  userId              Int
  filenameDesktop     String?
  filenameMobile      String?
  filenameTablet      String?
  originalNameDesktop String?
  originalNameMobile  String?
  originalNameTablet  String?
  pathDesktop         String?
  pathMobile          String?
  pathTablet          String?
  createdBy           User     @relation("pageImageCreator", fields: [userId], references: [id])

  @@index([userId], map: "PageImages_userId_fkey")
  @@map("PageImages")
}

model FaqCategory {
  id             Int       @id @default(autoincrement())
  slug           String    @unique
  order          Int       @default(0)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  deletedAt      DateTime?
  description_en String?   @db.Text
  description_tw String?   @db.Text
  name_en        String
  name_tw        String
  faqItems       FaqItem[]

  @@map("FaqCategories")
}

model FaqItem {
  id         Int         @id @default(autoincrement())
  order      Int         @default(0)
  status     Status      @default(draft)
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt
  deletedAt  DateTime?
  categoryId Int
  authorId   Int
  content_en String      @db.LongText
  content_tw String      @db.LongText
  title_en   String
  title_tw   String
  author     User        @relation(fields: [authorId], references: [id])
  category   FaqCategory @relation(fields: [categoryId], references: [id])

  @@index([authorId], map: "FaqItems_authorId_fkey")
  @@index([categoryId], map: "FaqItems_categoryId_fkey")
  @@map("FaqItems")
}

model DownloadCategory {
  id             Int        @id @default(autoincrement())
  order          Int        @default(0)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  deletedAt      DateTime?
  description_en String?    @db.Text
  description_tw String?    @db.Text
  name_en        String
  name_tw        String
  downloads      Download[]
  groups         DownloadGroup[]

  @@map("DownloadCategories")
}

model DownloadGroup {
  id             Int               @id @default(autoincrement())
  order          Int               @default(0)
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  deletedAt      DateTime?
  description_en String?           @db.Text
  description_tw String?           @db.Text
  name_en        String
  name_tw        String
  categoryId     Int
  category       DownloadCategory  @relation(fields: [categoryId], references: [id])
  downloads      Download[]

  @@index([categoryId], map: "DownloadGroups_categoryId_fkey")
  @@map("DownloadGroups")
}

model Download {
  id             Int               @id @default(autoincrement())
  filename       String
  originalName   String
  mimeType       String
  size           Int
  path           String
  status         Status            @default(draft)
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt
  deletedAt      DateTime?
  authorId       Int
  categoryId     Int?
  groupId        Int?
  downloadCount  Int               @default(0)
  description_en String?           @db.Text
  description_tw String?           @db.Text
  keywords_en    String?
  keywords_tw    String?
  title_en       String
  title_tw       String
  group_name_en  String?
  group_name_tw  String?
  author         User              @relation(fields: [authorId], references: [id])
  category       DownloadCategory? @relation(fields: [categoryId], references: [id])
  group          DownloadGroup?    @relation(fields: [groupId], references: [id])

  @@index([authorId], map: "Downloads_authorId_fkey")
  @@index([categoryId], map: "Downloads_categoryId_fkey")
  @@index([groupId], map: "Downloads_groupId_fkey")
  @@map("Downloads")
}

model NewsCategory {
  id             Int        @id @default(autoincrement())
  name_en        String
  name_tw        String
  slug           String     @unique
  description_en String?    @db.Text
  description_tw String?    @db.Text
  order          Int        @default(0)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  deletedAt      DateTime?
  newsItems      NewsItem[]

  @@map("NewsCategories")
}

model NewsItem {
  id            Int          @id @default(autoincrement())
  title_en      String
  title_tw      String
  summary_en    String?      @db.Text
  summary_tw    String?      @db.Text
  url           String?
  imagePath     String?
  publishedDate DateTime
  status        Status       @default(draft)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt
  deletedAt     DateTime?
  categoryId    Int
  authorId      Int
  slug          String
  author        User         @relation(fields: [authorId], references: [id])
  category      NewsCategory @relation(fields: [categoryId], references: [id])

  @@index([authorId], map: "NewsItems_authorId_fkey")
  @@index([categoryId], map: "NewsItems_categoryId_fkey")
  @@map("NewsItems")
}

model PromotionCategory {
  id             Int             @id @default(autoincrement())
  name_en        String
  name_tw        String
  slug           String          @unique
  description_en String?         @db.Text
  description_tw String?         @db.Text
  order          Int             @default(0)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  deletedAt      DateTime?
  promotionItems PromotionItem[]

  @@map("PromotionCategories")
}

model PromotionItem {
  id            Int               @id @default(autoincrement())
  title_en      String
  title_tw      String
  content_en    String            @db.LongText
  content_tw    String            @db.LongText
  slug          String            @unique
  url           String?
  urlEn         String?
  imagePath     String?
  imagePathEn   String?
  publishedDate DateTime
  status        Status            @default(draft)
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @updatedAt
  deletedAt     DateTime?
  categoryId    Int
  authorId      Int
  author        User              @relation(fields: [authorId], references: [id])
  category      PromotionCategory @relation(fields: [categoryId], references: [id])

  @@index([authorId], map: "PromotionItems_authorId_fkey")
  @@index([categoryId], map: "PromotionItems_categoryId_fkey")
  @@map("PromotionItems")
}

model AboutItem {
  id               Int       @id @default(autoincrement())
  title_en         String
  title_tw         String
  type             String
  content_en       String    @db.LongText
  content_tw       String    @db.LongText
  imagePath        String?
  order            Int       @default(0)
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  deletedAt        DateTime?
  authorId         Int?
  imagePathDesktop String?
  imagePathMobile  String?
  imagePathTablet  String?
  imagePathDesktopEn String?
  imagePathMobileEn  String?
  imagePathTabletEn  String?
  alt_en           String?
  alt_tw           String?
  author           User?     @relation(fields: [authorId], references: [id])

  @@index([authorId], map: "AboutItems_authorId_fkey")
  @@map("AboutItems")
}

model ContactCategory {
  id        Int       @id @default(autoincrement())
  name_en   String
  name_tw   String
  order     Int       @default(0)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deletedAt DateTime?
  contacts  Contact[]

  @@map("ContactCategories")
}

model ContactConsent {
  id          Int      @id @default(autoincrement())
  content_en  String   @db.LongText
  content_tw  String   @db.LongText
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("ContactConsents")
}

model ContactUsAgreement {
  id         Int      @id @default(autoincrement())
  content_en String   @db.LongText
  content_tw String   @db.LongText
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@map("ContactUsAgreements")
}

model Contact {
  id         Int              @id @default(autoincrement())
  name       String
  email      String
  company    String?
  phone      String?
  message    String           @db.Text
  categoryId Int?
  agreeTerms Boolean          @default(false)
  status     String           @default("pending")
  createdAt  DateTime         @default(now())
  updatedAt  DateTime         @updatedAt
  deletedAt  DateTime?
  category   ContactCategory? @relation(fields: [categoryId], references: [id])

  @@index([categoryId], map: "Contacts_categoryId_fkey")
  @@map("Contacts")
}

model Link {
  id        Int      @id @default(autoincrement())
  title_en  String
  title_tw  String
  url       String
  image     String?
  order     Int      @default(0)
  active    Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("Links")
}

model PlatformCategory {
  id             Int        @id @default(autoincrement())
  name_en        String
  name_tw        String
  description_en String?
  description_tw String?
  slug           String     @unique
  order          Int        @default(0)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  deletedAt      DateTime?
  platforms      Platform[]

  @@map("PlatformCategories")
}

model Platform {
  id            Int                  @id @default(autoincrement())
  title_en      String               @db.VarChar(255)
  title_tw      String               @db.VarChar(255)
  content_en    String?              @db.LongText
  content_tw    String?              @db.LongText
  slug          String               @db.VarChar(255)
  url           String?              @db.VarChar(255)
  urlEn         String?              @db.VarChar(255)
  imagePath     String?              @db.VarChar(255)
  imagePathEn   String?              @db.VarChar(255)
  alt_en        String?
  alt_tw        String?
  publishedDate DateTime             @default(now())
  status        Status               @default(draft)
  createdAt     DateTime             @default(now())
  updatedAt     DateTime             @updatedAt
  deletedAt     DateTime?
  categoryId    Int?
  authorId      Int?
  order         Int                  @default(0)
  type          String               @default("plain_text")
  partnersData  String?              @db.Text
  attachments   PlatformAttachment[]
  author        User?                @relation(fields: [authorId], references: [id])
  category      PlatformCategory?    @relation(fields: [categoryId], references: [id])

  @@index([authorId], map: "Platforms_authorId_fkey")
  @@index([categoryId], map: "Platforms_categoryId_fkey")
  @@map("Platforms")
}

model PlatformAttachment {
  id                 Int       @id @default(autoincrement())
  filename           String
  originalName       String
  mimeType           String
  size               Int
  path               String
  title_en           String?
  title_tw           String?
  description_en     String?   @db.Text
  description_tw     String?   @db.Text
  createdAt          DateTime  @default(now())
  updatedAt          DateTime  @updatedAt
  platformId         Int
  deletedAt          DateTime?
  attachment_name_en String?
  attachment_name_tw String?
  platform           Platform  @relation(fields: [platformId], references: [id], onDelete: Cascade)

  @@index([platformId], map: "PlatformAttachments_platformId_fkey")
  @@map("PlatformAttachments")
}

model FrontpageCategory {
  id             Int             @id @default(autoincrement())
  name_tw        String
  order          Int             @default(0)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  deletedAt      DateTime?
  frontpageItems FrontpageItem[]

  @@map("FrontpageCategories")
}

model FrontpageItem {
  id         Int                @id @default(autoincrement())
  title_tw   String
  title_en   String
  content_tw String?            @db.LongText
  content_en String?            @db.LongText
  type       String
  order      Int                @default(0)
  status     Status             @default(draft)
  createdAt  DateTime           @default(now())
  updatedAt  DateTime           @updatedAt
  deletedAt  DateTime?
  categoryId Int?
  authorId   Int
  images     FrontpageImage[]
  author     User               @relation(fields: [authorId], references: [id])
  category   FrontpageCategory? @relation(fields: [categoryId], references: [id])

  @@index([authorId], map: "FrontpageItems_authorId_fkey")
  @@index([categoryId], map: "FrontpageItems_categoryId_fkey")
  @@map("FrontpageItems")
}

model FrontpageImage {
  id           Int           @id @default(autoincrement())
  filename     String
  originalName String
  path         String
  order        Int           @default(0)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  itemId       Int
  alt          String?
  url          String?
  language     String?
  item         FrontpageItem @relation(fields: [itemId], references: [id], onDelete: Cascade)

  @@index([itemId], map: "FrontpageImages_itemId_fkey")
  @@map("FrontpageImages")
}

model VisitCounter {
  id        Int      @id @default(autoincrement())
  count     Int      @default(0)
  updatedAt DateTime @updatedAt
}

model VisitCounterHistory {
  id         Int      @id @default(autoincrement())
  count      Int      @default(0)
  date       DateTime @default(now())
  createdAt  DateTime @default(now())
  lastReset  DateTime @default(now())
  todayCount Int      @default(0)
}

model ArticleToMedia {
  A        Int
  B        Int
  articles Article @relation(fields: [A], references: [id], onDelete: Cascade)
  Media    Media   @relation(fields: [B], references: [id], onDelete: Cascade)

  @@unique([A, B], map: "_ArticleToMedia_AB_unique")
  @@index([B], map: "_ArticleToMedia_B_index")
  @@map("_ArticleToMedia")
}

model SiteSettings {
  id                Int      @id @default(autoincrement())
  site_name_en      String
  site_name_tw      String
  site_url          String
  logo_desktop_path String?
  logo_tablet_path  String?
  logo_mobile_path  String?
  logo_alt_en       String?
  logo_alt_tw       String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("site_settings")
}

enum Status {
  draft
  published
  archived
}

model PartnersCategory {
  id             Int        @id @default(autoincrement())
  name_en        String
  name_tw        String
  description_en String?
  description_tw String?
  slug           String     @unique
  order          Int        @default(0)
  createdAt      DateTime   @default(now())
  updatedAt      DateTime   @updatedAt
  deletedAt      DateTime?
  partners       Partner[]

  @@map("PartnersCategories")
}

model Partner {
  id            Int                @id @default(autoincrement())
  title_en      String             @db.VarChar(255)
  title_tw      String             @db.VarChar(255)
  slug          String             @db.VarChar(255)
  publishedDate DateTime           @default(now())
  status        Status             @default(draft)
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  deletedAt     DateTime?
  categoryId    Int?
  authorId      Int?
  order         Int                @default(0)
  url           String?            @db.VarChar(255)
  suppliers_en  String?            @db.Text
  suppliers_tw  String?            @db.Text
  buyers_en     String?            @db.Text
  buyers_tw     String?            @db.Text
  author        User?              @relation(fields: [authorId], references: [id])
  category      PartnersCategory?  @relation(fields: [categoryId], references: [id])

  @@index([authorId], map: "Partners_authorId_fkey")
  @@index([categoryId], map: "Partners_categoryId_fkey")
  @@map("Partners")
}

model Project {
  id          Int      @id @default(autoincrement())
  name_zh     String   @unique
  name_en     String?
  description String?  @db.Text
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  users       User[]
  visits      Visit[]  

  @@map("Projects")
}

model Visit {
  id          Int       @id @default(autoincrement())
  businessId  String    // 統一編號
  companyName String?    // 廠商名稱
  contactPerson String?   // 訪談對象
  address     String    // 公司地址
  visitDate   DateTime  // 訪談日期
  visitTime   String    // 訪談時間
  projectManager   String?   // 本案負責人
  projectManagerPhone String?   // 負責人電話
  notes       String    @db.LongText // 訪談記錄
  status      String    @default("待安排") // 狀態: 待安排, 已安排, 完成拜訪
  mouStatus   Int       @default(0) 
  statusNotes String?   @db.Text // 狀態備註
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  createdById Int       // 創建者ID
  projectId   Int?      // 分項計畫ID
  
  createdBy   User      @relation(fields: [createdById], references: [id])
  project     Project?  @relation(fields: [projectId], references: [id])

  @@index([createdById], map: "Visits_createdById_fkey")
  @@index([projectId], map: "Visits_projectId_fkey")
  @@map("Visits")
}
