const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const logger = require('../src/config/logger');

const prisma = new PrismaClient();

async function main() {
    try {
        // Create super admin role with all permissions
        const superAdminRole = await prisma.role.upsert({
            where: { name: 'super_admin' },
            update: {
                description: 'Super Administrator',
                permissions: ['*']
            },
            create: {
                name: 'super_admin',
                description: 'Super Administrator',
                permissions: ['*']
            }
        });

        // Create admin role with standard permissions
        const adminRole = await prisma.role.upsert({
            where: { name: 'admin' },
            update: {
                description: 'Administrator',
                permissions: [
                    'access:dashboard',
                    'manage_articles',
                    'manage_categories',
                    'manage_media',
                    'manage_banners',
                    'manage_pages',
                    'manage_faq_categories',
                    'manage_download_categories',
                    'manage_promotions',
                    'manage_about',
                    'manage_contact_categories',
                    'manage_contact',
                    'manage_platforms',
                    'manage_partners',
                    'manage_links',
                    'manage_frontpage',
                    'manage_page_images',
                    'manage_projects',
                    'manage_site_settings',
                ]
            },
            create: {
                name: 'admin',
                description: 'Administrator',
                permissions: [
                    'access:dashboard',
                    'manage_articles',
                    'manage_categories',
                    'manage_media',
                    'manage_banners',
                    'manage_pages',
                    'manage_faq_categories',
                    'manage_download_categories',
                    'manage_promotions',
                    'manage_about',
                    'manage_contact_categories',
                    'manage_contact',
                    'manage_platforms',
                    'manage_partners',
                    'manage_links',
                    'manage_frontpage',
                    'manage_page_images',
                    'manage_projects',
                    'manage_site_settings',
                ]
            }
        });

        // Create editor role
        const editorRole = await prisma.role.upsert({
            where: { name: 'editor' },
            update: {
                description: 'Content Editor',
                permissions: [
                    'access:dashboard',
                    'manage_faq_items',
                    'manage_downloads',
                    'manage_news_items',
                    'user:edit_own'
                ]
            },
            create: {
                name: 'editor',
                description: 'Content Editor',
                permissions: [
                    'access:dashboard',
                    'manage_faq_items',
                    'manage_downloads',
                    'manage_news_items',
                    'user:edit_own'
                ]
            }
        });

        // Create guest role with limited permissions
        const guestRole = await prisma.role.upsert({
            where: { name: 'guest' },
            update: {
                description: 'Guest user with access only to visits page',
                permissions: [
                    'access:dashboard',
                    'view:visits',
                    'user:edit_own'
                ]
            },
            create: {
                name: 'guest',
                description: 'Guest user with access only to visits page',
                permissions: [
                    'access:dashboard',
                    'view:visits',
                    'user:edit_own'
                ]
            }
        });

        // Create visitor role with read-only permissions
        const visitorRole = await prisma.role.upsert({
            where: { name: 'visitor' },
            update: {
                description: 'Visitor user with read-only access to visits page',
                permissions: [
                    'access:dashboard',
                    'view:visits'
                ]
            },
            create: {
                name: 'visitor',
                description: 'Visitor user with read-only access to visits page',
                permissions: [
                    'access:dashboard',
                    'view:visits'
                ]
            }
        });

        // Create super admin user
        const hashedPassword = await bcrypt.hash('admin', 10);
        const superAdminUser = await prisma.user.upsert({
            where: { username: 'admin' },
            update: {
                roleId: superAdminRole.id
            },
            create: {
                username: 'admin',
                email: '<EMAIL>',
                password: hashedPassword,
                roleId: superAdminRole.id,
                isActive: true
            }
        });

        // Create editor user
        const hashedEditorPassword = await bcrypt.hash('editor', 10);
        const editorUser = await prisma.user.upsert({
            where: { username: 'editor' },
            update: {
                roleId: editorRole.id
            },
            create: {
                username: 'editor',
                email: '<EMAIL>',
                password: hashedEditorPassword,
                roleId: editorRole.id,
                isActive: true
            }
        });

        // Create guest user
        const hashedGuestPassword = await bcrypt.hash('guest', 10);
        const guestUser = await prisma.user.upsert({
            where: { username: 'guest_user' },
            update: {
                roleId: guestRole.id
            },
            create: {
                username: 'guest_user',
                email: '<EMAIL>',
                password: hashedGuestPassword,
                roleId: guestRole.id,
                contactName_zh: '訪客用戶',
                contactName_en: 'Guest User',
                contactPhone: '',
                isActive: true
            }
        });

        // Create visitor user (read-only access)
        const hashedVisitorPassword = await bcrypt.hash('visitor', 10);
        const visitorUser = await prisma.user.upsert({
            where: { username: 'visitor' },
            update: {
                roleId: visitorRole.id,
                isActive: true
            },
            create: {
                username: 'visitor',
                email: '<EMAIL>',
                password: hashedVisitorPassword,
                roleId: visitorRole.id,
                contactName_zh: '僅限查看訪客',
                contactName_en: 'Visitor (Read-only)',
                isActive: true
            }
        });

        // Create default category
        // First check if the category exists
        let defaultCategory = await prisma.category.findFirst({
            where: { name_en: 'Uncategorized' }
        });

        // If it doesn't exist, create it
        if (!defaultCategory) {
            defaultCategory = await prisma.category.create({
                data: {
                    name_en: 'Uncategorized',
                    name_tw: '未分類',
                    description_en: 'Default category for uncategorized content',
                    description_tw: '未分類內容的預設類別',
                    order: 0
                }
            });
        }

        // Create default pages
        const defaultPages = [
            {
                slug: 'website-security-policy',
                title_en: 'Website Security Policy',
                title_tw: '網站安全政策',
                content_en: 'TBD',
                content_tw: 'TBD',
                status: 'published',
                showInNavigation: true,
                navigationOrder: 1,
                authorId: superAdminUser.id
            },
            {
                slug: 'privacy-policy',
                title_en: 'Privacy Policy',
                title_tw: '隱私政策',
                content_en: 'TBD',
                content_tw: 'TBD',
                status: 'published',
                showInNavigation: true,
                navigationOrder: 2,
                authorId: superAdminUser.id
            },
            {
                slug: 'government-open-data-declaration',
                title_en: 'Government Open Data Declaration',
                title_tw: '政府網站資料開放宣告',
                content_en: 'TBD',
                content_tw: 'TBD',
                status: 'published',
                showInNavigation: true,
                navigationOrder: 3,
                authorId: superAdminUser.id
            }
        ];

        const createdPages = [];
        const createdProjects = [];

        // Create or update the default pages
        for (const pageData of defaultPages) {
            const page = await prisma.page.upsert({
                where: { slug: pageData.slug },
                update: pageData,
                create: pageData
            });
            createdPages.push(page);
        }

        // Create default projects if they don't exist
        const defaultProjects = [
            {
                name_zh: '台灣智慧系統整合製造平台',
                name_en: 'Taiwan Smart System Integration Manufacturing Platform',
                isActive: true
            },
            {
                name_zh: '智慧雨林產業創生',
                name_en: 'Smart Rainforest Industry Creation',
                isActive: true
            }
        ];

        for (const projectData of defaultProjects) {
            const project = await prisma.project.upsert({
                where: { name_zh: projectData.name_zh },
                update: projectData,
                create: projectData
            });
            createdProjects.push(project);
        }

        // Update guest user with first project if not already associated
        if (!guestUser.projectId) {
            if (createdProjects.length > 0) {
                await prisma.user.update({
                    where: { id: guestUser.id },
                    data: { projectId: createdProjects[0].id }
                });
            }
        }

        logger.info('Database seeded successfully');
        console.log({
            superAdminRole,
            adminRole,
            editorRole,
            guestRole,
            visitorRole,
            superAdminUser,
            editorUser,
            guestUser,
            visitorUser,
            defaultCategory,
            createdPages,
            createdProjects
        });
    } catch (error) {
        logger.error('Error seeding database:', error);
        throw error;
    } finally {
        await prisma.$disconnect();
    }
}

main()
    .catch((e) => {
        console.error(e);
        process.exit(1);
    });
