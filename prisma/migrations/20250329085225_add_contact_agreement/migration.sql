-- CreateTable
CREATE TABLE `ContactConsents` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `content_en` LONGTEXT NOT NULL,
    `content_tw` LONGTEXT NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ContactUsAgreements` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `content_en` LONGTEXT NOT NULL,
    `content_tw` LONGTEXT NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
