-- First, let's create a backup of the current column values
ALTER TABLE `Visits` ADD COLUMN `mouStatus_backup` VARCHAR(191);
UPDATE `Visits` SET `mouStatus_backup` = `mouStatus`;

-- Next, set initial values to 0 to make sure we have valid integer data
ALTER TABLE `Visits` MODIFY `mouStatus` INT NULL DEFAULT 0;
UPDATE `Visits` SET `mouStatus` = 0;

-- Now update based on the backup values
UPDATE `Visits` SET `mouStatus` = 1 WHERE `mouStatus_backup` = '已簽約';

-- Make sure the column is properly defined with a non-null constraint
ALTER TABLE `Visits` MODIFY `mouStatus` INT NOT NULL DEFAULT 0;

-- Drop the backup column
ALTER TABLE `Visits` DROP COLUMN `mouStatus_backup`; 