-- Convert existing values
UPDATE `Visits` SET `mouStatus` = '0' WHERE `mouStatus` = '未簽約' OR `mouStatus` IS NULL;
UPDATE `Visits` SET `mouStatus` = '1' WHERE `mouStatus` = '已簽約';

-- Add a temporary column
ALTER TABLE `Visits` ADD COLUMN `mouStatus_int` INT DEFAULT 0;

-- Copy the converted values (MySQL will convert string '0' to integer 0)
UPDATE `Visits` SET `mouStatus_int` = `mouStatus`;

-- Drop the original column
ALTER TABLE `Visits` DROP COLUMN `mouStatus`;

-- Rename the new column to the original name
ALTER TABLE `Visits` CHANGE `mouStatus_int` `mouStatus` INT DEFAULT 0; 