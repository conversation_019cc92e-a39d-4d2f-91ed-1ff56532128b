# MOU Status Migration Guide (2025-04-19)

This document outlines the steps needed to migrate the `mouStatus` field in the `Visits` table from a `String` type to an `Int` type in the production environment.

## Background

We've changed the `mouStatus` field in the Prisma schema:
- From: `String?` with default value `"未簽約"`
- To: `Int` with default value `0`

This represents:
- `0` = "未用印" (previously "未簽約")
- `1` = "已用印" (previously "已簽約")

## Migration Steps

1. **Backup your database** (always do this before migrations):
   ```bash
   mysqldump -u [username] -p [database_name] > backup_before_migration.sql
   ```

2. **Create a SQL conversion script**:
   Create a file named `convert_mou_status.sql` with the following content:
   
   ```sql
   -- Convert existing values
   UPDATE `Visits` SET `mouStatus` = '0' WHERE `mouStatus` = '未簽約' OR `mouStatus` IS NULL;
   UPDATE `Visits` SET `mouStatus` = '1' WHERE `mouStatus` = '已簽約';

   -- Add a temporary column
   ALTER TABLE `Visits` ADD COLUMN `mouStatus_int` INT DEFAULT 0;

   -- Copy the converted values (MySQL will convert string '0' to integer 0)
   UPDATE `Visits` SET `mouStatus_int` = `mouStatus`;

   -- Drop the original column
   ALTER TABLE `Visits` DROP COLUMN `mouStatus`;

   -- Rename the new column to the original name
   ALTER TABLE `Visits` CHANGE `mouStatus_int` `mouStatus` INT DEFAULT 0;
   ```

3. **Deploy your code** with the updated Prisma schema to production

4. **Execute the conversion script** in your production database:
   ```bash
   mysql -u [username] -p [database_name] < convert_mou_status.sql
   ```
   
   Alternatively, if you use Prisma CLI:
   ```bash
   npx prisma db execute --schema=prisma/schema.prisma --file=convert_mou_status.sql
   ```

5. **Mark the migration as applied** in Prisma's migration history:
   ```bash
   npx prisma migrate resolve --applied 20250419153732_change_mou_status_to_int
   ```

6. **Verify the migration**:
   ```bash
   npx prisma migrate status
   ```
   The output should show that the database schema is up to date.

## Rollback Plan

If something goes wrong during the migration:

1. Restore the database from the backup:
   ```bash
   mysql -u [username] -p [database_name] < backup_before_migration.sql
   ```

2. Revert the Prisma schema changes and redeploy the previous version of your code.

## UI Changes

The UI forms have been updated to display:
- "未用印" instead of "未簽約"
- "已用印" instead of "已簽約"

And the values sent to the server are now:
- `0` instead of `"未簽約"`
- `1` instead of `"已簽約"` 