/*
  Warnings:

  - You are about to drop the column `projectName` on the `Users` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `Users` DROP COLUMN `projectName`,
    ADD COLUMN `projectId` INTEGER NULL;

-- CreateTable
CREATE TABLE `Projects` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `name_zh` VARCHAR(191) NOT NULL,
    `name_en` VARCHAR(191) NULL,
    `description` TEXT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT true,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `Users_projectId_fkey` ON `Users`(`projectId`);

-- Add<PERSON><PERSON>ignKey
ALTER TABLE `Users` ADD CONSTRAINT `Users_projectId_fkey` FOREIGN KEY (`projectId`) REFERENCES `Projects`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
