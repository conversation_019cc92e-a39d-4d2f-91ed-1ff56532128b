-- AlterTable
ALTER TABLE `Downloads` ADD COLUMN `groupId` INTEGER NULL;

-- CreateTable
CREATE TABLE `DownloadGroups` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `order` INTEGER NOT NULL DEFAULT 0,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deletedAt` DATETIME(3) NULL,
    `description_en` TEXT NULL,
    `description_tw` TEXT NULL,
    `name_en` VARCHAR(191) NOT NULL,
    `name_tw` VARCHAR(191) NOT NULL,
    `categoryId` INTEGER NOT NULL,

    INDEX `DownloadGroups_categoryId_fkey`(`categoryId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateIndex
CREATE INDEX `Downloads_groupId_fkey` ON `Downloads`(`groupId`);

-- AddForeignKey
ALTER TABLE `DownloadGroups` ADD CONSTRAINT `DownloadGroups_categoryId_fkey` FOREIGN KEY (`categoryId`) REFERENCES `DownloadCategories`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Downloads` ADD CONSTRAINT `Downloads_groupId_fkey` FOREIGN KEY (`groupId`) REFERENCES `DownloadGroups`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
