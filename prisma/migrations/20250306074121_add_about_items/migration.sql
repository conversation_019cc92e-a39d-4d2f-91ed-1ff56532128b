-- CreateTable
CREATE TABLE `AboutItems` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `title_en` VARCHAR(191) NOT NULL,
    `title_tw` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `content_en` TEXT NOT NULL,
    `content_tw` TEXT NOT NULL,
    `imagePath` VARCHAR(191) NULL,
    `order` INTEGER NOT NULL DEFAULT 0,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deletedAt` DATETIME(3) NULL,
    `authorId` INTEGER NULL,

    INDEX `AboutItems_authorId_fkey`(`authorId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddF<PERSON>ignKey
ALTER TABLE `AboutItems` ADD CONSTRAINT `AboutItems_authorId_fkey` FOREIGN KEY (`authorId`) REFERENCES `Users`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
