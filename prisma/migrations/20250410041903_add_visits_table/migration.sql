-- CreateTable
CREATE TABLE `Visits` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `businessId` VARCHAR(191) NOT NULL,
    `address` VARCHAR(191) NOT NULL,
    `visitDate` DATETIME(3) NOT NULL,
    `visitTime` VARCHAR(191) NOT NULL,
    `attendees` VARCHAR(191) NULL,
    `notes` LONGTEXT NOT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT '待安排',
    `statusNotes` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `createdById` INTEGER NOT NULL,
    `projectId` INTEGER NULL,

    INDEX `Visits_createdById_fkey`(`createdById`),
    INDEX `Visits_projectId_fkey`(`projectId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Visits` ADD CONSTRAINT `Visits_createdById_fkey` FOREIGN KEY (`createdById`) REFERENCES `Users`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Visits` ADD CONSTRAINT `Visits_projectId_fkey` FOREIGN KEY (`projectId`) REFERENCES `Projects`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
