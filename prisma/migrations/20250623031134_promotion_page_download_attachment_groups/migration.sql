-- CreateTable
CREATE TABLE `PromotionGroups` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `order` INTEGER NOT NULL DEFAULT 0,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `deletedAt` DATETIME(3) NULL,
    `description_en` TEXT NULL,
    `description_tw` TEXT NULL,
    `name_en` VARCHAR(191) NOT NULL,
    `name_tw` VARCHAR(191) NOT NULL,
    `categoryId` INTEGER NOT NULL,

    INDEX `PromotionGroups_categoryId_fkey`(`categoryId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PromotionAttachments` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `filename` VARCHAR(191) NOT NULL,
    `originalName` VARCHAR(191) NOT NULL,
    `mimeType` VARCHAR(191) NOT NULL,
    `size` INTEGER NOT NULL,
    `path` VARCHAR(191) NOT NULL,
    `title_en` VARCHAR(191) NULL,
    `title_tw` VARCHAR(191) NULL,
    `description_en` TEXT NULL,
    `description_tw` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `groupId` INTEGER NOT NULL,
    `deletedAt` DATETIME(3) NULL,
    `attachment_name_en` VARCHAR(191) NULL,
    `attachment_name_tw` VARCHAR(191) NULL,

    INDEX `PromotionAttachments_groupId_fkey`(`groupId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `PromotionGroups` ADD CONSTRAINT `PromotionGroups_categoryId_fkey` FOREIGN KEY (`categoryId`) REFERENCES `PromotionCategories`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `PromotionAttachments` ADD CONSTRAINT `PromotionAttachments_groupId_fkey` FOREIGN KEY (`groupId`) REFERENCES `PromotionGroups`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
