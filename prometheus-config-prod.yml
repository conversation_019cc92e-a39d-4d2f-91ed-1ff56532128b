global:
  scrape_interval: 60s
  evaluation_interval: 60s

# Remote write configuration
remote_write:
  - url: "http://tianyen-service.com:8428/api/v1/write"
    write_relabel_configs:
      - source_labels: [__name__]
        regex: 'up|http_requests_total|http_request_duration_seconds.*|nodejs_memory_usage_bytes|nodejs_eventloop_lag.*|nodejs_gc_duration_seconds.*|database_.*|http_response_time.*|http_.*_requests_total|prisma_.*|node_.*'
        action: keep

scrape_configs:
  # Node exporter metrics
  - job_name: 'node-exporter'
    scrape_interval: 5s
    static_configs:
      - targets: ['node-exporter:9100']
        labels:
          service: 'stsvpo-server'
          environment: 'production'
          hostname: 'www.stsvpo.org.tw'

  # Express app metrics
  - job_name: 'stsvpo-server'
    scrape_interval: 10s
    metrics_path: /metrics
    basic_auth:
      username: 'prometheus'
      password: 'prometheus123'
    scheme: http
    static_configs:
      - targets: ['host.docker.internal:3000']
        labels:
          service: 'stsvpo-server'
          environment: 'production' 