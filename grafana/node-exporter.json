{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Basic overview of linux host metrics, based on node_exporter", "editable": false, "fiscalYearStartMonth": 0, "graphTooltip": 1, "id": 12, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 42, "panels": [], "title": "Host Overview", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "description": "Time since last boot", "fieldConfig": {"defaults": {"decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "nullValueMode": "connected", "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 0, "y": 1}, "id": 6, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"]}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "node_time_seconds{hostname=~\"$host\"} - node_boot_time_seconds{hostname=~\"$host\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Uptime | ${hostname}", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "description": "Number of processors", "fieldConfig": {"defaults": {"mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "nullValueMode": "connected", "unit": "none"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 2, "y": 1}, "id": 2, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"]}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "count(count(node_cpu_seconds_total{hostname=~\"$host\"}) by (cpu))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Processors", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "description": "Amount of memory", "fieldConfig": {"defaults": {"decimals": 0, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "nullValueMode": "connected", "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 4, "y": 1}, "id": 4, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "none", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"]}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "node_memory_MemTotal_bytes{hostname=~\"$host\"}", "format": "time_series", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "RAM", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 1, "min": 0, "nullValueMode": "connected", "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0.6}, {"color": "#d44a3a", "value": 0.8}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 1}, "id": 34, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "/^Value$/"}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "1 - avg(irate(node_cpu_seconds_total{mode=\"idle\",hostname=~\"$host\"}[5m]))", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "CPU Load | ${hostname}", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 1, "min": 0, "nullValueMode": "connected", "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0.6}, {"color": "#d44a3a", "value": 0.8}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 1}, "id": 35, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"]}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "1 - node_memory_MemFree_bytes{hostname=~\"$host\"} / node_memory_MemTotal_bytes{hostname=~\"$host\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Memory | ${hostname}", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "description": "Free diskspace", "fieldConfig": {"defaults": {"decimals": 1, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "max": 1, "min": 0, "nullValueMode": "connected", "thresholds": {"mode": "absolute", "steps": [{"color": "#299c46", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0.6}, {"color": "#d44a3a", "value": 0.8}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 1}, "id": 8, "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"]}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "1 - (sum(node_filesystem_free_bytes{hostname=~\"$host\"}) / sum(node_filesystem_size_bytes{hostname=~\"$host\"}))", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Disk Free (Total)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "description": "Network traffic in the last hour", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "nullValueMode": "connected", "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 15, "y": 1}, "id": 10, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"]}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "sum(increase(node_network_receive_bytes_total{hostname=~\"$host\"}[24h]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Net IN (24h)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "description": "Network traffic in the last hour", "fieldConfig": {"defaults": {"color": {"fixedColor": "rgb(31, 120, 193)", "mode": "fixed"}, "mappings": [{"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "nullValueMode": "connected", "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 2, "x": 17, "y": 1}, "id": 12, "maxDataPoints": 100, "options": {"colorMode": "none", "graphMode": "area", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"]}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "sum(increase(node_network_transmit_bytes_total{hostname=~\"$host\"}[24h]))", "format": "time_series", "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "Net OUT (24h)", "type": "stat"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 4, "w": 5, "x": 19, "y": 1}, "id": 37, "options": {}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "node_filesystem_free_bytes{fstype!~\"(tmpfs|rootfs).*\",hostname=~\"$host\"}", "format": "table", "instant": true, "intervalFactor": 1, "legendFormat": "", "refId": "A"}], "title": "<PERSON>sk (Free)", "type": "table-old"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 26, "panels": [], "title": "CPU Details", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 6}, "id": 14, "options": {"legend": {"calcs": ["mean", "lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "sum by (mode) (irate(node_cpu_seconds_total{hostname=~\"$host\"}[5m]))", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{mode}}", "refId": "B"}], "title": "CPU Load | ${hostname}", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 24, "panels": [], "title": "Memory Details", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 13}, "id": 16, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "node_memory_MemFree_bytes{hostname=~\"$host\"} > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Free", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "node_memory_MemTotal_bytes{hostname=~\"$host\"} > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Total", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "node_memory_MemAvailable_bytes{hostname=~\"$host\"} > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Available", "refId": "C"}], "title": "Memory | ${hostname}", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 28, "panels": [], "title": "Network Details", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 6, "w": 14, "x": 0, "y": 20}, "id": 18, "options": {"legend": {"calcs": ["max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "irate(node_network_receive_bytes_total{hostname=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m])  > 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "IN ({{device}})", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "- irate(node_network_transmit_bytes_total{hostname=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m]) < 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "OUT ({{device}})", "refId": "B"}], "title": "Network Traffic | ${hostname}", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 6, "w": 10, "x": 14, "y": 20}, "id": 43, "options": {"legend": {"calcs": ["max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "irate(node_network_receive_errs_total{hostname=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m]) + irate(node_network_receive_drop_total{hostname=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Err/Drop IN ({{device}})", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "- (irate(node_network_transmit_errs_total{hostname=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m]) + irate(node_network_transmit_drop_total{hostname=~\"$host\",device=~\"(?i)^(ens|eth).+$\"}[5m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Err/Drop OUT ({{device}})", "refId": "B"}], "title": "Network Traffic | ${hostname}", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 22, "panels": [], "title": "Disk Details | ${hostname}", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 9, "x": 0, "y": 27}, "id": 40, "options": {"legend": {"calcs": ["lastNotNull"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "node_filesystem_free_bytes{hostname=~\"$host\",fstype!~\"(tmpfs|rootfs)\"}", "format": "time_series", "instant": false, "intervalFactor": 1, "legendFormat": "{{mountpoint}}", "refId": "A"}], "title": "Disk (Free) | ${hostname}", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 9, "y": 27}, "id": 30, "options": {"legend": {"calcs": ["max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "irate(node_disk_read_bytes_total{hostname=~\"$host\"}[5m]) > 0", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Read ({{device}})", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "- irate(node_disk_written_bytes_total{hostname=~\"$host\"}[5m]) < 0", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Write ({{device}})", "refId": "B"}], "title": "Disk Activity | ${hostname}", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 7, "w": 7, "x": 17, "y": 27}, "id": 32, "options": {"legend": {"calcs": ["max", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "repeat": "host", "repeatDirection": "v", "targets": [{"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "irate(node_disk_read_time_seconds_total{hostname=~\"$host\"}[5m]) > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Read ({{device}})", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "P4169E866C3094E38"}, "expr": "- irate(node_disk_write_time_seconds_total{hostname=~\"$host\"}[5m]) < 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "Write ({{device}})", "refId": "B"}], "title": "Disk IO | ${hostname}", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": ["linux", "node-exporter", "ops"], "templating": {"list": [{"current": {"text": ["node-exporter:9100"], "value": ["node-exporter:9100"]}, "datasource": "P4169E866C3094E38", "definition": "label_values(node_time_seconds,hostname)", "includeAll": false, "label": "Host", "multi": true, "name": "host", "options": [], "query": "label_values(node_time_seconds,hostname)", "refresh": 1, "regex": "", "type": "query"}, {"current": {"text": "node-exporter", "value": "node-exporter"}, "datasource": "P4169E866C3094E38", "definition": "label_values(node_boot_time_seconds,job)", "includeAll": false, "label": "Job", "name": "job", "options": [], "query": "label_values(node_boot_time_seconds,job)", "refresh": 1, "regex": "/(.*)/", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Linux Hosts Metrics | Base", "uid": "ov0oEgdik", "version": 1, "weekStart": ""}