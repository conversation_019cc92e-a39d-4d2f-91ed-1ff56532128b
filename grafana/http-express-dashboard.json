{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 200, "panels": [], "title": "HTTP Status Codes", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 201, "options": {"legend": {"calcs": ["mean", "max", "last"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum(rate(http_request_duration_seconds_count{status_code=~\"2..\"}[1m]))", "legendFormat": "2xx Success", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum(rate(http_request_duration_seconds_count{status_code=~\"3..\"}[1m]))", "hide": false, "legendFormat": "3xx Redirect", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum(rate(http_request_duration_seconds_count{status_code=~\"4..\"}[1m]))", "hide": false, "legendFormat": "4xx Client Error", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum(rate(http_request_duration_seconds_count{status_code=~\"5..\"}[1m]))", "hide": false, "legendFormat": "5xx Server Error", "range": true, "refId": "D"}], "title": "HTTP Status Codes (Requests/sec)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 202, "options": {"legend": {"calcs": ["mean", "max", "last"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${DS_PROMETHEUS}"}, "editorMode": "code", "expr": "sum(rate(http_request_duration_seconds_count[1m])) by (path, method)", "legendFormat": "{{method}} {{path}}", "range": true, "refId": "A"}], "title": "HTTP Request Rate by Endpoint", "type": "timeseries"}], "refresh": "1h", "schemaVersion": 37, "style": "dark", "tags": ["express", "node.js", "prometheus"], "templating": {"list": [{"current": {"selected": false, "text": "Prometheus", "value": "Prometheus"}, "hide": 0, "includeAll": false, "label": "Data Source", "multi": false, "name": "DS_PROMETHEUS", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": true, "text": "2..|3..|4..|5..", "value": "2..|3..|4..|5.."}, "hide": 0, "label": "Status Code", "name": "status_code", "options": [{"selected": true, "text": "2..|3..|4..|5..", "value": "2..|3..|4..|5.."}, {"selected": false, "text": "2..", "value": "2.."}, {"selected": false, "text": "3..", "value": "3.."}, {"selected": false, "text": "4..", "value": "4.."}, {"selected": false, "text": "5..", "value": "5.."}], "query": "2..|3..|4..|5..,2..,3..,4..,5..", "skipUrlSync": false, "type": "custom"}, {"current": {"selected": true, "text": "stsvpo-server", "value": "stsvpo-server"}, "hide": 0, "includeAll": false, "label": "Job", "multi": false, "name": "job", "options": [{"selected": true, "text": "stsvpo-server", "value": "stsvpo-server"}], "query": "stsvpo-server", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Express.js HTTP Dashboard only", "uid": "express-http-dashboard-only", "version": 1, "weekStart": ""}