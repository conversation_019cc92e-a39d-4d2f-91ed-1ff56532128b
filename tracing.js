// tracing.js
'use strict'
const process = require('process')
require('dotenv').config();

// Check if tracing is enabled
const enableTracing = process.env.ENABLE_TRACING === 'true';
const enableTelemetry = process.env.ENABLE_TELEMETRY === 'true';

// Only initialize tracing if it's enabled
if (enableTracing && enableTelemetry) {
  const opentelemetry = require('@opentelemetry/sdk-node')
  const { getNodeAutoInstrumentations } = require('@opentelemetry/auto-instrumentations-node')
  const { OTLPTraceExporter } = require('@opentelemetry/exporter-trace-otlp-http')
  const { resourceFromAttributes } = require('@opentelemetry/resources')
  const { SemanticResourceAttributes } = require('@opentelemetry/semantic-conventions')
  const { ExpressInstrumentation } = require('@opentelemetry/instrumentation-express')

  // Configuration for trace exporter
  const exporterOptions = {
    url: process.env.OTEL_EXPORTER_OTLP_TRACES_ENDPOINT || 'http://tianyen-service.com:4318/v1/traces',
    headers: {
      'Content-Type': 'application/json',
      'signoz-access-token': process.env.SIGNOZ_INGESTION_KEY || '',
    },
  }

  // Create trace exporter
  const traceExporter = new OTLPTraceExporter(exporterOptions)

  // Configure Express instrumentation with HTTP context attributes
  const expressInstrumentation = new ExpressInstrumentation({
    enabled: true,
    requestHook: (span, info) => {
      if (info.request) {
        // Improved client IP detection
        const clientIp = info.request.headers['x-forwarded-for'] ? 
          info.request.headers['x-forwarded-for'].split(',')[0].trim() : 
          info.request.ip || 
          info.request.connection.remoteAddress || 
          '';
          
        // Add HTTP context attributes to spans
        span.setAttribute('http.user_agent', info.request.headers['user-agent'] || '');
        span.setAttribute('http.client_ip', clientIp);
        span.setAttribute('http.host', info.request.headers.host || '');
        span.setAttribute('http.request_id', info.request.headers['x-request-id'] || '');
        span.setAttribute('http.language', info.request.headers['accept-language'] || '');
        
        // Add request query parameters as attributes (sanitized)
        if (info.request.query) {
          const safeQuery = { ...info.request.query };
          // Remove sensitive fields
          delete safeQuery.password;
          delete safeQuery.token;
          span.setAttribute('http.query_params', JSON.stringify(safeQuery));
        }
      }
    },
    responseHook: (span, info) => {
      if (info.response) {
        // Add response status code
        span.setAttribute('http.status_code', info.response.statusCode);
        
        // Add response time
        if (info.response.locals && info.response.locals.responseTime) {
          span.setAttribute('http.response_time_ms', info.response.locals.responseTime);
        }
      }
    }
  });

  // Configure the SDK
  const sdk = new opentelemetry.NodeSDK({
    traceExporter,
    instrumentations: [
      getNodeAutoInstrumentations(),
      expressInstrumentation
    ],
    resource: resourceFromAttributes({
      [SemanticResourceAttributes.SERVICE_NAME]: process.env.SERVICE_NAME ,
      [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: process.env.NODE_ENV ,
    })
  })

  // Initialize the SDK and register with the OpenTelemetry API
  // this enables the API to record telemetry
  sdk.start()

  // Gracefully shut down the SDK on process exit
  process.on('SIGTERM', () => {
    sdk
      .shutdown()
      .then(() => console.log('Tracing terminated'))
      .catch((error) => console.log('Error terminating tracing', error))
      .finally(() => process.exit(0))
  })

  console.log('OpenTelemetry tracing initialized successfully');
} else {
  console.log('OpenTelemetry tracing is disabled via ENABLE_TRACING or ENABLE_TELEMETRY flag');
}
