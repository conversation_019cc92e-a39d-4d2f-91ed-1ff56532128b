# 錯誤參考指南

本文檔提供後端應用程式中常見錯誤、其原因以及可能解決方案的參考。使用本指南可快速識別和解決問題。

## HTTP 狀態碼

| 狀態碼 | 描述 | 常見原因 | 解決方案 |
|-------------|-------------|---------------|----------|
| 400 | 錯誤請求 | 無效的表單數據，缺少必填字段 | 檢查表單數據的完整性和有效性 |
| 401 | 未授權 | 缺失或無效的身份驗證憑證 | 確保用戶已使用有效憑證登錄 |
| 403 | 禁止訪問 | 用戶缺乏訪問資源的權限 | 驗證用戶角色和權限 |
| 404 | 未找到 | 資源或路由不存在 | 檢查 URL 路徑、路由配置或資源 ID |
| 500 | 內部伺服器錯誤 | 應用程式代碼中的伺服器端錯誤 | 查看伺服器日誌以獲取詳細錯誤信息 |

## 表單提交錯誤

### 路由不匹配錯誤

**錯誤**: "糟糕！發生錯誤。找不到您要查找的頁面。"

**原因**: 表單動作 URL 與路由器中定義的路由不匹配。

**示例**: 表單提交到 `/admin/about/edit/2` 而路由定義為 `/admin/about/2`

**解決方案**: 更新表單動作以匹配路由器中定義的路由：
```html
<!-- 錯誤 -->
<form action="/admin/about/edit/<%= item.id %>" method="POST">

<!-- 正確 -->
<form action="/admin/about/<%= item.id %>" method="POST">
```

### 文件上傳錯誤

**錯誤**: "處理文件上傳失敗"

**原因**: 缺少文件上傳中間件或表單中的 enctype 不正確

**解決方案**: 
1. 確保表單具有 `enctype="multipart/form-data"` 屬性
2. 在路由中添加上傳中間件：
```javascript
router.post('/about/:id', hasRole(['super_admin', 'admin']), aboutController.upload, aboutController.updateItem);
```

## 數據庫錯誤

### 唯一約束違反

**錯誤**: "字段 {field} 上的唯一約束失敗"

**原因**: 嘗試創建具有必須唯一但已存在值的記錄

**解決方案**: 在創建之前檢查記錄是否已存在，或更新現有記錄

### 外鍵約束違反

**錯誤**: "字段 {field} 上的外鍵約束失敗"

**原因**: 引用不存在的記錄或刪除被其他記錄引用的記錄

**解決方案**: 確保引用的記錄存在或適當處理級聯刪除

### 類型錯誤

**錯誤**: "參數 {argument} 必須是 {type}，但得到了 {actual_type}"

**原因**: 向 Prisma 查詢傳遞錯誤的資料類型

**解決方案**: 在傳遞給 Prisma 之前將資料轉換為正確的類型：
```javascript
// 錯誤
const item = await prisma.aboutItem.findUnique({ where: { id: req.params.id } });

// 正確
const item = await prisma.aboutItem.findUnique({ where: { id: parseInt(req.params.id) } });
```

### 複合鍵錯誤

**錯誤**: "複合鍵 `userId_categoryId` 缺少必需值"

**原因**: 創建 CategoryPermission 記錄時，複合鍵的一個或多個部分缺失

**解決方案**: 確保同時提供 userId 和 categoryId 作為整數：
```javascript
await prisma.categoryPermission.create({
  data: {
    userId: parseInt(userId),
    categoryId: parseInt(categoryId),
    canView: true,
    canCreate: false,
    canEdit: false,
    canDelete: false
  }
});
```

## 身份驗證錯誤

### 會話過期

**錯誤**: "請登錄以訪問此資源"

**原因**: 用戶會話已過期或無效

**解決方案**: 將用戶重定向到登錄頁面並清除無效的會話數據

### 權限被拒絕

**錯誤**: "您沒有權限訪問此資源"

**原因**: 用戶缺乏所請求操作所需的角色或權限

**解決方案**: 檢查用戶角色和權限，或請求提升權限

### 角色特定訪問被拒絕

**錯誤**: "您只能訪問訪廠資訊頁面"

**原因**: 訪客用戶嘗試訪問非訪問頁面

**解決方案**: 訪客用戶應只訪問訪問頁面，或應更新其角色

### 分類權限錯誤

**錯誤**: "分類權限格式無效"

**原因**: 分配分類權限時權限數據格式錯誤

**解決方案**: 確保權限以正確結構的對象數組形式發送：
```javascript
// 正確格式
const permissions = [
  {
    categoryId: 1,
    canView: true,
    canCreate: true,
    canEdit: false,
    canDelete: false
  },
  // 更多權限...
];
```

## 文件系統錯誤

### 文件未找到

**錯誤**: "ENOENT: 沒有這樣的文件或目錄"

**原因**: 嘗試訪問不存在的文件

**解決方案**: 檢查文件路徑並確保在訪問文件之前目錄存在

### 權限被拒絕

**錯誤**: "EACCES: 權限被拒絕"

**原因**: 讀取/寫入文件的權限不足

**解決方案**: 檢查文件權限並確保應用程式具有適當的訪問權限

## 中間件錯誤

### 缺少中間件

**錯誤**: "無法讀取未定義的屬性 'file'" 或 "req.file 未定義"

**原因**: 路由定義中缺少文件上傳中間件

**解決方案**: 向路由添加適當的中間件：
```javascript
router.post('/route', uploadMiddleware, controller.method);
```

### 身份驗證中間件錯誤

**錯誤**: "無法讀取未定義的屬性 'role'"

**原因**: 會話無效時訪問 req.session.user 屬性

**解決方案**: 在訪問 req.session.user 屬性之前始終檢查它是否存在：
```javascript
// 錯誤
const userRole = req.session.user.role;

// 正確
const userRole = req.session.user ? req.session.user.role : null;
if (!userRole) {
  return res.redirect('/admin/login');
}
```

## 訪廠系統錯誤

### 狀態更新錯誤

**錯誤**: "更新訪廠狀態失敗"

**原因**: 更新訪問狀態時狀態值無效或數據庫錯誤

**解決方案**: 
1. 檢查狀態值是否與系統中允許的值匹配
2. 正確處理數據庫事務：
```javascript
try {
  await prisma.$transaction(async (tx) => {
    await tx.visit.update({
      where: { id: parseInt(id) },
      data: { status: newStatus }
    });
    // 額外日誌記錄或相關更新
  });
} catch (error) {
  logger.error('訪廠狀態更新錯誤:', error);
  req.flash('error_msg', '更新訪廠狀態失敗');
}
```

### 訪廠日期驗證錯誤

**錯誤**: "參訪日期無效"

**原因**: 訪問日期是過去日期或格式無效

**解決方案**: 提交前驗證日期：
```javascript
const visitDate = new Date(req.body.visitDate);
const today = new Date();
if (visitDate < today) {
  req.flash('error_msg', '參訪日期必須是未來日期');
  return res.redirect('back');
}
```

## 常見故障排除步驟

1. **檢查伺服器日誌**: 最詳細的錯誤信息會在伺服器日誌中
2. **驗證路由配置**: 確保路由正確定義並與表單動作匹配
3. **檢查中間件順序**: 中間件執行順序很重要; 確保它們按正確順序排列
4. **驗證表單數據**: 確保所有必填字段都存在且格式正確
5. **檢查文件路徑**: 對於文件操作，驗證路徑是否正確且目錄存在
6. **檢查數據庫查詢**: 記錄數據庫查詢以驗證它們是否正確形成
7. **檢查身份驗證**: 驗證用戶是否已經過身份驗證並擁有適當的權限
8. **檢查角色權限**: 驗證用戶是否具有該操作的正確角色和權限

## 日誌記錄最佳實踐

添加詳細的日誌記錄以幫助排查問題：

```javascript
try {
    // 可能失敗的操作
} catch (error) {
    logger.error(`更新項目失敗: ${error.message}`, {
        error,
        userId: req.session.user.id,
        itemId: req.params.id,
        userRole: req.session.user.role
    });
    req.flash('error_msg', `更新項目失敗: ${error.message}`);
    res.redirect('/admin/items');
}
```

## 使用標準化錯誤碼系統

我們的應用程式已實現標準化的錯誤碼系統，定義在 `src/config/errorCodes.js` 中：

```javascript
// 使用錯誤碼示例
const { COMMON_ERROR_CODES, USER_ERROR_CODES } = require('../config/errorCodes');

try {
    // 操作
} catch (error) {
    logger.error(`${COMMON_ERROR_CODES.DATABASE_ERROR}: ${error.message}`);
    req.flash('error_msg', `錯誤碼 ${COMMON_ERROR_CODES.DATABASE_ERROR}: ${error.message}`);
}
```

主要錯誤碼類別包括：

- `COMMON_ERROR_CODES`: 通用錯誤（DATABASE_ERROR, INTERNAL_SERVER_ERROR 等）
- `AUTH_ERROR_CODES`: 認證相關錯誤
- `USER_ERROR_CODES`: 用戶管理相關錯誤
- `ARTICLE_ERROR_CODES`: 文章管理相關錯誤
- `NEWS_ERROR_CODES`: 新聞管理相關錯誤
- `PAGE_ERROR_CODES`: 頁面管理相關錯誤
- `BANNER_ERROR_CODES`: 橫幅管理相關錯誤
- `PLATFORM_ERROR_CODES`: 平台管理相關錯誤
- `PARTNER_ERROR_CODES`: 合作夥伴相關錯誤
- `FRONTEND_ERROR_CODES`: 前端顯示相關錯誤
- `FRONTPAGE_ERROR_CODES`: 首頁管理相關錯誤
- `ABOUT_ERROR_CODES`: 關於頁面相關錯誤
- `FAQ_ERROR_CODES`: FAQ管理相關錯誤
- `DOWNLOAD_ERROR_CODES`: 下載管理相關錯誤

## 基於角色的錯誤處理

根據用戶角色自定義錯誤消息以提供更好的用戶體驗：

```javascript
try {
  // 可能失敗的操作
} catch (error) {
  logger.error(`操作失敗: ${error.message}`, {
    errorCode: COMMON_ERROR_CODES.INTERNAL_SERVER_ERROR,
    userId: req.session?.user?.id,
    userRole: req.session?.user?.role
  });
  
  // 根據用戶角色定制錯誤消息
  if (req.session.user) {
    switch(req.session.user.role) {
      case 'super_admin':
      case 'admin':
        // 向管理員顯示詳細錯誤
        req.flash('error_msg', `詳細錯誤: ${error.message}`);
        break;
      case 'editor':
        // 向編輯者顯示簡化信息
        req.flash('error_msg', '操作失敗，請聯絡管理員');
        break;
      case 'guest':
        // 顯示訪客友好信息
        req.flash('error_msg', '無法完成操作');
        break;
      default:
        req.flash('error_msg', '操作失敗');
    }
  } else {
    req.flash('error_msg', '請先登錄');
  }
  
  res.redirect('back');
}
```

## 標準化錯誤處理工具

我們的應用程式使用 `src/utils/errorHandler.js` 中定義的標準化錯誤處理工具：

### handleControllerError

用於處理管理控制器中的錯誤：

```javascript
const { handleControllerError } = require('../utils/errorHandler');
const { NEWS_ERROR_CODES } = require('../config/errorCodes');

try {
  // 操作
} catch (error) {
  handleControllerError(
    error,
    req,
    res,
    NEWS_ERROR_CODES.LIST_FAILED,
    '載入新聞失敗',
    '/admin/news'
  );
}
```

### handleApiError

用於處理API錯誤：

```javascript
const { handleApiError } = require('../utils/errorHandler');
const { API_ERROR_CODES } = require('../config/errorCodes');

try {
  // API操作
} catch (error) {
  return handleApiError(
    error,
    req,
    res,
    API_ERROR_CODES.FETCH_FAILED,
    '獲取數據失敗',
    400
  );
}
```

### handleFrontendError

用於處理前端頁面錯誤：

```javascript
const { handleFrontendError } = require('../utils/errorHandler');
const { FRONTEND_ERROR_CODES } = require('../config/errorCodes');

try {
  // 前端頁面渲染操作
} catch (error) {
  return handleFrontendError(
    error,
    req,
    res,
    FRONTEND_ERROR_CODES.RENDER_FAILED,
    '無法載入頁面',
    'frontend/error',
    { title: '錯誤頁面' }
  );
}
```

## 全局錯誤處理

我們的應用程式已實現全局錯誤處理中間件，可在 `app.js` 中找到。此中間件會捕獲未在控制器中處理的所有錯誤：

```javascript
app.use((err, req, res, next) => {
    // 使用請求感知logger（如果可用）
    const reqLogger = logger.withRequest ? logger.withRequest(req, res) : logger;
    
    // 根據環境記錄適當詳細程度的錯誤
    if (process.env.NODE_ENV === 'development') {
        reqLogger.error('未處理的錯誤:', { 
            message: err.message,
            stack: err.stack,
            path: req.path
        });
    } else {
        reqLogger.error('未處理的錯誤:', { 
            message: err.message,
            path: req.path
        });
    }
    
    // 根據請求路徑設置布局
    const layout = req.path.startsWith('/admin') ? 'layouts/admin' : 'layouts/frontend';
    res.locals.layout = layout;
    
    // 渲染錯誤頁面
    res.status(500).render('error', {
        title: '錯誤',
        message: '發生意外錯誤',
        error: process.env.NODE_ENV === 'development' ? err : {},
        layout: layout
    });
}); 