## Deploy the app

```bash

zip -r output.zip . -x '.git' -x 'node_modules'

# Send entire file via croc
croc send output.zip

# on server directory path
cd /data/wwwroot/stsvpo.bun.tw

unzip output.zip

# password: oneinstack
nano .env

# Run prisma migrate 

npx prisma migrate dev

npx prisma generate

# Restart the pm2 application
pm2 restart 0
pm2 logs 0

```

## Pm2 debug

Start the pm2

```bash
cd /data/wwwroot/stsvpo.bun.tw

pm2 start node --name="stsvpo-server" -- -r $(pwd)/tracing.js   $(pwd)/src/app.js
```