global:
  scrape_interval: 60s
  evaluation_interval: 60s

# Remote write configuration
remote_write:
  - url: "https://ltt-remote-write-prometheus.curl.rs/api/v1/write"
    write_relabel_configs:
      - source_labels: [__name__]
        regex: 'up|http_requests_total|http_request_duration_seconds.*|nodejs_memory_usage_bytes|nodejs_eventloop_lag.*|nodejs_gc_duration_seconds.*|database_.*|http_response_time.*|http_.*_requests_total|prisma_.*|node_.*'
        action: keep

scrape_configs:
  # Node exporter metrics
  - job_name: 'node-exporter'
    scrape_interval: 5s
    static_configs:
      - targets: ['node-exporter:9100']
        labels:
          service: 'stsvpo-server'
          environment: 'development'
          hostname: 'lol-5.local'

  # Express app metrics
  - job_name: 'stsvpo-server'
    scrape_interval: 10s
    metrics_path: /metrics
    scheme: http
    static_configs:
      - targets: ['host.docker.internal:3000']
        labels:
          service: 'stsvpo-server'
          environment: 'development' 