const promClient = require('prom-client');
const promBundle = require('express-prom-bundle');
const logger = require('./logger');

// Check if metrics are enabled
const enableMetrics = process.env.ENABLE_METRICS === 'true';
const collectInterval = parseInt(process.env.METRICS_COLLECT_INTERVAL || '10000', 10);

// Get metrics authentication credentials from environment variables
const metricsUser = process.env.METRICS_USER;
const metricsPassword = process.env.METRICS_PASSWORD;

// Check if we're in production environment
const isProduction = process.env.NODE_ENV === 'production';
const requireAuth = isProduction && metricsUser && metricsPassword;

// Create a Registry to register the metrics
const register = new promClient.Registry();

// Add a default label to all metrics
register.setDefaultLabels({
  app: process.env.SERVICE_NAME || 'stsvpo-server',
  environment: process.env.NODE_ENV || 'development'
});

// Create custom metrics
// Renamed from http_request_duration_seconds to app_http_request_duration_seconds to avoid conflict
const httpRequestDurationMicroseconds = new promClient.Histogram({
  name: 'app_http_request_duration_seconds',
  help: 'Duration of HTTP requests in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5, 10]
});

const httpRequestsTotal = new promClient.Counter({
  name: 'app_http_requests_total',
  help: 'Total number of HTTP requests',
  labelNames: ['method', 'route', 'status_code']
});

const databaseQueriesTotal = new promClient.Counter({
  name: 'database_queries_total',
  help: 'Total number of database queries',
  labelNames: ['operation', 'model']
});

const databaseQueryDurationSeconds = new promClient.Histogram({
  name: 'database_query_duration_seconds',
  help: 'Duration of database queries in seconds',
  labelNames: ['operation', 'model'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1]
});

const memoryUsage = new promClient.Gauge({
  name: 'nodejs_memory_usage_bytes',
  help: 'Memory usage of Node.js in bytes',
  labelNames: ['type']
});

// Register the metrics
register.registerMetric(httpRequestDurationMicroseconds);
register.registerMetric(httpRequestsTotal);
register.registerMetric(databaseQueriesTotal);
register.registerMetric(databaseQueryDurationSeconds);
register.registerMetric(memoryUsage);

// Only enable default metrics collection if metrics are enabled
let metricsMiddleware = (req, res, next) => next();

if (enableMetrics) {
  // Enable the collection of default metrics
  promClient.collectDefaultMetrics({ 
    register,
    timeout: collectInterval 
  });
  
  // Update memory usage metrics periodically
  setInterval(() => {
    const usageData = process.memoryUsage();
    memoryUsage.set({ type: 'rss' }, usageData.rss);
    memoryUsage.set({ type: 'heapTotal' }, usageData.heapTotal);
    memoryUsage.set({ type: 'heapUsed' }, usageData.heapUsed);
    memoryUsage.set({ type: 'external' }, usageData.external);
  }, collectInterval);
  
  // Configure express-prom-bundle to use our registry and avoid duplicate metrics
  const promMetricsMiddleware = promBundle({
    includeMethod: true,
    includePath: true,
    includeStatusCode: true,
    includeUp: true,
    customLabels: { app: process.env.SERVICE_NAME || 'stsvpo-server' },
    promClient: {
      collectDefaultMetrics: false
    },
    metricsPath: '/metrics',
    formatStatusCode: (res) => res.statusCode,
    promRegistry: register, // Use our existing registry
    autoregister: false,    // Don't auto-register metrics that might conflict
    // Define metric names that won't conflict with our custom ones
    metricNames: {
      httpRequestDurationMicroseconds: 'http_request_duration_seconds', // Use default name from bundle
      httpRequestsTotal: 'http_requests_total' // Use default name from bundle
    }
  });
  
  // Combine auth middleware with prom-bundle middleware
  metricsMiddleware = (req, res, next) => {
    // Only apply authentication to the metrics endpoint and only in production
    if (req.path === '/metrics' && requireAuth) {
      // Check for basic auth header
      const authHeader = req.headers.authorization;
      
      if (!authHeader) {
        res.set('WWW-Authenticate', 'Basic realm="Prometheus Metrics"');
        return res.status(401).send('Authentication required');
      }
      
      // Get credentials from auth header
      const auth = Buffer.from(authHeader.split(' ')[1], 'base64').toString().split(':');
      const user = auth[0];
      const pass = auth[1];
      
      // Verify credentials
      if (user !== metricsUser || pass !== metricsPassword) {
        return res.status(403).send('Invalid credentials');
      }
    }
    
    // Then apply metrics middleware
    return promMetricsMiddleware(req, res, next);
  };
  
  // Log when metrics are initialized
  logger.info('Prometheus metrics initialized', {
    enabled: true,
    collectInterval,
    authEnabled: requireAuth
  });
} else {
  logger.info('Prometheus metrics are disabled', {
    enabled: false
  });
}

// Make the register available for other modules to register their metrics
module.exports = {
  register,
  metrics: {
    httpRequestDurationMicroseconds,
    httpRequestsTotal,
    databaseQueriesTotal,
    databaseQueryDurationSeconds,
    memoryUsage
  },
  metricsMiddleware,
  enableMetrics
}; 