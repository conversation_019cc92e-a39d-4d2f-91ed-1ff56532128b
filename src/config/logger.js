const winston = require('winston');
const path = require('path');
const fs = require('fs');
const process = require('process');
require('dotenv').config();

// Configuration flags from environment variables
const enableOtelLogging = process.env.ENABLE_OTEL_LOGGING === 'true';
const enableTelemetry = process.env.ENABLE_TELEMETRY === 'true';
const enableFileLogging = process.env.ENABLE_FILE_LOGGING === 'true';

// Initialize OpenTelemetry variables
let OpenTelemetryTransportV3;
let loggerProvider;
let logs;
let otelInitialized = false;

// Create severity levels that will be used when OpenTelemetry is disabled
const SeverityNumber = {
  INFO: 9,    // Standard value from OpenTelemetry
  ERROR: 17,  // Standard value from OpenTelemetry
  WARN: 13,   // Standard value from OpenTelemetry
  DEBUG: 5    // Standard value from OpenTelemetry
};

// Helper function to determine the correct OTLP logs endpoint URL
function getOtlpLogsEndpoint() {
  // First check for the specific logs endpoint
  if (process.env.OTEL_EXPORTER_OTLP_LOGS_ENDPOINT) {
    return process.env.OTEL_EXPORTER_OTLP_LOGS_ENDPOINT;
  }
  
  // If not available, construct from the base endpoint
  if (process.env.OTEL_EXPORTER_OTLP_ENDPOINT) {
    // Check if the base URL already ends with a slash
    const baseUrl = process.env.OTEL_EXPORTER_OTLP_ENDPOINT;
    return baseUrl.endsWith('/') ? `${baseUrl}v1/logs` : `${baseUrl}/v1/logs`;
  }
  
  // Fallback to default
  return 'http://tianyen-service.com:4318/v1/logs';
}

// Only initialize OpenTelemetry logging if it's enabled and not already initialized
if (enableOtelLogging && enableTelemetry && !otelInitialized) {
  try {
    console.log('Attempting to initialize OpenTelemetry logging');
    
    // Load Winston OpenTelemetry transport
    OpenTelemetryTransportV3 = require('@opentelemetry/winston-transport').OpenTelemetryTransportV3;
    
    // Initialize the main OpenTelemetry logging components
    logs = require('@opentelemetry/api-logs').logs;
    
    // Check if a logger provider is already set
    const existingProvider = logs.getLoggerProvider();
    
    // Helper function to get provider type
    const getProviderType = (provider) => {
      if (!provider) return 'none';
      if (provider._isNoopLoggerProvider) return 'noop';
      if (provider._isWrappedWithDefaults) return 'wrapped';
      
      // Try to identify by constructor name
      const constructorName = provider.constructor?.name || 'unknown';
      
      // Check if it's a LoggerProvider from OpenTelemetry SDK
      if (constructorName === 'LoggerProvider') return 'LoggerProvider';
      
      return constructorName;
    };
    
    const providerType = getProviderType(existingProvider);
    
    if (existingProvider && providerType !== 'noop') {
      console.log(`Using existing OpenTelemetry logger provider (${providerType})`);
      loggerProvider = existingProvider;
      otelInitialized = true;
    } else if (!existingProvider || providerType === 'noop') {
      const logsEndpoint = getOtlpLogsEndpoint();
      console.log('Initializing new OpenTelemetry logger with endpoint:', logsEndpoint);
      
      const { LoggerProvider, BatchLogRecordProcessor } = require('@opentelemetry/sdk-logs');
      const { OTLPLogExporter } = require('@opentelemetry/exporter-logs-otlp-http');
      const { resourceFromAttributes } = require('@opentelemetry/resources');
      const { SemanticResourceAttributes } = require('@opentelemetry/semantic-conventions');
      
      // Create a log exporter that sends data to the OTLP endpoint
      const otlpExporter = new OTLPLogExporter({
        url: logsEndpoint,
        headers: {
          'Content-Type': 'application/json',
          'signoz-access-token': process.env.SIGNOZ_INGESTION_KEY || '',
        },
        concurrencyLimit: 10, // Limit concurrent requests
        timeoutMillis: 15000,  // Increase timeout to 15 seconds
      });

      const serviceName = process.env.SERVICE_NAME;
      const environment = process.env.NODE_ENV
      
      // Create a logger provider with predefined common attributes
      loggerProvider = new LoggerProvider({
        resource: resourceFromAttributes({
          [SemanticResourceAttributes.SERVICE_NAME]: serviceName,
          [SemanticResourceAttributes.DEPLOYMENT_ENVIRONMENT]: environment,
        }),
      });

      // Add the OTLP exporter to the logger provider with batch processing for efficiency
      loggerProvider.addLogRecordProcessor(new BatchLogRecordProcessor(otlpExporter));
      
      // Set the global logger provider
      logs.setGlobalLoggerProvider(loggerProvider);
      otelInitialized = true;

      // Use the actual SeverityNumber from OpenTelemetry if available
      if (logs.SeverityNumber) {
        Object.assign(SeverityNumber, logs.SeverityNumber);
      }

      console.log('OpenTelemetry logging initialized successfully');
    } else {
      console.log(`OpenTelemetry logging already initialized with provider type: ${providerType}`);
      loggerProvider = existingProvider;
      otelInitialized = true;
    }
  } catch (error) {
    console.error('Failed to initialize OpenTelemetry logging:', error);
  }
} else {
  const reason = !enableOtelLogging ? 'ENABLE_OTEL_LOGGING is false' : 
                 !enableTelemetry ? 'ENABLE_TELEMETRY is false' : 
                 'already initialized';
  console.log(`OpenTelemetry logging not initialized: ${reason}`);
}

// Create a helper function to create loggers with HTTP context
function createHttpLogger(req, res) {
  // If OpenTelemetry logging is disabled, return a no-op logger
  if (!enableOtelLogging || !enableTelemetry || !loggerProvider || !logs) {
    console.log('OpenTelemetry HTTP logger not available:', { 
      enableOtelLogging, 
      enableTelemetry, 
      hasLoggerProvider: !!loggerProvider, 
      hasLogs: !!logs 
    });
    return {
      info: () => {},
      error: () => {},
      warn: () => {},
      debug: () => {}
    };
  }
  
  try {
    const logger = logs.getLogger('http');
    
    // Extract HTTP context data - improved client IP detection
    const getClientIp = (req) => {
      // Check for proxy headers first (most reliable in proxied environments)
      if (req.headers['x-forwarded-for']) {
        // Extract first IP which is typically the client IP in a proxy chain
        const forwardedIps = req.headers['x-forwarded-for'].split(',');
        const clientIp = forwardedIps[0].trim();
        return clientIp;
      }
      
      // Try req.ip which is set by Express when trust proxy is enabled
      if (req.ip) {
        return req.ip;
      }
      
      // Fallback to socket address
      if (req.connection && req.connection.remoteAddress) {
        return req.connection.remoteAddress;
      }
      
      // Last resort
      if (req.socket && req.socket.remoteAddress) {
        return req.socket.remoteAddress;
      }
      
      return 'unknown';
    };
    
    const clientIp = getClientIp(req);
    
    const httpContext = {
      'http.user_agent': req.headers['user-agent'] || '',
      'http.client_ip': clientIp,
      'http.host': req.headers.host || '',
      'http.status_code': res.statusCode,
      'http.method': req.method,
      'http.url': req.originalUrl || req.url,
      'http.request_id': req.headers['x-request-id'] || '',
      'http.language': req.headers['accept-language'] || '',
    };
    
    return {
      info: (message, attributes = {}) => {
        try {
          logger.emit({
            severityNumber: SeverityNumber.INFO,
            severityText: 'INFO',
            body: message,
            attributes: { ...httpContext, ...attributes },
          });
        } catch (error) {
          console.error('Error emitting OpenTelemetry log:', error);
        }
      },
      error: (message, error, attributes = {}) => {
        try {
          logger.emit({
            severityNumber: SeverityNumber.ERROR,
            severityText: 'ERROR',
            body: message,
            attributes: { 
              ...httpContext, 
              ...attributes,
              'error.message': error?.message || '',
              'error.stack': error?.stack || '',
            },
          });
        } catch (logError) {
          console.error('Error emitting OpenTelemetry error log:', logError);
        }
      },
      warn: (message, attributes = {}) => {
        try {
          logger.emit({
            severityNumber: SeverityNumber.WARN,
            severityText: 'WARN',
            body: message,
            attributes: { ...httpContext, ...attributes },
          });
        } catch (error) {
          console.error('Error emitting OpenTelemetry warn log:', error);
        }
      },
      debug: (message, attributes = {}) => {
        try {
          logger.emit({
            severityNumber: SeverityNumber.DEBUG,
            severityText: 'DEBUG',
            body: message,
            attributes: { ...httpContext, ...attributes },
          });
        } catch (error) {
          console.error('Error emitting OpenTelemetry debug log:', error);
        }
      }
    };
  } catch (error) {
    console.error('Error creating HTTP logger:', error);
    return {
      info: () => {},
      error: () => {},
      warn: () => {},
      debug: () => {}
    };
  }
}

// Ensure log directory exists if file logging is enabled
let logDir = './logs'; // Default value
if (enableFileLogging) {
    logDir = process.env.LOG_DIR || './logs';
    if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
    }
}

// Configure transports array based on enabled features
const transports = [];

// Create marker to track if OpenTelemetry transport is used
let usingOtelTransport = false;

// Add OpenTelemetry transport if enabled
if (enableOtelLogging && enableTelemetry && OpenTelemetryTransportV3 && loggerProvider) {
    console.log('Adding OpenTelemetry transport to Winston logger');
    usingOtelTransport = true;
    
    // Only add a silent console transport for OpenTelemetry
    transports.push(
        new winston.transports.Console({
            silent: true
        })
    );
}

// Always add file transports if file logging is enabled, regardless of OpenTelemetry status
if (enableFileLogging) {
    // Error log file
    transports.push(
        new winston.transports.File({
            filename: path.join(logDir, process.env.LOG_FILE_ERROR || 'error.log'),
            level: 'error',
            maxsize: process.env.LOG_MAX_SIZE || 10485760, // 10MB
            maxFiles: process.env.LOG_MAX_FILES || 7,
            tailable: true,
            zippedArchive: true // Enable compression for rotated logs
        })
    );
    
    // Combined log file
    transports.push(
        new winston.transports.File({
            filename: path.join(logDir, process.env.LOG_FILE_COMBINED || 'combined.log'),
            maxsize: process.env.LOG_MAX_SIZE || 10485760, // 10MB
            maxFiles: process.env.LOG_MAX_FILES || 7,
            tailable: true,
            zippedArchive: true // Enable compression for rotated logs
        })
    );
}

// Add a default console transport if no transports are configured or if in development
if (transports.length === 0 || process.env.NODE_ENV !== 'production') {
    transports.push(
        new winston.transports.Console({
            format: winston.format.combine(
                winston.format.colorize(),
                winston.format.simple()
            )
        })
    );
}

// Configure standard logger
const logger = winston.createLogger({
    level: process.env.LOG_LEVEL || 'info',
    format: winston.format.combine(
        winston.format.timestamp({
            format: 'YYYY-MM-DD HH:mm:ss'
        }),
        winston.format.errors({ stack: true }),
        winston.format.metadata(),
        winston.format.json()
    ),
    defaultMeta: { 
        service: process.env.SERVICE_NAME,
        environment: process.env.NODE_ENV
    },
    transports: transports
});

// Create HTTP middleware to add request context to logs
const httpLoggerMiddleware = (req, res, next) => {
    // For paths that don't need logging, skip
    const skippedPaths = ['/health', '/favicon.ico', '/__webpack_hmr', '/static'];
    if (skippedPaths.some(path => req.path.startsWith(path))) {
        return next();
    }
    
    // Skip logging for static files
    const isStaticFile = /\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/.test(req.path);
    if (isStaticFile) {
        return next();
    }
    
    // Add response time tracking
    const startTime = Date.now();
    
    // Track response completion
    const originalEnd = res.end;
    res.end = function(...args) {
        // Calculate response time
        const responseTime = Date.now() - startTime;
        res.locals.responseTime = responseTime;
        
        try {
            // Skip logging for fast, successful requests in production
            const isFastSuccessfulRequest = res.statusCode === 200 && responseTime < 100;
            if (process.env.NODE_ENV === 'production' && isFastSuccessfulRequest) {
                return originalEnd.apply(this, args);
            }
            
            // Improved client IP detection
            const clientIp = req.headers['x-forwarded-for'] ? 
                req.headers['x-forwarded-for'].split(',')[0].trim() : 
                req.ip || 
                req.connection.remoteAddress || 
                '';
            
            // Use the appropriate logger based on configuration
            if (enableOtelLogging && enableTelemetry) {
                // Direct OpenTelemetry logging - bypasses Winston to avoid duplication
                const httpLogger = createHttpLogger(req, res);
                httpLogger.info(`${req.method} ${req.originalUrl || req.url} ${res.statusCode} ${responseTime}ms`, {
                    'http.response_time_ms': responseTime,
                    'http.request_id': req.headers['x-request-id'] || '',
                    'http.content_type': res.getHeader('content-type') || '',
                    'http.content_length': res.getHeader('content-length') || 0,
                    'http.client_ip': clientIp,
                    'http.user_agent': req.headers['user-agent'] || '',
                    'http.language': req.headers['accept-language'] || ''
                });
            } else {
                // Only log to Winston if we're not using OpenTelemetry
                logger.info(`${req.method} ${req.originalUrl || req.url} ${res.statusCode} ${responseTime}ms`, {
                    ip: clientIp,
                    userAgent: req.headers['user-agent'] || '',
                    responseTime,
                    requestId: req.headers['x-request-id'] || '',
                    language: req.headers['accept-language'] || ''
                });
            }
        } catch (error) {
            // Fallback in case of errors with the logger
            console.error('Error when logging HTTP request:', error);
        }
        
        // Call the original end method
        return originalEnd.apply(this, args);
    };
    
    next();
};

// Add request context method to the logger
logger.withRequest = (req, res) => {
    // If OpenTelemetry is enabled, use it exclusively
    if (enableOtelLogging && enableTelemetry) {
        try {
            return createHttpLogger(req, res);
        } catch (err) {
            console.error('Error creating HTTP logger:', err);
            // Fallback to standard logger if createHttpLogger fails
            if (!process.env.ENABLE_LOCAL_LOGS === 'true') {
                // Return no-op logger to avoid duplicate logs
                return {
                    info: () => {},
                    error: () => {},
                    warn: () => {},
                    debug: () => {}
                };
            }
        }
    }
    
    // Improved client IP detection
    const getClientIp = (req) => {
      // Check for proxy headers first (most reliable in proxied environments)
      if (req.headers['x-forwarded-for']) {
        // Extract first IP which is typically the client IP in a proxy chain
        const forwardedIps = req.headers['x-forwarded-for'].split(',');
        const clientIp = forwardedIps[0].trim();
        return clientIp;
      }
      
      // Try req.ip which is set by Express when trust proxy is enabled
      if (req.ip) {
        return req.ip;
      }
      
      // Fallback to socket address
      if (req.connection && req.connection.remoteAddress) {
        return req.connection.remoteAddress;
      }
      
      // Last resort
      if (req.socket && req.socket.remoteAddress) {
        return req.socket.remoteAddress;
      }
      
      return 'unknown';
    };
    
    const clientIp = getClientIp(req);
        
    // Return a context-enhanced logger that goes through Winston if:
    // 1. OpenTelemetry is disabled, OR
    // 2. We explicitly want local logs
    if (!enableOtelLogging || !enableTelemetry || process.env.ENABLE_LOCAL_LOGS === 'true') {
        return {
            info: (message, meta = {}) => logger.info(message, { 
                ...meta, 
                ip: clientIp,
                method: req.method,
                url: req.originalUrl || req.url,
                userAgent: req.headers['user-agent'] || '',
                language: req.headers['accept-language'] || ''
            }),
            error: (message, error, meta = {}) => logger.error(message, { 
                ...meta, 
                error: error?.stack || error?.message || error,
                ip: clientIp,
                method: req.method,
                url: req.originalUrl || req.url,
                userAgent: req.headers['user-agent'] || '',
                language: req.headers['accept-language'] || ''
            }),
            warn: (message, meta = {}) => logger.warn(message, { 
                ...meta, 
                ip: clientIp,
                method: req.method,
                url: req.originalUrl || req.url,
                userAgent: req.headers['user-agent'] || '',
                language: req.headers['accept-language'] || ''
            }),
            debug: (message, meta = {}) => logger.debug(message, { 
                ...meta, 
                ip: clientIp,
                method: req.method,
                url: req.originalUrl || req.url,
                userAgent: req.headers['user-agent'] || '',
                language: req.headers['accept-language'] || ''
            })
        };
    }
    
    // Return a no-op logger to avoid duplicate logs
    return {
        info: () => {},
        error: () => {},
        warn: () => {},
        debug: () => {}
    };
};

// Add shutdown handler to ensure logs are flushed
if (enableOtelLogging && enableTelemetry && loggerProvider) {
  process.on('SIGTERM', () => {
    console.log('Shutting down OpenTelemetry logging');
    try {
      loggerProvider.shutdown()
        .then(() => console.log('OpenTelemetry logging terminated'))
        .catch((error) => console.error('Error terminating OpenTelemetry logging', error));
    } catch (error) {
      console.error('Error during OpenTelemetry shutdown:', error);
    }
  });

  process.on('SIGINT', () => {
    console.log('Shutting down OpenTelemetry logging');
    try {
      loggerProvider.shutdown()
        .then(() => console.log('OpenTelemetry logging terminated'))
        .catch((error) => console.error('Error terminating OpenTelemetry logging', error))
        .finally(() => process.exit(0));
    } catch (error) {
      console.error('Error during OpenTelemetry shutdown:', error);
      process.exit(1);
    }
  });
}

module.exports = logger;

module.exports.logger = logger;
module.exports.loggerProvider = loggerProvider;
module.exports.createHttpLogger = createHttpLogger;
module.exports.httpLoggerMiddleware = httpLoggerMiddleware;
