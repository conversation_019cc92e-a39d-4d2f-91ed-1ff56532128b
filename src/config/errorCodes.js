/**
 * Central Error Codes System
**/

// Common/General Error Codes
const COMMON_ERROR_CODES = {
    INPUT_VALIDATION: 'COMMON_E001',
    DATABASE_ERROR: 'COMMON_E002',
    FILE_SYSTEM_ERROR: 'COMMON_E003',
    INTERNAL_SERVER_ERROR: 'COMMON_E004',
    API_ERROR: 'COMMON_E005',
    NOT_FOUND: 'COMMON_E006',
    UNAUTHORIZED: 'COMMON_E007',
    FORBIDDEN: 'COMMON_E008',
    SESSION_EXPIRED: 'COMMON_E009',
    RATE_LIMIT_EXCEEDED: 'COMMON_E010'
};

// Authentication Error Codes
const AUTH_ERROR_CODES = {
    INVALID_CREDENTIALS: 'AUTH_E001',
    LOGIN_FAILED: 'AUTH_E002',
    SESSION_EXPIRED: 'AUTH_E003',
    UNAUTHORIZED_ACCESS: 'AUTH_E004',
    PASS<PERSON>ORD_RESET_FAILED: 'AUTH_E005',
    R<PERSON>ISTRATION_FAILED: 'AUTH_E006',
    INVALID_TOKEN: 'AUTH_E007',
    TOO_MANY_ATTEMPTS: 'AUTH_E008',
    LOGOUT_FAILED: 'AUTH_E009',
    GUEST_LOGIN_FAILED: 'AUTH_E010',
    INACTIVE_ACCOUNT: 'AUTH_E011',
    WRONG_LOGIN_PAGE: 'AUTH_E012'
};

// User Management Error Codes
const USER_ERROR_CODES = {
    LIST_FAILED: 'USER_E001',
    CREATE_FORM_FAILED: 'USER_E002',
    USER_EXISTS: 'USER_E003',
    INVALID_ROLE: 'USER_E004',
    UNAUTHORIZED_ROLE_CREATE: 'USER_E005',
    CREATE_FAILED: 'USER_E006',
    NOT_FOUND: 'USER_E007',
    SELF_EDIT_ONLY: 'USER_E008',
    ADMIN_CANNOT_EDIT_SUPER: 'USER_E009',
    LOAD_FAILED: 'USER_E010',
    UPDATE_FAILED: 'USER_E011',
    SELF_DELETE: 'USER_E012',
    DELETE_PERMISSION: 'USER_E013',
    ADMIN_DELETE_RESTRICTION: 'USER_E014',
    SUPER_ADMIN_ONLY: 'USER_E015',
    LAST_SUPER_ADMIN: 'USER_E016',
    DELETE_FAILED: 'USER_E017',
    PROJECT_ERROR: 'USER_E018'
};

// Category Related Error Codes
const CATEGORY_ERROR_CODES = {
    LIST_FAILED: 'CATEGORY_E001',
    CREATE_FAILED: 'CATEGORY_E002',
    UPDATE_FAILED: 'CATEGORY_E003',
    DELETE_FAILED: 'CATEGORY_E004',
    NOT_FOUND: 'CATEGORY_E005',
    ALREADY_EXISTS: 'CATEGORY_E006',
    PERMISSION_ERROR: 'CATEGORY_E007',
    INVALID_PARENT: 'CATEGORY_E008'
};

// Category Permission Error Codes
const CATEGORY_PERMISSION_ERROR_CODES = {
    ASSIGN_FAILED: 'CATEGORY_PERMISSION_E001',
    FETCH_FAILED: 'CATEGORY_PERMISSION_E002',
    CHECK_FAILED: 'CATEGORY_PERMISSION_E003',
    USER_NOT_FOUND: 'CATEGORY_PERMISSION_E004',
    INVALID_ROLE: 'CATEGORY_PERMISSION_E005',
    INVALID_FORMAT: 'CATEGORY_PERMISSION_E006',
    TRANSACTION_FAILED: 'CATEGORY_PERMISSION_E007'
};

// Media Upload Error Codes
const MEDIA_ERROR_CODES = {
    UPLOAD_FAILED: 'MEDIA_E001',
    INVALID_FILE_TYPE: 'MEDIA_E002',
    FILE_TOO_LARGE: 'MEDIA_E003',
    DELETE_FAILED: 'MEDIA_E004',
    STORAGE_ERROR: 'MEDIA_E005',
    FILE_NOT_FOUND: 'MEDIA_E006',
    DIRECTORY_ERROR: 'MEDIA_E007',
    PROCESSING_ERROR: 'MEDIA_E008',
    METADATA_ERROR: 'MEDIA_E009'
};

// Article Management Error Codes
const ARTICLE_ERROR_CODES = {
    LIST_FAILED: 'ARTICLE_E001',
    CREATE_FAILED: 'ARTICLE_E002',
    UPDATE_FAILED: 'ARTICLE_E003',
    DELETE_FAILED: 'ARTICLE_E004',
    NOT_FOUND: 'ARTICLE_E005',
    INVALID_STATUS: 'ARTICLE_E006',
    PERMISSION_ERROR: 'ARTICLE_E007',
    FEATURED_LIMIT: 'ARTICLE_E008'
};

// News Management Error Codes
const NEWS_ERROR_CODES = {
    LIST_FAILED: 'NEWS_E001',
    CREATE_FAILED: 'NEWS_E002',
    UPDATE_FAILED: 'NEWS_E003',
    DELETE_FAILED: 'NEWS_E004',
    NOT_FOUND: 'NEWS_E005',
    CATEGORY_ERROR: 'NEWS_E006',
    IMAGE_UPLOAD_ERROR: 'NEWS_E007',
    PUBLISH_ERROR: 'NEWS_E008',
    LIST_CATEGORIES_FAILED: 'NEWS_E009',
    CREATE_CATEGORY_FAILED: 'NEWS_E010',
    UPDATE_CATEGORY_FAILED: 'NEWS_E011',
    DELETE_CATEGORY_FAILED: 'NEWS_E012',
    CATEGORY_NOT_FOUND: 'NEWS_E013',
    CATEGORY_EXISTS: 'NEWS_E014',
    INVALID_ID: 'NEWS_E015',
    CREATE_FORM_FAILED: 'NEWS_E016',
    EDIT_FORM_FAILED: 'NEWS_E017',
    FRONTEND_RENDER_FAILED: 'NEWS_E018',
    SLUG_EXISTS: 'NEWS_E019',
    INVALID_STATUS: 'NEWS_E020'
};

// Page Management Error Codes
const PAGE_ERROR_CODES = {
    LIST_FAILED: 'PAGE_E001',
    CREATE_FAILED: 'PAGE_E002',
    UPDATE_FAILED: 'PAGE_E003',
    DELETE_FAILED: 'PAGE_E004',
    NOT_FOUND: 'PAGE_E005',
    SLUG_EXISTS: 'PAGE_E006',
    TEMPLATE_ERROR: 'PAGE_E007',
    ATTACHMENT_ERROR: 'PAGE_E008',
    PAGE_IMAGE_ERROR: 'PAGE_E009',
    CREATE_FORM_FAILED: 'PAGE_E010',
    EDIT_FORM_FAILED: 'PAGE_E011',
    CONTENT_PROCESSING_ERROR: 'PAGE_E012',
    UPLOAD_DIR_FAILED: 'PAGE_E013',
    DELETE_ATTACHMENT_FAILED: 'PAGE_E014',
    FILE_SIZE_EXCEEDED: 'PAGE_E015'
};

// Page Image Error Codes
const PAGE_IMAGE_ERROR_CODES = {
    LIST_FAILED: 'PAGE_IMAGE_E001',
    CREATE_FORM_FAILED: 'PAGE_IMAGE_E002',
    CREATE_FAILED: 'PAGE_IMAGE_E003',
    UPDATE_FAILED: 'PAGE_IMAGE_E004',
    DELETE_FAILED: 'PAGE_IMAGE_E005',
    NOT_FOUND: 'PAGE_IMAGE_E006',
    EDIT_FORM_FAILED: 'PAGE_IMAGE_E007',
    UPLOAD_FAILED: 'PAGE_IMAGE_E008',
    FILE_ACCESS_ERROR: 'PAGE_IMAGE_E009',
    MISSING_FILES: 'PAGE_IMAGE_E010',
    STATUS_TOGGLE_FAILED: 'PAGE_IMAGE_E011'
};

// Banner Management Error Codes
const BANNER_ERROR_CODES = {
    LIST_FAILED: 'BANNER_E001',
    CREATE_FAILED: 'BANNER_E002',
    UPDATE_FAILED: 'BANNER_E003',
    DELETE_FAILED: 'BANNER_E004',
    NOT_FOUND: 'BANNER_E005',
    UPLOAD_ERROR: 'BANNER_E006',
    ORDER_ERROR: 'BANNER_E007',
    ACTIVE_ERROR: 'BANNER_E008',
    RESPONSIVE_ERROR: 'BANNER_E009'
};

// Platform Management Error Codes
const PLATFORM_ERROR_CODES = {
    LIST_FAILED: 'PLATFORM_E001',
    CREATE_FORM_FAILED: 'PLATFORM_E002',
    CREATE_FAILED: 'PLATFORM_E003',
    UPDATE_FAILED: 'PLATFORM_E004',
    DELETE_FAILED: 'PLATFORM_E005',
    NOT_FOUND: 'PLATFORM_E006',
    INVALID_ID: 'PLATFORM_E007',
    CATEGORY_ERROR: 'PLATFORM_E008',
    ATTACHMENT_ERROR: 'PLATFORM_E009',
    SLUG_EXISTS: 'PLATFORM_E010',
    EMBED_ERROR: 'PLATFORM_E011',
    FILE_PROCESSING_ERROR: 'PLATFORM_E012',
    RENDER_FORM_FAILED: 'PLATFORM_E013',
    API_ERROR: 'PLATFORM_E014',
    CATEGORY_LIST_FAILED: 'PLATFORM_E015',
    CATEGORY_CREATE_FAILED: 'PLATFORM_E016',
    CATEGORY_UPDATE_FAILED: 'PLATFORM_E017',
    CATEGORY_DELETE_FAILED: 'PLATFORM_E018',
    CATEGORY_HAS_PLATFORMS: 'PLATFORM_E019',
    FRONTEND_RENDER_FAILED: 'PLATFORM_E020',
    PARTNERS_DATA_ERROR: 'PLATFORM_E021',
    UPLOAD_FAILED: 'PLATFORM_E022'
};

// Partner Management Error Codes
const PARTNER_ERROR_CODES = {
    LIST_FAILED: 'PARTNER_E001',
    CREATE_FORM_FAILED: 'PARTNER_E002',
    CREATE_FAILED: 'PARTNER_E003',
    UPDATE_FAILED: 'PARTNER_E004',
    DELETE_FAILED: 'PARTNER_E005',
    NOT_FOUND: 'PARTNER_E006',
    CATEGORY_ERROR: 'PARTNER_E007',
    API_ERROR: 'PARTNER_E008',
    CATEGORY_LIST_FAILED: 'PARTNER_E009',
    CATEGORY_CREATE_FAILED: 'PARTNER_E010',
    CATEGORY_UPDATE_FAILED: 'PARTNER_E011',
    CATEGORY_DELETE_FAILED: 'PARTNER_E012',
    CATEGORY_HAS_PARTNERS: 'PARTNER_E013',
    CATEGORY_NOT_FOUND: 'PARTNER_E014',
    INVALID_FORM_DATA: 'PARTNER_E015',
    FRONTEND_DATA_FAILED: 'PARTNER_E016'
};

// Contact Management Error Codes
const CONTACT_ERROR_CODES = {
    SUBMISSION_FAILED: 'CONTACT_E001',
    LIST_FAILED: 'CONTACT_E002',
    VIEW_FAILED: 'CONTACT_E003',
    DELETE_FAILED: 'CONTACT_E004',
    CATEGORY_ERROR: 'CONTACT_E005',
    AGREEMENT_ERROR: 'CONTACT_E006',
    EMAIL_ERROR: 'CONTACT_E007',
    STATUS_UPDATE_ERROR: 'CONTACT_E008'
};

// Visit Management Error Codes
const VISIT_ERROR_CODES = {
    LIST_FAILED: 'VISIT_E001',
    CREATE_FAILED: 'VISIT_E002',
    UPDATE_FAILED: 'VISIT_E003',
    DELETE_FAILED: 'VISIT_E004',
    NOT_FOUND: 'VISIT_E005',
    DATE_INVALID: 'VISIT_E006',
    STATUS_UPDATE_ERROR: 'VISIT_E007',
    PERMISSION_ERROR: 'VISIT_E008',
    MOU_ERROR: 'VISIT_E009'
};

// Frontend Display Error Codes
const FRONTEND_ERROR_CODES = {
    RENDER_FAILED: 'FRONTEND_E001',
    DATA_FETCH_FAILED: 'FRONTEND_E002',
    LANGUAGE_ERROR: 'FRONTEND_E003',
    NAVIGATION_ERROR: 'FRONTEND_E004',
    CONTENT_NOT_FOUND: 'FRONTEND_E005',
    TEMPLATE_ERROR: 'FRONTEND_E006',
    RESOURCE_ERROR: 'FRONTEND_E007',
    SEO_ERROR: 'FRONTEND_E008'
};

// Frontpage Management Error Codes
const FRONTPAGE_ERROR_CODES = {
    LIST_FAILED: 'FRONTPAGE_E001',
    CREATE_FORM_FAILED: 'FRONTPAGE_E002',
    CREATE_FAILED: 'FRONTPAGE_E003',
    EDIT_FORM_FAILED: 'FRONTPAGE_E004',
    UPDATE_FAILED: 'FRONTPAGE_E005',
    DELETE_FAILED: 'FRONTPAGE_E006',
    NOT_FOUND: 'FRONTPAGE_E007',
    UPLOAD_FAILED: 'FRONTPAGE_E008',
    FILE_DELETION_FAILED: 'FRONTPAGE_E009',
    CATEGORY_LIST_FAILED: 'FRONTPAGE_E010',
    CATEGORY_CREATE_FAILED: 'FRONTPAGE_E011',
    CATEGORY_UPDATE_FAILED: 'FRONTPAGE_E012',
    CATEGORY_DELETE_FAILED: 'FRONTPAGE_E013',
    CATEGORY_IN_USE: 'FRONTPAGE_E014'
};

// Site Settings Error Codes
const SITE_SETTINGS_ERROR_CODES = {
    RENDER_FAILED: 'SITE_SETTINGS_E001',
    SAVE_FAILED: 'SITE_SETTINGS_E002',
    UPLOAD_FAILED: 'SITE_SETTINGS_E003',
    FETCH_FAILED: 'SITE_SETTINGS_E004'
};

// Promotion Error Codes
const PROMOTION_ERROR_CODES = {
    CATEGORY_LIST_FAILED: 'PROMOTION_E001',
    CATEGORY_CREATE_FAILED: 'PROMOTION_E002',
    CATEGORY_EDIT_FAILED: 'PROMOTION_E003',
    CATEGORY_UPDATE_FAILED: 'PROMOTION_E004',
    CATEGORY_DELETE_FAILED: 'PROMOTION_E005',
    CATEGORY_EXISTS: 'PROMOTION_E006',
    CATEGORY_NOT_FOUND: 'PROMOTION_E007',
    ITEM_LIST_FAILED: 'PROMOTION_E008',
    ITEM_CREATE_FORM_FAILED: 'PROMOTION_E009',
    ITEM_CREATE_FAILED: 'PROMOTION_E010',
    ITEM_EDIT_FAILED: 'PROMOTION_E011',
    ITEM_UPDATE_FAILED: 'PROMOTION_E012',
    ITEM_DELETE_FAILED: 'PROMOTION_E013',
    ITEM_EXISTS: 'PROMOTION_E014',
    ITEM_NOT_FOUND: 'PROMOTION_E015',
    INVALID_CATEGORY_ID: 'PROMOTION_E016',
    IMAGE_PROCESSING_ERROR: 'PROMOTION_E017',
    FRONTEND_RENDER_FAILED: 'PROMOTION_E018',
    EMBED_PARSE_ERROR: 'PROMOTION_E019'
};

// Project Management Error Codes
const PROJECT_ERROR_CODES = {
    LIST_FAILED: 'PROJECT_E001',
    CREATE_FORM_FAILED: 'PROJECT_E002',
    CREATE_FAILED: 'PROJECT_E003',
    NAME_EXISTS: 'PROJECT_E004',
    EDIT_FORM_FAILED: 'PROJECT_E005',
    NOT_FOUND: 'PROJECT_E006',
    UPDATE_FAILED: 'PROJECT_E007',
    DELETE_FAILED: 'PROJECT_E008',
    HAS_DEPENDENCIES: 'PROJECT_E009',
    API_FETCH_FAILED: 'PROJECT_E010'
};

// About Page Error Codes
const ABOUT_ERROR_CODES = {
    LIST_FAILED: 'ABOUT_E001',
    CREATE_FORM_FAILED: 'ABOUT_E002',
    CREATE_FAILED: 'ABOUT_E003',
    EDIT_FORM_FAILED: 'ABOUT_E004',
    NOT_FOUND: 'ABOUT_E005',
    UPDATE_FAILED: 'ABOUT_E006',
    DELETE_FAILED: 'ABOUT_E007',
    UPLOAD_FAILED: 'ABOUT_E008',
    FILE_DELETION_FAILED: 'ABOUT_E009',
    FRONTEND_RENDER_FAILED: 'ABOUT_E010',
    CONTENT_PROCESSING_ERROR: 'ABOUT_E011'
};

// Dashboard Error Codes
const DASHBOARD_ERROR_CODES = {
    LOAD_FAILED: 'DASHBOARD_E001',
    STATS_FETCH_FAILED: 'DASHBOARD_E002',
    RECENT_ITEMS_FAILED: 'DASHBOARD_E003',
    VISITOR_STATS_FAILED: 'DASHBOARD_E004',
    RENDER_FAILED: 'DASHBOARD_E005'
};

// Link Management Error Codes
const LINK_ERROR_CODES = {
    LIST_FAILED: 'LINK_E001',
    CREATE_FAILED: 'LINK_E002',
    UPDATE_FAILED: 'LINK_E003',
    DELETE_FAILED: 'LINK_E004',
    NOT_FOUND: 'LINK_E005',
    TOGGLE_STATUS_FAILED: 'LINK_E006',
    FORM_LOAD_FAILED: 'LINK_E007',
    IMAGE_PROCESSING_ERROR: 'LINK_E008'
};

// FAQ Management Error Codes
const FAQ_ERROR_CODES = {
    LIST_CATEGORIES_FAILED: 'FAQ_E001',
    CREATE_CATEGORY_FAILED: 'FAQ_E002',
    UPDATE_CATEGORY_FAILED: 'FAQ_E003',
    DELETE_CATEGORY_FAILED: 'FAQ_E004',
    CATEGORY_NOT_FOUND: 'FAQ_E005',
    CATEGORY_EXISTS: 'FAQ_E006',
    RENDER_FORM_FAILED: 'FAQ_E007',
    LIST_ITEMS_FAILED: 'FAQ_E008',
    CREATE_ITEM_FAILED: 'FAQ_E009',
    UPDATE_ITEM_FAILED: 'FAQ_E010',
    DELETE_ITEM_FAILED: 'FAQ_E011',
    ITEM_NOT_FOUND: 'FAQ_E012',
    CONTENT_PROCESSING_ERROR: 'FAQ_E013',
    INVALID_STATUS: 'FAQ_E014',
    FRONTEND_DISPLAY_FAILED: 'FAQ_E015'
};

// Download Management Error Codes
const DOWNLOAD_ERROR_CODES = {
    LIST_FAILED: 'DOWNLOAD_E001',
    CREATE_FORM_FAILED: 'DOWNLOAD_E002',
    CREATE_FAILED: 'DOWNLOAD_E003',
    EDIT_FORM_FAILED: 'DOWNLOAD_E004',
    UPDATE_FAILED: 'DOWNLOAD_E005',
    DELETE_FAILED: 'DOWNLOAD_E006',
    NOT_FOUND: 'DOWNLOAD_E007',
    FILE_NOT_FOUND: 'DOWNLOAD_E008',
    FILE_ACCESS_ERROR: 'DOWNLOAD_E009',
    MISSING_FILE: 'DOWNLOAD_E010',
    INVALID_ID: 'DOWNLOAD_E011',
    FRONTEND_LIST_FAILED: 'DOWNLOAD_E012',
    DOWNLOAD_FAILED: 'DOWNLOAD_E013',
    FILE_SYSTEM_ERROR: 'DOWNLOAD_E014',
    CATEGORY_LOAD_FAILED: 'DOWNLOAD_E015'
};

// Download Category Error Codes
const DOWNLOAD_CATEGORY_ERROR_CODES = {
    LIST_FAILED: 'DOWNLOAD_CATEGORY_E001',
    CREATE_FORM_FAILED: 'DOWNLOAD_CATEGORY_E002',
    CREATE_FAILED: 'DOWNLOAD_CATEGORY_E003',
    EDIT_FORM_FAILED: 'DOWNLOAD_CATEGORY_E004',
    UPDATE_FAILED: 'DOWNLOAD_CATEGORY_E005',
    DELETE_FAILED: 'DOWNLOAD_CATEGORY_E006',
    NOT_FOUND: 'DOWNLOAD_CATEGORY_E007',
    INVALID_ID: 'DOWNLOAD_CATEGORY_E008'
};

// Export all error codes
module.exports = {
    COMMON_ERROR_CODES,
    AUTH_ERROR_CODES,
    USER_ERROR_CODES,
    CATEGORY_ERROR_CODES,
    CATEGORY_PERMISSION_ERROR_CODES,
    MEDIA_ERROR_CODES,
    ARTICLE_ERROR_CODES,
    NEWS_ERROR_CODES,
    PAGE_ERROR_CODES,
    PAGE_IMAGE_ERROR_CODES,
    BANNER_ERROR_CODES,
    PLATFORM_ERROR_CODES,
    PARTNER_ERROR_CODES,
    CONTACT_ERROR_CODES,
    VISIT_ERROR_CODES,
    FRONTEND_ERROR_CODES,
    FRONTPAGE_ERROR_CODES,
    SITE_SETTINGS_ERROR_CODES,
    PROMOTION_ERROR_CODES,
    PROJECT_ERROR_CODES,
    ABOUT_ERROR_CODES,
    DASHBOARD_ERROR_CODES,
    LINK_ERROR_CODES,
    FAQ_ERROR_CODES,
    DOWNLOAD_ERROR_CODES,
    DOWNLOAD_CATEGORY_ERROR_CODES
}; 