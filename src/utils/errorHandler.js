/**
 * Error Handler Utility
 * 
 * This module provides standardized error handling functions for the application.
 * It integrates with the error codes system and logger to provide consistent
 * error handling across the application.
 */
const logger = require('../config/logger');
const { COMMON_ERROR_CODES } = require('../config/errorCodes');

/**
 * <PERSON>les controller errors in a standardized way
 * 
 * @param {Error} error - The error that occurred
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {string} errorCode - Error code from the errorCodes module
 * @param {string} userMessage - Message to display to the user
 * @param {string} redirectPath - Path to redirect to
 * @param {Object} metadata - Additional metadata to log with the error
 */
const handleControllerError = (error, req, res, errorCode, userMessage, redirectPath, metadata = {}) => {
    // Log the error with error code and additional context
    logger.error(`${errorCode}: ${error.message}`, {
        error,
        stack: error.stack,
        path: req.path,
        method: req.method,
        userId: req.session?.user?.id,
        userRole: req.session?.user?.role,
        ...metadata
    });

    // Add error code to user message if in development mode
    const displayMessage = process.env.NODE_ENV === 'production' 
        ? userMessage 
        : `${userMessage} [${errorCode}]`;

    // Flash error message for user
    req.flash('error_msg', displayMessage);

    // Redirect to specified path
    res.redirect(redirectPath);
};

/**
 * Handles API errors in a standardized way
 * 
 * @param {Error} error - The error that occurred
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {string} errorCode - Error code from the errorCodes module
 * @param {string} userMessage - Message to display to the user
 * @param {number} statusCode - HTTP status code to return
 * @param {Object} metadata - Additional metadata to log with the error
 */
const handleApiError = (error, req, res, errorCode, userMessage, statusCode = 500, metadata = {}) => {
    // Log the error with error code and additional context
    logger.error(`${errorCode}: ${error.message}`, {
        error,
        stack: error.stack,
        path: req.path,
        method: req.method,
        userId: req.session?.user?.id,
        userRole: req.session?.user?.role,
        ...metadata
    });

    // Return JSON response with error details
    return res.status(statusCode).json({
        success: false,
        errorCode: errorCode,
        message: userMessage,
        // Include detailed error in development mode only
        ...(process.env.NODE_ENV !== 'production' ? { details: error.message } : {})
    });
};

/**
 * Handles frontend errors in a standardized way
 * 
 * @param {Error} error - The error that occurred
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {string} errorCode - Error code from the errorCodes module
 * @param {string} userMessage - Message to display to the user
 * @param {string} template - Template to render
 * @param {Object} templateData - Data to pass to the template
 */
const handleFrontendError = (error, req, res, errorCode, userMessage, template, templateData = {}) => {
    // Get language from request params or default to 'en'
    const language = req.params?.language || 'en';
    
    // Log the error with error code and additional context
    logger.error(`${errorCode}: ${error.message}`, {
        error,
        stack: error.stack,
        path: req.path,
        language,
        ...templateData
    });

    // Set default layout for frontend errors
    res.locals.layout = 'layouts/frontend';

    // Determine title based on language
    const title = language === 'en' ? 'Error' : '錯誤';
    
    // Render error page with appropriate message
    res.status(500).render(template || 'frontend/error', {
        title,
        message: userMessage,
        language,
        layout: 'layouts/frontend',
        error: process.env.NODE_ENV === 'production' ? {} : error,
        ...templateData
    });
};

/**
 * Creates a database error handler with a specific error code
 * 
 * @param {string} operation - Description of the database operation
 * @param {string} errorCode - Error code to use
 * @returns {Function} Error handler function
 */
const createDatabaseErrorHandler = (operation, errorCode = COMMON_ERROR_CODES.DATABASE_ERROR) => {
    return (error) => {
        logger.error(`${errorCode}: Database error during ${operation}:`, {
            error: error.message,
            stack: error.stack,
            operation
        });
        throw new Error(`Database error during ${operation}: ${error.message}`);
    };
};

module.exports = {
    handleControllerError,
    handleApiError,
    handleFrontendError,
    createDatabaseErrorHandler
}; 