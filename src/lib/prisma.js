const { PrismaClient } = require('@prisma/client');

// Check if metrics are enabled from environment variable
const isMetricsEnabled = process.env.ENABLE_METRICS === 'true';
const isProdEnv = process.env.NODE_ENV === 'production';
const logQueries = process.env.PRISMA_LOG_QUERIES === 'true';

// Configure Prisma log levels based on environment
let logLevels = [];

// In production, only log warnings and errors unless queries are explicitly enabled
if (isProdEnv) {
  logLevels = ['warn', 'error'];
  
  // Only add query logging if explicitly enabled in production
  if (logQueries) {
    logLevels.push('query');
  }
} else {
  // In development, log everything by default
  logLevels = ['query', 'info', 'warn', 'error'];
}

const prisma = new PrismaClient({
    log: logLevels,
});

// Check if Metrics tracking should be enabled
const enableMetrics = () => {
    // If metrics are disabled in the environment, don't proceed
    if (!isMetricsEnabled) {
        console.log('Database metrics are disabled via ENABLE_METRICS environment variable');
        return prisma;
    }
    
    try {
        // Dynamically require the metrics middleware to avoid circular dependencies
        const { trackDatabaseMetrics } = require('../middleware/databaseMetricsMiddleware');
        return trackDatabaseMetrics(prisma);
    } catch (error) {
        // If metrics middleware is not available, return the original prisma client
        console.error('Failed to enable database metrics:', error.message);
        return prisma;
    }
};

// Export the prisma client, which will be wrapped with metrics when that module is loaded
module.exports = prisma;

// Add a method to enable metrics after initialization
module.exports.enableMetrics = enableMetrics;
