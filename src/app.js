require('dotenv').config();
const express = require('express');
const session = require('express-session');
const flash = require('connect-flash');
const path = require('path');
const logger = require('./config/logger');
const prisma = require('./lib/prisma');
const expressLayouts = require('express-ejs-layouts');
const methodOverride = require('method-override');
const cookieParser = require('cookie-parser');
const helmet = require('helmet');
const { setLocals, ensureVisitCount } = require('./middleware/viewMiddleware');
const { languageMiddleware } = require('./middleware/languageMiddleware');
const { languageRouteMiddleware, addLanguageHelpers } = require('./middleware/languageRouteMiddleware');
const { attachPageImage } = require('./middleware/pageImageMiddleware');
const { trackVisits } = require('./middleware/visitCounterMiddleware');
const siteSettingsMiddleware = require('./middleware/siteSettings');
const httpLoggingMiddleware = require('./middleware/httpLoggingMiddleware');
// Import block suspicious paths middleware
const blockSuspiciousPathsMiddleware = require('./middleware/blockSuspiciousPathsMiddleware');
// Import Prometheus metrics middleware
const { metricsMiddleware, register, enableMetrics } = require('./config/metrics');
// Import HTTP metrics middleware
const { httpMetricsMiddleware } = require('./middleware/httpMetricsMiddleware');

// Import routes
const frontendRoutes = require('./routes/frontend');
const adminRoutes = require('./routes/admin');
const apiRoutes = require('./routes/api');

const ekitaiapp = express();

ekitaiapp.use(helmet({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'", "http:", "https:"],
            scriptSrc: ["'self'", "'unsafe-inline'", "http:", "https:"],
            scriptSrcAttr: ["'unsafe-inline'"],
            styleSrc: ["'self'", "'unsafe-inline'", "http:", "https:"],
            imgSrc: ["'self'", "data:", "http:", "https:"],
            upgradeInsecureRequests: null // Disable automatic HTTPS upgrade (Safari browser only)
        },
    }
}));

ekitaiapp.disable('x-powered-by');

// Trust proxies to get real client IP when behind a proxy/load balancer
// This is critical for x-forwarded-for headers to be trusted

const trustProxySetting = process.env.TRUST_PROXY || 'true';

// Parse the trust proxy setting based on type
let trustProxyValue;
if (trustProxySetting === 'true') {
    trustProxyValue = true;
} else if (trustProxySetting === 'false') {
    trustProxyValue = false;
} else if (!isNaN(Number(trustProxySetting))) {
    // If it's a number (e.g. "1"), convert it
    trustProxyValue = Number(trustProxySetting);
} else if (trustProxySetting && trustProxySetting.includes(',')) {
    // If it's a comma-separated list, convert to array
    trustProxyValue = trustProxySetting.split(',').map(ip => ip.trim());
} else {
    // Use the string value or default to true
    trustProxyValue = trustProxySetting || true;
}

// Apply the trust proxy setting
ekitaiapp.set('trust proxy', trustProxyValue);

// Log the applied setting
console.log(`Express 'trust proxy' set to: ${JSON.stringify(trustProxyValue)}`);

// View engine setup
ekitaiapp.set('views', path.join(__dirname, 'views'));
ekitaiapp.set('view engine', 'ejs');

// Express layouts setup
ekitaiapp.use(expressLayouts);
ekitaiapp.set('layout', 'layouts/admin');
ekitaiapp.set('layout extractScripts', true);
ekitaiapp.set('layout extractStyles', true);

// Early middleware for parsing and logging
ekitaiapp.use(express.json({ limit: '200mb' }));
ekitaiapp.use(express.urlencoded({ extended: true, limit: '200mb' }));
ekitaiapp.use(cookieParser());

// Setup Prometheus metrics endpoint before any other middleware that would count visits or render pages
if (enableMetrics) {
    if (process.env.NODE_ENV === 'production') {
        // In production, require basic auth for metrics
        ekitaiapp.get('/metrics', (req, res, next) => {
            const auth = req.headers.authorization;

            if (!auth || !auth.startsWith('Basic ')) {
                res.setHeader('WWW-Authenticate', 'Basic realm="Metrics"');
                return res.status(401).send('Authentication required');
            }

            const credentials = Buffer.from(auth.split(' ')[1], 'base64').toString().split(':');
            const username = credentials[0];
            const password = credentials[1];

            if (username !== process.env.METRICS_USER || password !== process.env.METRICS_PASSWORD) {
                return res.status(403).send('Invalid credentials');
            }

            // If authentication is successful, proceed to metrics
            res.setHeader('Content-Type', register.contentType);
            register.metrics().then(async data => {
                // Get Prisma metrics if available
                let prismaMetrics = '';
                if (prisma.getPrometheusMetrics) {
                    prismaMetrics = await prisma.getPrometheusMetrics();
                }

                // Combine the metrics
                res.send(data + '\n' + prismaMetrics);
            });
        });
    } else {
        // In development, expose metrics without authentication
        ekitaiapp.get('/metrics', async (req, res) => {
            res.setHeader('Content-Type', register.contentType);

            // Get standard metrics
            const metrics = await register.metrics();

            // Get Prisma metrics if available
            let prismaMetrics = '';
            if (prisma.getPrometheusMetrics) {
                prismaMetrics = await prisma.getPrometheusMetrics();
            }

            // Combine the metrics
            res.send(metrics + '\n' + prismaMetrics);
        });
    }
}

// Add Prometheus metrics middleware (must be before route handlers for accurate metrics)
ekitaiapp.use(metricsMiddleware);

// Add HTTP metrics middleware for status codes and response times
ekitaiapp.use(httpMetricsMiddleware);

// HTTP logging middleware - should be early in the chain to capture all requests
ekitaiapp.use(httpLoggingMiddleware);

// Block suspicious paths middleware - add early to prevent unnecessary processing
ekitaiapp.use(blockSuspiciousPathsMiddleware);

// Serve static files - keep only one static file serving configuration
ekitaiapp.use(express.static(path.join(__dirname, '../public')));

// Method override middleware - must be before route handlers
ekitaiapp.use(methodOverride('_method'));

// Session middleware
ekitaiapp.use(session({
    secret: process.env.SESSION_SECRET,
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: process.env.NODE_ENV === 'production' && process.env.SECURE_COOKIES === 'true',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
}));

// Flash messages
ekitaiapp.use(flash());

// Pass user and flash messages to all views
ekitaiapp.use((req, res, next) => {
    res.locals.success_msg = req.flash('success_msg');
    res.locals.error_msg = req.flash('error_msg');
    res.locals.error = req.flash('error');
    res.locals.user = req.session.user || null;
    next();
});

// Language route middleware - must be after session middleware and before language middleware
ekitaiapp.use(languageRouteMiddleware);

// Language middleware - must be after session middleware
ekitaiapp.use(languageMiddleware);

// Add language helper functions to res.locals
ekitaiapp.use(addLanguageHelpers);

// Load site settings for all views
ekitaiapp.use(siteSettingsMiddleware);

// Attach page image to frontend routes
ekitaiapp.use(attachPageImage);

// Track website visits
ekitaiapp.use(trackVisits);

// Ensure visitCount is defined for all views
ekitaiapp.use(ensureVisitCount);

// Global variables
ekitaiapp.use((req, res, next) => {
    res.locals.currentUser = req.session.user || null;
    next();
});

// Layout middleware - Set different layouts for admin and frontend
ekitaiapp.use((req, res, next) => {
    // Set admin layout for admin routes
    if (req.path.startsWith('/admin')) {
        res.locals.layout = 'layouts/admin';
    } else {
        // Set frontend layout for all other routes
        res.locals.layout = 'layouts/frontend';
    }
    next();
});

// Routes
ekitaiapp.use('/admin', adminRoutes);
ekitaiapp.use('/api', apiRoutes);
ekitaiapp.use('/', frontendRoutes);

// 404 handler
ekitaiapp.use((req, res) => {
    // Set the layout based on the route
    const layout = req.path.startsWith('/admin') ? 'layouts/admin' : 'layouts/frontend';
    res.locals.layout = layout;

    res.status(404).render('frontend/404', {
        title: '404 Not Found',
        layout: layout
    });
});

// Error handling middleware
ekitaiapp.use((err, req, res, next) => {
    // Use request-aware logger if available
    const reqLogger = logger.withRequest ? logger.withRequest(req, res) : logger;

    // Log the error only once with ekitaiappropriate detail level
    if (process.env.NODE_ENV === 'development') {
        reqLogger.error('Unhandled error:', {
            message: err.message,
            stack: err.stack,
            path: req.path
        });
    } else {
        reqLogger.error('Unhandled error:', {
            message: err.message,
            path: req.path
        });
    }

    // Set the layout based on the route
    const layout = req.path.startsWith('/admin') ? 'layouts/admin' : 'layouts/frontend';
    res.locals.layout = layout;

    res.status(500).render('error', {
        title: 'Error',
        message: 'An unexpected error occurred',
        error: process.env.NODE_ENV === 'development' ? err : {},
        layout: layout
    });
});

// Start server
const startekitaiapp = async () => {
    try {
        // Test database connection
        if (!prisma.$connect()) {
            throw new Error('Failed to connect to database');
        }

        // Enable Prisma metrics collection after connection is established
        if (enableMetrics) {
            prisma.enableMetrics();
        }

        logger.info('Database connection established', {
            service: process.env.SERVICE_NAME
        });

        // Initialize scheduler for cron jobs
        const { initScheduler } = require('./config/scheduler');
        initScheduler();

        // Start server
        const port = process.env.PORT || 3001;
        ekitaiapp.listen(port, () => {
            logger.info('Server started', {
                port,
                environment: process.env.NODE_ENV,
                service: process.env.SERVICE_NAME
            });

            if (enableMetrics) {
                logger.info('Prometheus metrics available at /metrics endpoint');
            }
        });
    } catch (error) {
        logger.error('Failed to start ekitaiapplication', {
            error: error.message,
            stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        });
        process.exit(1);
    }
};

// Handle graceful shutdown
process.on('SIGTERM', async () => {
    logger.info('Shutting down gracefully', {
        service: process.env.SERVICE_NAME,
        event: 'shutdown',
        signal: 'SIGTERM'
    });
    await prisma.$disconnect();
    process.exit(0);
});

if (require.main === module) {
    startekitaiapp();
}

module.exports = ekitaiapp;