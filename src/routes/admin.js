const express = require('express');
const router = express.Router();
const multer = require('multer');
const path = require('path');
const { body } = require('express-validator');
const { isAuthenticated, hasRole, hasPermission, isOwnerOrHasPermission } = require('../middleware/auth');
const { handleUploadError } = require('../middleware/uploadMiddleware');
const prisma = require('../lib/prisma');
const logger = require('../config/logger');

// Controllers
const authController = require('../controllers/authController');
const userController = require('../controllers/userController');
const articleController = require('../controllers/articleController');
const mediaController = require('../controllers/mediaController');
const dashboardController = require('../controllers/dashboardController');
const categoryController = require('../controllers/categoryController');
const categoryPermissionController = require('../controllers/categoryPermissionController');
const bannerController = require('../controllers/bannerController');
const pageController = require('../controllers/pageController');
const pageImageController = require('../controllers/pageImageController');
const faqController = require('../controllers/faqController');
const downloadController = require('../controllers/downloadController');
const downloadCategoryController = require('../controllers/downloadCategoryController');
const downloadGroupController = require('../controllers/downloadGroupController');
const newsController = require('../controllers/newsController');
const promotionController = require('../controllers/promotionController');
const promotionCategoryController = require('../controllers/promotionCategoryController');
const promotionGroupController = require('../controllers/promotionGroupController');
const aboutController = require('../controllers/aboutController');
const contactCategoryController = require('../controllers/contactCategoryController');
const contactController = require('../controllers/contactController');
const linksController = require('../controllers/linksController');
const platformController = require('../controllers/platformController');
const platformUpload = require('../middleware/platformImageUpload');
const siteSettingsController = require('../controllers/siteSettingsController');
const siteLogoUpload = require('../middleware/siteLogoUpload');
const partnerController = require('../controllers/partnerController');
const contactAgreementController = require('../controllers/contactAgreementController');
const projectController = require('../controllers/projectController');
const visitController = require('../controllers/visitController');

// Import frontpage routes
const frontpageRoutes = require('./admin/frontpage');

// Multer configuration
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        cb(null, 'uploads/');
    },
    filename: (req, file, cb) => {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        cb(null, uniqueSuffix + path.extname(file.originalname));
    }
});

const upload = multer({
    storage,
    limits: {
        fileSize: process.env.MAX_FILE_SIZE || 5 * 1024 * 1024 // 5MB default
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('Invalid file type'));
        }
    }
});

// Public routes (no auth required)
router.get('/login', authController.renderLoginForm);
router.post('/login', authController.login);
router.get('/logout', authController.logout);

// Guest login routes
router.get('/guest-login', authController.renderGuestLoginForm);
router.post('/guest-login', authController.guestLogin);

// Protected routes
router.use(isAuthenticated);

// Add path to res.locals for active menu highlighting
router.use((req, res, next) => {
    res.locals.path = req.path;
    next();
});

// Default admin route - accessible to all authenticated users
router.get('/', isAuthenticated, (req, res) => {
    // Redirect super_admin, admin, and editor to dashboard if they have access
    if (req.session.user.role === 'super_admin' ||
        req.session.user.role === 'admin' ||
        req.session.user.role === 'editor') {
        return res.redirect('/admin/dashboard');
    }

    // Redirect visitor to visits list
    if (req.session.user.role === 'visitor') {
        return res.redirect('/admin/visits');
    }

    // Otherwise show a simple welcome page
    res.render('admin/welcome', {
        title: 'Admin Panel',
        layout: 'layouts/admin',
        user: req.session.user
    });
});

// Dashboard
router.get('/dashboard', hasRole(['super_admin', 'admin', 'editor', 'guest']), dashboardController.renderDashboard);

// Article routes
router.get('/articles', hasRole(['super_admin', 'admin']), articleController.listArticles);
router.get('/articles/create', hasRole(['super_admin', 'admin']), articleController.renderCreateForm);
router.post('/articles', hasRole(['super_admin', 'admin']), articleController.createArticle);
router.get('/articles/edit/:id', hasRole(['super_admin', 'admin']), articleController.renderEditForm);
router.post('/articles/:id', hasRole(['super_admin', 'admin']), articleController.updateArticle);
router.post('/articles/:id/delete', hasRole(['super_admin', 'admin']), articleController.deleteArticle);

// Category routes
router.get('/categories', hasRole(['super_admin', 'admin']), categoryController.listCategories);
router.get('/categories/create', hasRole(['super_admin', 'admin']), categoryController.renderCreateCategory);
router.post('/categories', hasRole(['super_admin', 'admin']), categoryController.createCategory);
router.get('/categories/edit/:id', hasRole(['super_admin', 'admin']), categoryController.renderEditCategory);
router.post('/categories/:id', hasRole(['super_admin', 'admin']), categoryController.updateCategory);
router.post('/categories/:id/delete', hasRole(['super_admin', 'admin']), categoryController.deleteCategory);

// Media routes
router.get('/media', hasRole(['super_admin', 'admin']), mediaController.listMedia);
router.post('/media/upload', hasRole(['super_admin', 'admin']), upload.single('file'), handleUploadError, mediaController.uploadMedia);
router.get('/media/:id', hasRole(['super_admin', 'admin']), mediaController.getMediaDetails);
router.post('/media/:id/delete', hasRole(['super_admin', 'admin']), mediaController.deleteMedia);

// Banner routes
const bannerUpload = require('../middleware/bannerUpload');

router.get('/banners', hasRole(['super_admin', 'admin']), bannerController.listBanners);
router.get('/banners/create', hasRole(['super_admin', 'admin']), bannerController.renderCreateBanner);
router.post('/banners', hasRole(['super_admin', 'admin']), bannerUpload, bannerController.createBanner);
router.get('/banners/edit/:id', hasRole(['super_admin', 'admin']), bannerController.renderEditBanner);
router.post('/banners/:id', hasRole(['super_admin', 'admin']), bannerUpload, bannerController.updateBanner);
router.post('/banners/:id/delete', hasRole(['super_admin', 'admin']), bannerController.deleteBanner);
router.delete('/banners/:id', hasRole(['super_admin', 'admin']), bannerController.deleteBanner);

// Page routes
const pageAttachmentUpload = require('../middleware/pageAttachmentUpload');
const pageImageUpload = require('../middleware/pageImageUpload');

router.get('/pages', hasRole(['super_admin', 'admin']), pageController.listPages);
router.get('/pages/create', hasRole(['super_admin', 'admin']), pageController.renderCreatePage);
router.post('/pages', hasRole(['super_admin', 'admin']), pageAttachmentUpload, pageController.createPage);
router.get('/pages/edit/:slug', hasRole(['super_admin', 'admin']), pageController.renderEditPage);
router.post('/pages/:slug', hasRole(['super_admin', 'admin']), pageAttachmentUpload, pageController.updatePage);
router.put('/pages/:slug', hasRole(['super_admin', 'admin']), pageAttachmentUpload, pageController.updatePage);
router.post('/pages/:id/delete', hasRole(['super_admin', 'admin']), pageController.deletePage);
router.delete('/pages/attachment/:attachmentId', hasRole(['super_admin', 'admin']), pageController.deleteAttachment);

// FAQ Category Routes
router.get('/faq/categories', hasRole(['super_admin', 'admin']), faqController.listCategories);
router.get('/faq/categories/create', hasRole(['super_admin', 'admin']), faqController.renderCreateCategory);
router.post('/faq/categories', hasRole(['super_admin', 'admin']), faqController.createCategory);
router.get('/faq/categories/edit/:id', hasRole(['super_admin', 'admin']), faqController.renderEditCategory);
router.post('/faq/categories/:id', hasRole(['super_admin', 'admin']), faqController.updateCategory);
router.post('/faq/categories/:id/delete', hasRole(['super_admin', 'admin']), faqController.deleteCategory);

// FAQ Item Routes - Accessible to editors
router.get('/faq/items', hasRole(['super_admin', 'admin', 'editor']), faqController.listItems);
router.get('/faq/items/create', hasRole(['super_admin', 'admin', 'editor']), faqController.renderCreateItem);
router.post('/faq/items', hasRole(['super_admin', 'admin', 'editor']), faqController.createItem);
router.get('/faq/items/edit/:id', hasRole(['super_admin', 'admin', 'editor']), faqController.renderEditItem);
router.post('/faq/items/:id', hasRole(['super_admin', 'admin', 'editor']), faqController.updateItem);
router.post('/faq/items/:id/delete', hasRole(['super_admin', 'admin', 'editor']), faqController.deleteItem);

// Download Routes
const downloadFileUpload = require('../middleware/downloadFileUpload');

// Download Category Routes
router.get('/downloads/categories', hasRole(['super_admin', 'admin']), downloadCategoryController.listCategories);
router.get('/downloads/categories/create', hasRole(['super_admin', 'admin']), downloadCategoryController.renderCreateCategory);
router.post('/downloads/categories', hasRole(['super_admin', 'admin']), downloadCategoryController.createCategory);
router.get('/downloads/categories/edit/:id', hasRole(['super_admin', 'admin']), downloadCategoryController.renderEditCategory);
router.post('/downloads/categories/:id', hasRole(['super_admin', 'admin']), downloadCategoryController.updateCategory);
router.post('/downloads/categories/:id/delete', hasRole(['super_admin', 'admin']), downloadCategoryController.deleteCategory);

// Download Group Routes
router.get('/downloads/groups', hasRole(['super_admin', 'admin']), downloadGroupController.listGroups);
router.get('/downloads/groups/create', hasRole(['super_admin', 'admin']), downloadGroupController.renderCreateGroup);
router.post('/downloads/groups', hasRole(['super_admin', 'admin']), downloadGroupController.createGroup);
router.get('/downloads/groups/edit/:id', hasRole(['super_admin', 'admin']), downloadGroupController.renderEditGroup);
router.post('/downloads/groups/:id', hasRole(['super_admin', 'admin']), downloadGroupController.updateGroup);
router.post('/downloads/groups/:id/delete', hasRole(['super_admin', 'admin']), downloadGroupController.deleteGroup);

// API Routes for dynamic loading
router.get('/api/groups/category/:categoryId', hasRole(['super_admin', 'admin', 'editor']), downloadGroupController.getGroupsByCategory);

// Download Item Routes
router.get('/downloads', hasRole(['super_admin', 'admin', 'editor']), downloadController.listDownloads);
router.get('/downloads/create', hasRole(['super_admin', 'admin', 'editor']), downloadController.renderCreateDownload);
router.post('/downloads', hasRole(['super_admin', 'admin', 'editor']), downloadFileUpload, downloadController.createDownload);
router.get('/downloads/edit/:id', hasRole(['super_admin', 'admin', 'editor']), downloadController.renderEditDownload);
router.post('/downloads/:id', hasRole(['super_admin', 'admin', 'editor']), downloadFileUpload, downloadController.updateDownload);
router.post('/downloads/:id/delete', hasRole(['super_admin', 'admin', 'editor']), downloadController.deleteDownload);
router.get('/downloads/:id/download', hasRole(['super_admin', 'admin', 'editor']), downloadController.adminDownloadFile);

// News Dashboard
router.get('/news', hasRole(['super_admin', 'admin', 'editor']), (req, res) => {
    res.render('admin/news/index', {
        title: 'News Management',
        success_msg: req.flash('success_msg'),
        error_msg: req.flash('error_msg')
    });
});

// News Category Routes
router.get('/news/categories', hasRole(['super_admin', 'admin']), newsController.listCategories);
router.get('/news/categories/create', hasRole(['super_admin', 'admin']), newsController.renderCreateCategory);
router.post('/news/categories', hasRole(['super_admin', 'admin']), newsController.createCategory);
router.get('/news/categories/edit/:id', hasRole(['super_admin', 'admin']), newsController.renderEditCategory);
router.post('/news/categories/:id', hasRole(['super_admin', 'admin']), newsController.updateCategory);
router.post('/news/categories/:id/delete', hasRole(['super_admin', 'admin']), newsController.deleteCategory);

// News Item Routes - Accessible to editors
const newsImageUpload = require('../middleware/newsImageUpload');

router.get('/news/items', hasRole(['super_admin', 'admin', 'editor']), newsController.listItems);
router.get('/news/items/create', hasRole(['super_admin', 'admin', 'editor']), newsController.renderCreateItem);
router.post('/news/items', hasRole(['super_admin', 'admin', 'editor']), newsImageUpload, newsController.createItem);
router.get('/news/items/edit/:id', hasRole(['super_admin', 'admin', 'editor']), newsController.renderEditItem);
router.post('/news/items/:id', hasRole(['super_admin', 'admin', 'editor']), newsImageUpload, newsController.updateItem);
router.post('/news/items/:id/delete', hasRole(['super_admin', 'admin', 'editor']), newsController.deleteItem);

// Promotions Dashboard
const promotionImageUpload = require('../middleware/promotionImageUpload');

router.get('/promotions', hasRole(['super_admin', 'admin']), (req, res) => {
    res.redirect('/admin/promotions/items');
});

// Promotion Category Routes
router.get('/promotions/categories', hasRole(['super_admin', 'admin']), promotionController.listCategories);
router.get('/promotions/categories/create', hasRole(['super_admin', 'admin']), promotionController.renderCreateCategory);
router.post('/promotions/categories', hasRole(['super_admin', 'admin']), promotionController.createCategory);
router.get('/promotions/categories/edit/:id', hasRole(['super_admin', 'admin']), promotionController.renderEditCategory);
router.post('/promotions/categories/:id', hasRole(['super_admin', 'admin']), promotionController.updateCategory);
router.post('/promotions/categories/:id/delete', hasRole(['super_admin', 'admin']), promotionController.deleteCategory);

// Promotion Item Routes
router.get('/promotions/items', hasRole(['super_admin', 'admin']), promotionController.listItems);
router.get('/promotions/items/create', hasRole(['super_admin', 'admin']), promotionController.renderCreateItem);
router.post('/promotions/items', hasRole(['super_admin', 'admin']), promotionImageUpload, promotionController.createItem);
router.get('/promotions/items/edit/:id', hasRole(['super_admin', 'admin']), promotionController.renderEditItem);
router.post('/promotions/items/:id', hasRole(['super_admin', 'admin']), promotionImageUpload, promotionController.updateItem);
router.post('/promotions/items/:id/delete', hasRole(['super_admin', 'admin']), promotionController.deleteItem);

// Promotion Category Routes
router.get('/promotions/categories', hasRole(['super_admin', 'admin']), promotionCategoryController.listCategories);
router.get('/promotions/categories/create', hasRole(['super_admin', 'admin']), promotionCategoryController.renderCreateCategory);
router.post('/promotions/categories', hasRole(['super_admin', 'admin']), promotionCategoryController.createCategory);
router.get('/promotions/categories/edit/:id', hasRole(['super_admin', 'admin']), promotionCategoryController.renderEditCategory);
router.post('/promotions/categories/:id', hasRole(['super_admin', 'admin']), promotionCategoryController.updateCategory);
router.post('/promotions/categories/:id/delete', hasRole(['super_admin', 'admin']), promotionCategoryController.deleteCategory);

// Promotion Group Routes
router.get('/promotions/groups', hasRole(['super_admin', 'admin']), promotionGroupController.listGroups);
router.get('/promotions/groups/create', hasRole(['super_admin', 'admin']), promotionGroupController.renderCreateGroup);
router.post('/promotions/groups', hasRole(['super_admin', 'admin']), promotionGroupController.createGroup);
router.get('/promotions/groups/edit/:id', hasRole(['super_admin', 'admin']), promotionGroupController.renderEditGroup);
router.post('/promotions/groups/:id', hasRole(['super_admin', 'admin']), promotionGroupController.updateGroup);
router.post('/promotions/groups/:id/delete', hasRole(['super_admin', 'admin']), promotionGroupController.deleteGroup);
router.get('/promotions/groups/by-category/:categoryId', hasRole(['super_admin', 'admin']), promotionGroupController.getGroupsByCategory);



// About Routes
router.get('/about', hasRole(['super_admin', 'admin']), aboutController.listItems);
router.get('/about/create', hasRole(['super_admin', 'admin']), aboutController.renderCreateItem);
router.post('/about', hasRole(['super_admin', 'admin']), aboutController.upload, aboutController.createItem);
router.get('/about/edit/:id', hasRole(['super_admin', 'admin']), aboutController.renderEditItem);
router.post('/about/:id', hasRole(['super_admin', 'admin']), aboutController.upload, aboutController.updateItem);
router.post('/about/:id/delete', hasRole(['super_admin', 'admin']), aboutController.deleteItem);

// Contact Category Routes
router.get('/contact/categories', hasRole(['super_admin', 'admin']), contactCategoryController.listCategories);
router.get('/contact/categories/create', hasRole(['super_admin', 'admin']), contactCategoryController.renderCreateCategory);
router.post('/contact/categories', hasRole(['super_admin', 'admin']), contactCategoryController.createCategory);
router.get('/contact/categories/edit/:id', hasRole(['super_admin', 'admin']), contactCategoryController.renderEditCategory);
router.post('/contact/categories/:id', hasRole(['super_admin', 'admin']), contactCategoryController.updateCategory);
router.post('/contact/categories/:id/delete', hasRole(['super_admin', 'admin']), contactCategoryController.deleteCategory);

// Contact Agreement Routes
router.get('/contact/agreement', hasRole(['super_admin', 'admin']), contactAgreementController.showAgreementEditor);
router.post('/contact/agreement', hasRole(['super_admin', 'admin']), contactAgreementController.updateAgreement);

// Project Routes
router.get('/projects', hasRole(['super_admin', 'admin']), projectController.listProjects);
router.get('/projects/create', hasRole(['super_admin', 'admin']), projectController.renderCreateForm);
router.post('/projects', hasRole(['super_admin', 'admin']), projectController.createProject);
router.get('/projects/edit/:id', hasRole(['super_admin', 'admin']), projectController.renderEditForm);
router.post('/projects/:id', hasRole(['super_admin', 'admin']), projectController.updateProject);
router.post('/projects/:id/delete', hasRole(['super_admin', 'admin']), projectController.deleteProject);

// API Routes
router.get('/api/projects', projectController.getProjects);

// Contact Routes - view and delete specific contact
router.get('/contact/view/:id', hasRole(['super_admin', 'admin']), contactController.viewContact);
router.post('/contact/:id/status', hasRole(['super_admin', 'admin']), contactController.updateContactStatus);
router.post('/contact/:id/delete', hasRole(['super_admin', 'admin']), contactController.deleteContact);

// Contact Routes - main list page (must be after other /contact/... routes)
router.get('/contact', hasRole(['super_admin', 'admin']), contactController.listContacts);

// Platform Routes
router.get('/platforms', hasRole(['super_admin', 'admin']), platformController.listItems);
router.get('/platforms/create', hasRole(['super_admin', 'admin']), platformController.renderCreateItem);
router.post('/platforms', hasRole(['super_admin', 'admin']), platformUpload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'imageEn', maxCount: 1 },
    { name: 'attachments', maxCount: 10 }
]), platformController.createItem);
router.get('/platforms/categories', hasRole(['super_admin', 'admin']), platformController.listCategories);
router.get('/platforms/categories/create', hasRole(['super_admin', 'admin']), platformController.renderCreateCategory);
router.post('/platforms/categories', hasRole(['super_admin', 'admin']), platformController.createCategory);
router.get('/platforms/categories/edit/:id', hasRole(['super_admin', 'admin']), platformController.renderEditCategory);
router.post('/platforms/categories/:id', hasRole(['super_admin', 'admin']), platformController.updateCategory);
router.post('/platforms/categories/:id/delete', hasRole(['super_admin', 'admin']), platformController.deleteCategory);
router.post('/platforms/attachments', hasRole(['super_admin', 'admin']), platformUpload.array('attachments', 10), platformController.uploadAttachment);
router.get('/platforms/edit/:id', hasRole(['super_admin', 'admin']), platformController.renderEditItem);
router.post('/platforms/:id', hasRole(['super_admin', 'admin']), platformUpload.fields([
    { name: 'image', maxCount: 1 },
    { name: 'imageEn', maxCount: 1 },
    { name: 'attachments', maxCount: 10 }
]), platformController.updateItem);
router.post('/platforms/:id/delete', hasRole(['super_admin', 'admin']), platformController.deleteItem);

// Partners Routes
router.get('/partners', hasRole(['super_admin', 'admin']), partnerController.listItems);
router.get('/partners/create', hasRole(['super_admin', 'admin']), partnerController.renderCreateItem);
router.post('/partners', hasRole(['super_admin', 'admin']), express.urlencoded({ extended: true }), partnerController.createItem);
router.get('/partners/categories', hasRole(['super_admin', 'admin']), partnerController.listCategories);
router.get('/partners/categories/create', hasRole(['super_admin', 'admin']), partnerController.renderCreateCategory);
router.post('/partners/categories', hasRole(['super_admin', 'admin']), partnerController.createCategory);
router.get('/partners/categories/edit/:id', hasRole(['super_admin', 'admin']), partnerController.renderEditCategory);
router.post('/partners/categories/:id', hasRole(['super_admin', 'admin']), partnerController.updateCategory);
router.put('/partners/categories/:id', hasRole(['super_admin', 'admin']), partnerController.updateCategory);
router.delete('/partners/categories/:id', hasRole(['super_admin', 'admin']), partnerController.deleteCategory);
router.get('/partners/:id/edit', hasRole(['super_admin', 'admin']), partnerController.renderEditItem);
router.post('/partners/:id', hasRole(['super_admin', 'admin']), express.urlencoded({ extended: true }), partnerController.updateItem);
router.post('/partners/:id/delete', hasRole(['super_admin', 'admin']), partnerController.deleteItem);

// Links Routes
const { uploadLinkImage } = require('../middleware/linkImageUpload');

router.get('/links', hasRole(['super_admin', 'admin']), linksController.index);
router.get('/links/create', hasRole(['super_admin', 'admin']), linksController.createForm);
router.post('/links', hasRole(['super_admin', 'admin']), uploadLinkImage, linksController.create);
router.get('/links/edit/:id', hasRole(['super_admin', 'admin']), linksController.editForm);
router.post('/links/:id', hasRole(['super_admin', 'admin']), uploadLinkImage, linksController.update);
router.post('/links/:id/delete', hasRole(['super_admin', 'admin']), linksController.delete);

// Frontpage Routes
router.use('/frontpage', hasRole(['super_admin', 'admin']), frontpageRoutes);

// Page Image Routes
router.get('/pageImages', hasRole(['super_admin', 'admin']), pageImageController.listPageImages);
router.get('/pageImages/create', hasRole(['super_admin', 'admin']), pageImageController.renderCreateForm);
router.post('/pageImages', hasRole(['super_admin', 'admin']), pageImageUpload, pageImageController.createPageImage);
router.get('/pageImages/edit/:id', hasRole(['super_admin', 'admin']), pageImageController.renderEditForm);
router.post('/pageImages/edit/:id', hasRole(['super_admin', 'admin']), pageImageUpload, pageImageController.updatePageImage);
router.get('/pageImages/delete/:id', hasRole(['super_admin', 'admin']), pageImageController.deletePageImage);
router.get('/pageImages/toggle/:id', hasRole(['super_admin', 'admin']), pageImageController.toggleStatus);

// Helper middleware to check if user is editing their own profile
const isOwnProfileOrAdmin = (req, res, next) => {
    // Super admins and admins can edit any profile
    if (req.session.user.role === 'super_admin' || req.session.user.role === 'admin') {
        return next();
    }

    // Editors and guests can only edit their own profile
    if ((req.session.user.role === 'editor' || req.session.user.role === 'guest') &&
        req.params.id && parseInt(req.params.id) === req.session.user.id) {
        return next();
    }

    // Unauthorized access
    req.flash('error_msg', '您沒有權限執行此操作');
    return res.redirect('/admin/dashboard');
};

// User Routes
router.get('/users', hasRole(['super_admin', 'admin']), userController.listUsers);
router.get('/users/new', hasRole(['super_admin', 'admin']), userController.renderCreateUser);
router.post('/users', hasRole(['super_admin', 'admin']), userController.createUser);
router.get('/users/edit/:id', isOwnProfileOrAdmin, userController.renderEditUser);
router.post('/users/:id', isOwnProfileOrAdmin, userController.updateUser);
router.put('/users/:id', isOwnProfileOrAdmin, userController.updateUser);
router.post('/users/:id/delete', hasRole(['super_admin', 'admin']), userController.deleteUser);

// Site Settings routes
router.get('/site-settings', hasRole(['super_admin', 'admin']), siteSettingsController.renderSiteSettingsForm);
router.post('/site-settings', hasRole(['super_admin', 'admin']), siteLogoUpload, siteSettingsController.saveSiteSettings);

// Visits routes
router.get('/visits', hasRole(['super_admin', 'admin', 'guest', 'editor', 'visitor']), visitController.listVisits);
router.get('/visits/create', hasRole(['super_admin', 'admin', 'guest', 'editor']), visitController.renderCreateForm);
router.post('/visits', hasRole(['super_admin', 'admin', 'guest', 'editor']), visitController.createVisit);
router.get('/visits/view/:id', hasRole(['super_admin', 'admin', 'guest', 'editor', 'visitor']), visitController.viewVisit);
router.get('/visits/edit/:id', hasRole(['super_admin', 'admin', 'guest', 'editor']), visitController.renderEditForm);
router.post('/visits/:id', hasRole(['super_admin', 'admin', 'guest', 'editor']), visitController.updateVisit);
router.post('/visits/:id/delete', hasRole(['super_admin', 'admin', 'editor']), visitController.deleteVisit);
router.get('/visits/:id/status', hasRole(['super_admin', 'admin']), visitController.renderStatusForm);
router.post('/visits/:id/status', hasRole(['super_admin', 'admin']), visitController.updateStatus);
router.get('/visits/:id/update-status/:newStatus/:newMouStatus', hasRole(['super_admin', 'admin']), visitController.updateVisitStatusDirect);

module.exports = router;