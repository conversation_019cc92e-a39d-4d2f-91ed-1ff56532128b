<%- contentFor('body') %>

<div class="container px-6 mx-auto grid">
  <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">首頁項目</h1>
      <a href="/admin/frontpage" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
        <i class="fas fa-arrow-left mr-2"></i> 返回列表
      </a>
    </div>
    <div class="px-4 py-6 sm:px-0">
      <div class="bg-white shadow overflow-hidden sm:rounded-lg p-6">
        <!-- Language Switcher -->
        <div class="mb-6">
          <div class="flex border-b border-gray-200">
            <button type="button"
              class="py-2 px-4 text-center border-b-2 border-blue-500 text-blue-500 font-medium text-sm leading-5 focus:outline-none language-tab active"
              data-language="tw">
              中文
            </button>
            <button type="button"
              class="py-2 px-4 text-center border-b-2 border-transparent text-gray-500 font-medium text-sm leading-5 hover:text-gray-700 hover:border-gray-300 focus:outline-none language-tab"
              data-language="en">
              英文
            </button>
          </div>
        </div>
        
        <form action="/admin/frontpage/items/<%= item.id %>" method="POST" enctype="multipart/form-data"
          data-max-image-size="<%= process.env.MAX_IMAGE_SIZE || (5 * 1024 * 1024) %>"
          data-allowed-image-types="<%= process.env.ALLOWED_IMAGE_TYPES || 'jpg,jpeg,png,gif,webp' %>">
          <input type="hidden" name="_method" value="POST">
          <div class="mb-4">
            <label for="type" class="block text-sm font-medium text-gray-700 mb-2">類型</label>
            <select id="type" name="type"
              class="mt-1 block w-full py-2 px-6 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required>
              <option value="plain_text" <%=item.type==='plain_text' ? 'selected' : '' %>>編輯器</option>
              <option value="picture" <%=item.type==='picture' ? 'selected' : '' %>>圖片</option>
            </select>
          </div>

          <div class="mb-4 hidden">
            <label for="categoryId" class="block text-sm font-medium text-gray-700 mb-2">分類</label>
            <select id="categoryId" name="categoryId"
              class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
              <% categories.forEach(function(category) { %>
                <option value="<%= category.id %>" <%=item.categoryId===category.id ? 'selected' : '' %>><%=
                    category.name_tw %>
                </option>
                <% }); %>
            </select>
          </div>

          <!-- Title - Traditional Chinese -->
          <div class="mb-4 language-content tw">
            <label for="title_tw" class="block text-sm font-medium text-gray-700 mb-2">標題 (中文)</label>
            <input type="text" id="title_tw" name="title_tw" value="<%= item.title_tw %>"
              class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required>
          </div>

          <!-- Title - English -->
          <div class="mb-4 language-content en hidden">
            <label for="title_en" class="block text-sm font-medium text-gray-700 mb-2">標題 (英文)</label>
            <input type="text" id="title_en" name="title_en" value="<%= item.title_en %>"
              class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              required>
          </div>

          <!-- Moved 排序 field here -->
          <div class="mb-4">
            <label for="order" class="block text-sm font-medium text-gray-700 mb-2">排序</label>
            <input type="number" id="order" name="order" value="<%= item.order %>" min="0"
              class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
          </div>

          <!-- Moved 狀態 field here -->
          <div class="mb-4">
            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">狀態</label>
            <select id="status" name="status"
              class="mt-1 block w-full py-2 px-6 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
              <option value="draft" <%=item.status==='draft' ? 'selected' : '' %>>草稿</option>
              <option value="published" <%=item.status==='published' ? 'selected' : '' %>>發布</option>
            </select>
          </div>

          <div id="content-fields" class="mb-4 <%= item.type === 'picture' ? 'hidden' : '' %>">
            <!-- Content - Traditional Chinese -->
            <div class="mb-4 language-content tw">
              <label for="content_tw" class="block text-sm font-medium text-gray-700 mb-2">內容 (中文)</label>
              <textarea id="content_tw" name="content_tw" rows="5"
                class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"><%= item.content_tw || '' %></textarea>
            </div>

            <!-- Content - English -->
            <div class="mb-4 language-content en hidden">
              <label for="content_en" class="block text-sm font-medium text-gray-700 mb-2">內容 (英文)</label>
              <textarea id="content_en" name="content_en" rows="5"
                class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"><%= item.content_en || '' %></textarea>
            </div>
          </div>

          <div id="image-upload" class="mb-4 <%= item.type === 'plain_text' ? 'hidden' : '' %>">
            <% if (item.images && item.images.length > 0) { %>
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">現有圖片</label>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                  <% item.images.forEach(function(image, index) { %>
                    <div class="relative border rounded p-2">
                      <div class="flex justify-between items-center mb-2">
                        <% if (image.language === 'en') { %>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            英文版
                          </span>
                        <% } else if (image.language === 'tw') { %>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            中文版
                          </span>
                        <% } else { %>
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            通用
                          </span>
                        <% } %>
                      </div>
                      <img src="<%= image.path %>" alt="Image <%= index + 1 %>" class="w-full h-32 object-cover">
                      <div class="mt-2">
                        <span class="text-sm text-gray-500">圖片排序: <%= image.order %></span>
                        <div class="flex justify-between items-center">
                          <input type="hidden" name="existingImages[]" value="<%= image.id %>">
                          <input type="number" name="imageOrders[<%= image.id %>]" value="<%= image.order %>" min="0"
                            class="w-16 py-1 px-2 border border-gray-300 rounded text-sm">
                          <button type="button" class="delete-image ml-2 text-red-500 hover:text-red-700"
                            data-id="<%= image.id %>">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>

                      <div class="mt-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">替代文字 (Alt)</label>
                        <input type="text" name="imageAlts[<%= image.id %>]" value="<%= image.alt || '' %>"
                          placeholder="描述圖片內容" class="w-full py-1 px-2 border border-gray-300 rounded text-sm">
                      </div>

                      <div class="mt-2">
                        <label class="block text-sm font-medium text-gray-700 mb-1">連結網址 (URL)</label>
                        <input type="url" name="imageUrls[<%= image.id %>]" value="<%= image.url || '' %>"
                          placeholder="https://example.com"
                          class="w-full py-1 px-2 border border-gray-300 rounded text-sm">
                      </div>
                    </div>
                    <% }); %>
                </div>
              </div>
              <% } %>

                <label class="block text-sm font-medium text-gray-700 mb-2">新增圖片 (最多10張)</label>
                <div id="image-container" class="grid grid-cols-1 gap-4 mb-4">
                  <div class="image-upload-item border rounded p-4">
                    <!-- Chinese Image Section -->
                    <div class="language-content tw">
                      <div class="flex items-center justify-between mb-3">
                        <span class="font-medium text-gray-800">中文版圖片</span>
                      </div>
                      <div class="mb-3">
                        <input type="file" name="images[]" accept="image/*" multiple
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">替代文字 (Alt)</label>
                        <input type="text" name="image_alts[]" placeholder="描述圖片內容"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">連結網址 (URL)</label>
                        <input type="url" name="image_urls[]" placeholder="https://example.com"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">圖片排序</label>
                        <input type="number" name="image_orders[]" value="0" min="0"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                    </div>
                    
                    <!-- English Image Section -->
                    <div class="language-content en hidden">
                      <div class="flex items-center justify-between mb-3">
                        <span class="font-medium text-gray-800">英文版圖片</span>
                      </div>
                      <div class="mb-3">
                        <input type="file" name="images_en[]" accept="image/*" multiple
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">替代文字 (Alt)</label>
                        <input type="text" name="image_alts_en[]" placeholder="Image description"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">連結網址 (URL)</label>
                        <input type="url" name="image_urls_en[]" placeholder="https://example.com"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">圖片排序</label>
                        <input type="number" name="image_orders_en[]" value="0" min="0"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                    </div>
                  </div>
                </div>
                <button type="button" id="add-image"
                  class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                  新增圖片
                </button>
          </div>

          <div class="flex justify-end">
            <a href="/admin/frontpage"
              class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2">
              取消
            </a>
            <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              更新
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const typeSelect = document.getElementById('type');
      const contentFields = document.getElementById('content-fields');
      const imageUpload = document.getElementById('image-upload');
      const addImageBtn = document.getElementById('add-image');
      const imageContainer = document.getElementById('image-container');
      const titleTw = document.getElementById('title_tw');
      const titleEn = document.getElementById('title_en');
      
      // Language switcher functionality
      const languageTabs = document.querySelectorAll('.language-tab');
      const languageContents = document.querySelectorAll('.language-content');

      languageTabs.forEach(tab => {
          tab.addEventListener('click', function () {
              const language = this.getAttribute('data-language');

              // Update active tab
              languageTabs.forEach(t => {
                  t.classList.remove('border-blue-500', 'text-blue-500', 'active');
                  t.classList.add('border-transparent', 'text-gray-500');
              });
              this.classList.remove('border-transparent', 'text-gray-500');
              this.classList.add('border-blue-500', 'text-blue-500', 'active');

              // Show/hide content sections
              languageContents.forEach(content => {
                  if (content.classList.contains(language)) {
                      content.classList.remove('hidden');
                  } else {
                      content.classList.add('hidden');
                  }
              });
              
              // Update required attributes for title fields
              if (language === 'tw') {
                titleTw.required = true;
                titleEn.required = false;
              } else if (language === 'en') {
                titleTw.required = false;
                titleEn.required = true;
              }
          });
      });

      // Toggle fields based on selected type
      typeSelect.addEventListener('change', function () {
        if (this.value === 'plain_text') {
          contentFields.classList.remove('hidden');
          imageUpload.classList.add('hidden');
        } else if (this.value === 'picture') {
          contentFields.classList.add('hidden');
          imageUpload.classList.remove('hidden');
        }
      });

      // Add more image upload fields
      let imageCount = 1;
      const existingImagesCount = <%= item.images && item.images.length ? item.images.length : 0 %>;

      addImageBtn.addEventListener('click', function () {
        if (imageCount + existingImagesCount < 10) {
          const newImageInput = document.createElement('div');
          newImageInput.className = 'image-upload-item border rounded p-4 mt-4';
          newImageInput.innerHTML = `
                    <div class="flex justify-end mb-2">
                        <button type="button" class="remove-image text-red-500 hover:text-red-700">
                            <i class="fas fa-times"></i> 移除
                        </button>
                    </div>
                    
                    <!-- Chinese Image Section -->
                    <div class="language-content tw">
                      <div class="flex items-center justify-between mb-3">
                        <span class="font-medium text-gray-800">中文版圖片</span>
                      </div>
                      <div class="mb-3">
                        <input type="file" name="images[]" accept="image/*" multiple
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">替代文字 (Alt)</label>
                        <input type="text" name="image_alts[]" placeholder="描述圖片內容"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">連結網址 (URL)</label>
                        <input type="url" name="image_urls[]" placeholder="https://example.com"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">圖片排序</label>
                        <input type="number" name="image_orders[]" value="0" min="0"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                    </div>
                    
                    <!-- English Image Section -->
                    <div class="language-content en hidden">
                      <div class="flex items-center justify-between mb-3">
                        <span class="font-medium text-gray-800">英文版圖片</span>
                      </div>
                      <div class="mb-3">
                        <input type="file" name="images_en[]" accept="image/*" multiple
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">替代文字 (Alt)</label>
                        <input type="text" name="image_alts_en[]" placeholder="Image description"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">連結網址 (URL)</label>
                        <input type="url" name="image_urls_en[]" placeholder="https://example.com"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 mb-2">圖片排序</label>
                        <input type="number" name="image_orders_en[]" value="0" min="0"
                          class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                      </div>
                    </div>
                `;
          imageContainer.appendChild(newImageInput);
          imageCount++;

          // Add event listener to remove button
          const removeBtn = newImageInput.querySelector('.remove-image');
          removeBtn.addEventListener('click', function () {
            imageContainer.removeChild(newImageInput);
            imageCount--;

            if (imageCount + existingImagesCount < 10) {
              addImageBtn.disabled = false;
            }
          });
        }

        if (imageCount + existingImagesCount >= 10) {
          addImageBtn.disabled = true;
        }
      });

      // Delete existing images
      const deleteButtons = document.querySelectorAll('.delete-image');
      deleteButtons.forEach(button => {
        button.addEventListener('click', function () {
          const imageId = this.getAttribute('data-id');
          const imageContainer = this.closest('.relative');

          // Add a hidden input to mark this image for deletion
          const deleteInput = document.createElement('input');
          deleteInput.type = 'hidden';
          deleteInput.name = 'deleteImages[]';
          deleteInput.value = imageId;
          document.querySelector('form').appendChild(deleteInput);

          // Hide the image container
          imageContainer.style.display = 'none';

          // Update the count
          existingImagesCount--;
          if (imageCount + existingImagesCount < 10) {
            addImageBtn.disabled = false;
          }
        });
      });
    });
  </script>