<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold text-neutral-800">首頁項目</h1>
      <a href="/admin/frontpage/items/create"
        class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center">
        <i class="fas fa-plus mr-2"></i> 新增首頁項目
      </a>
    </div>

    <% if (items && items.length> 0) { %>
      <div class="bg-white shadow-md rounded-lg overflow-x-auto">
        <table class="min-w-full divide-y divide-neutral-200 ">
          <thead class="bg-neutral-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                標題
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                類型
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                狀態
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                排序
              </th>
              <th scope="col"
                class="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-neutral-200">
            <% items.forEach(function(item) { %>
              <tr class="hover:bg-neutral-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-900">
                  <%= item.title_tw %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                  <span
                    class="px-2 py-1 inline-flex text-xs leading-5 font-medium rounded-md <%= item.type==='plain_text' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800' %>">
                    <%= item.type==='plain_text' ? '編輯器' : '圖片' %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <%= item.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-neutral-100 text-neutral-800' %>">
                    <%= item.status==='published' ? '已發布' : '草稿' %>
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                  <%= item.order %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <a href="/admin/frontpage/items/<%= item.id %>/edit" class="text-blue-600 hover:text-blue-800 mr-4">
                    <i class="fas fa-edit"></i>
                  </a>
                  <form action="/admin/frontpage/items/<%= item.id %>/delete" method="POST"
                    class="inline-block delete-form">
                    <button type="submit" class="text-red-600 hover:text-red-800">
                      <i class="fas fa-trash"></i>
                    </button>
                  </form>
                </td>
              </tr>
              <% }); %>
          </tbody>
        </table>
      </div>
      <% } else { %>
        <div class="bg-white shadow-md rounded-lg p-8 text-center text-neutral-500">
          <div class="mb-4">
            <i class="fas fa-list text-4xl text-neutral-300"></i>
          </div>
          <p class="mb-4">尚未找到任何首頁項目。</p>
          <a href="/admin/frontpage/items/create" class="text-blue-600 hover:text-blue-800 font-medium">
            立即建立一個 <i class="fas fa-arrow-right ml-1"></i>
          </a>
        </div>
        <% } %>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const deleteForms = document.querySelectorAll('.delete-form');
      if (deleteForms.length > 0) {
        deleteForms.forEach(form => {
          form.addEventListener('submit', function (e) {
            e.preventDefault();
            if (confirm('確定要刪除此項目嗎？')) {
              this.submit();
            }
          });
        });
      }
    });
  </script>