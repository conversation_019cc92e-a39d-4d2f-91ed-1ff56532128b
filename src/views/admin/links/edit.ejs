<%- contentFor('body') %>

  <div class="container px-6 mx-auto grid">
    <h2 class="my-6 text-2xl font-semibold text-gray-700">
      編輯連結
    </h2>

    <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
      <form action="/admin/links/<%= link.id %>" method="POST" enctype="multipart/form-data">
        <!-- Language Tabs -->
        <%- include('../../partials/admin-language-switcher', { title: '內容語言' , activeTab: 'tw' }) %>

          <!-- Traditional Chinese Content -->
          <div class="language-content language-content-tw ">
            <!-- Title (Traditional Chinese) -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="title_tw">
                標題 (中文) <span class="text-red-500">*</span>
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="text" id="title_tw" name="title_tw" placeholder="請輸入連結標題（中文）" value="<%= link.title_tw %>"
                required />
            </div>
          </div>

          <!-- English Content -->
          <div class="language-content language-content-en hidden">
            <!-- Title (English) -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="title_en">
                標題 (英文) <span class="text-red-500">*</span>
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="text" id="title_en" name="title_en" placeholder="請輸入連結標題（英文）" value="<%= link.title_en %>"
                required />
            </div>
          </div>
          
          <!-- Common Fields -->
          <div class="border-t pt-4 mt-4">
            <!-- URL -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="url">
                網址 <span class="text-red-500">*</span>
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="url" id="url" name="url" placeholder="https://example.com" value="<%= link.url %>" required />
              <p class="mt-1 text-xs text-gray-500">請輸入完整網址，包含 http:// 或 https://</p>
            </div>

            <!-- Image Upload -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="image">
                圖片
              </label>
              <% if (link.image) { %>
                <div class="mt-2 mb-2">
                  <img src="<%= link.image %>" alt="<%= link.title_en %>" class="h-24 w-24 object-cover rounded">
                  <div class="mt-2">
                    <label class="inline-flex items-center">
                      <input type="checkbox" name="removeImage" class="form-checkbox h-4 w-4 text-blue-600">
                      <span class="ml-2 text-sm text-gray-700">移除目前圖片</span>
                    </label>
                  </div>
                </div>
                <% } %>
                  <div class="mt-1 flex items-center">
                    <input type="file" id="image" name="image" accept="image/*"
                      class="shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" />
                  </div>
                  <p class="mt-1 text-xs text-gray-500">建議尺寸：800x600 像素。最大檔案大小：2MB。</p>
            </div>

            <!-- Order -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="order">
                顯示順序
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="number" id="order" name="order" placeholder="請輸入顯示順序（數字越小排序越前）" value="<%= link.order %>" />
            </div>

            <!-- Status -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
                狀態
              </label>
              <select
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="status" name="status">
                <option value="active" <%= link.status === 'active' ? 'selected' : '' %>>啟用</option>
                <option value="inactive" <%= link.status === 'inactive' ? 'selected' : '' %>>停用</option>
              </select>
              <p class="mt-1 text-xs text-gray-500">啟用的連結將顯示在頁面</p>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end mt-6">
              <a href="/admin/links"
                class="px-4 py-2 mr-2 text-sm font-medium leading-5 text-gray-700 transition-colors duration-150 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:outline-none focus:shadow-outline-gray">
                取消
              </a>
              <button type="submit"
                class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-blue-600 border border-transparent rounded-lg active:bg-blue-600 hover:bg-blue-700 focus:outline-none focus:shadow-outline-blue">
                更新連結
              </button>
            </div>
          </div>
      </form>
    </div>
  </div>