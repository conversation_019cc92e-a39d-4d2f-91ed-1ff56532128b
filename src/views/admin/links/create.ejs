<%- contentFor('body') %>

  <div class="container px-6 mx-auto grid">
    <h2 class="my-6 text-2xl font-semibold text-gray-700">
      建立連結
    </h2>

    <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
      <form action="/admin/links" method="POST" enctype="multipart/form-data">
        <!-- Language Tabs -->
        <%- include('../../partials/admin-language-switcher', { title: '內容語言' , activeTab: 'tw' }) %>

          <!-- Traditional Chinese Content -->
          <div class="language-content language-content-tw">
            <!-- Title (Traditional Chinese) -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="title_tw">
                標題 (中文) <span class="text-red-500">*</span>
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="text" id="title_tw" name="title_tw" placeholder="請輸入連結標題（中文）" required />
            </div>
          </div>

          <!-- English Content -->
          <div class="language-content language-content-en hidden">
            <!-- Title (English) -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="title_en">
                標題 (英文) <span class="text-red-500">*</span>
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="text" id="title_en" name="title_en" placeholder="請輸入連結標題（英文）" required />
            </div>
          </div>

          <!-- Common Fields -->
          <div class="border-t pt-4 mt-4">
            <!-- URL -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="url">
                網址 <span class="text-red-500">*</span>
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="url" id="url" name="url" placeholder="https://example.com" required />
              <p class="mt-1 text-xs text-gray-500">請輸入完整網址，包含 http:// 或 https://</p>
            </div>

            <!-- Image Upload -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="image">
                圖片
              </label>
              <div class="mt-1 flex items-center">
                <span class="inline-block h-12 w-12 rounded-full overflow-hidden bg-gray-100">
                  <svg class="h-full w-full text-gray-300" fill="currentColor" viewBox="0 0 24 24">
                    <path
                      d="M24 20.993V24H0v-2.996A14.977 14.977 0 0112.004 15c4.904 0 9.26 2.354 11.996 5.993zM16.002 8.999a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                </span>
                <input type="file" id="image" name="image" accept="image/*"
                  class="ml-5 shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" />
              </div>
              <p class="mt-1 text-xs text-gray-500">建議尺寸：800x600 像素。最大檔案大小：2MB。</p>
            </div>

            <!-- Order -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="order">
                顯示順序
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                type="number" id="order" name="order" placeholder="請輸入顯示順序（數字越小排序越前）" value="0" />
            </div>

            <!-- Status -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
                狀態
              </label>
              <select
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="status" name="status">
                <option value="active" selected>啟用</option>
                <option value="inactive">停用</option>
              </select>
              <p class="mt-1 text-xs text-gray-500">啟用的連結將顯示在頁面</p>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end mt-6">
              <a href="/admin/links"
                class="px-4 py-2 mr-2 text-sm font-medium leading-5 text-gray-700 transition-colors duration-150 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:outline-none focus:shadow-outline-gray">
                取消
              </a>
              <button type="submit"
                class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-blue-600 border border-transparent rounded-lg active:bg-blue-600 hover:bg-blue-700 focus:outline-none focus:shadow-outline-blue">
                建立連結
              </button>
            </div>
          </div>
      </form>
    </div>
  </div>