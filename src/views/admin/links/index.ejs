<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold text-neutral-800">相關連結</h1>
      <a href="/admin/links/create"
        class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center">
        <i class="fas fa-plus mr-2"></i> 新增相關連結
      </a>
    </div>

    <% if (links && links.length> 0) { %>
      <div class="bg-white shadow-md rounded-lg overflow-x-auto">
        <table class="min-w-full divide-y divide-neutral-200">
          <thead class="bg-neutral-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                順序
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                圖片
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                標題
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                網址
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                狀態
              </th>
              <th scope="col"
                class="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-neutral-200">
            <% links.forEach(function(link) { %>
              <tr class="hover:bg-neutral-50 transition-colors">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                  <%= link.order %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (link.image) { %>
                    <img src="<%= link.image %>" alt="<%= link.title_en %>" class="h-12 w-12 object-cover rounded-md">
                    <% } else { %>
                      <div class="h-12 w-12 bg-neutral-200 flex items-center justify-center rounded-md">
                        <span class="text-neutral-500 text-xs">無圖片</span>
                      </div>
                      <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-neutral-900">
                    <div class="mb-1">
                      <span class="text-xs text-neutral-500">中:</span>
                      <%= link.title_tw %>
                    </div>
                    <div>
                      <span class="text-xs text-neutral-500">EN:</span>
                      <%= link.title_en %>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                  <a href="<%= link.url %>" target="_blank"
                    class="text-blue-600 hover:text-blue-800 truncate block max-w-xs">
                    <%= link.url.length> 30 ? link.url.substring(0, 30) + '...' : link.url %>
                  </a>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <% if (link.active) { %>
                    <span
                      class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                      啟用
                    </span>
                    <% } else { %>
                      <span
                        class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-neutral-100 text-neutral-800">
                        停用
                      </span>
                      <% } %>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-4">
                    <a href="/admin/links/edit/<%= link.id %>" class="text-blue-600 hover:text-blue-800">
                      <i class="fas fa-edit"></i>
                    </a>
                    <a href="/admin/links/toggle/<%= link.id %>" class="text-amber-600 hover:text-amber-800">
                      <i class="fas <%= link.active ? 'fa-eye-slash' : 'fa-eye' %>"></i>
                    </a>
                    <button class="text-red-600 hover:text-red-800"
                      onclick="confirmDelete('<%= link.id %>', '<%= link.title_en %>')">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
              <% }); %>
          </tbody>
        </table>
      </div>
      <% } else { %>
        <div class="bg-white shadow-md rounded-lg p-8 text-center text-neutral-500">
          <div class="mb-4">
            <i class="fas fa-link text-4xl text-neutral-300"></i>
          </div>
          <p class="mb-4">尚未找到任何相關連結。</p>
          <a href="/admin/links/create" class="text-blue-600 hover:text-blue-800 font-medium">
            立即建立一個 <i class="fas fa-arrow-right ml-1"></i>
          </a>
        </div>
        <% } %>
  </div>

  <!-- Delete Confirmation Modal -->
  <div id="deleteModal" class="fixed inset-0 bg-black/50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg p-6 max-w-md w-full shadow-xl">
      <h3 class="text-xl font-semibold text-neutral-900 mb-4">確認刪除</h3>
      <p class="text-neutral-600 mb-6">您確定要刪除連結「<span id="linkTitle"></span>」嗎？此操作無法撤銷。</p>
      <div class="flex justify-end">
        <button id="cancelDelete"
          class="px-4 py-2 mr-2 text-sm font-medium leading-5 text-neutral-700 transition-colors duration-150 bg-white border border-neutral-300 rounded-md hover:bg-neutral-100 focus:outline-none focus:ring-2 focus:ring-neutral-300">
          取消
        </button>
        <a id="confirmDeleteBtn" href="#"
          class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
          刪除
        </a>
      </div>
    </div>
  </div>

  <script>
    function confirmDelete(id, title) {
      document.getElementById('linkTitle').textContent = title;
      document.getElementById('confirmDeleteBtn').href = `/admin/links/delete/${id}`;
      document.getElementById('deleteModal').classList.remove('hidden');
    }

    document.getElementById('cancelDelete').addEventListener('click', function () {
      document.getElementById('deleteModal').classList.add('hidden');
    });

    // Close modal when clicking outside
    document.getElementById('deleteModal').addEventListener('click', function (e) {
      if (e.target === this) {
        this.classList.add('hidden');
      }
    });
  </script>