<%- contentFor('body') %>

    <!-- No rich text editor needed for news items -->

    <div class="container px-6 mx-auto grid">
        <div class="flex justify-between items-center my-6">
            <h2 class="text-2xl font-semibold text-gray-700">
                <%= newsItem ? '編輯最新消息項目' : '建立最新消息項目' %>
            </h2>
            <a href="/admin/news/items"
                class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-gray-600 border border-transparent rounded-lg active:bg-gray-600 hover:bg-gray-700 focus:outline-none focus:shadow-outline-gray">
                <i class="fas fa-arrow-left mr-2"></i> 返回最新消息項目列表
            </a>
        </div>

        <!-- Flash Messages -->
        <% if(typeof error_msg !=='undefined' && error_msg.length> 0) { %>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                <%= error_msg %>
            </div>
            <% } %>

                <!-- Form -->
                <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
                    <form action="<%= newsItem ? `/admin/news/items/${newsItem.id}` : '/admin/news/items' %>"
                        method="POST" enctype="multipart/form-data" id="newsForm">

                        <!-- Language Tabs -->
                        <div class="mb-6">
                            <div class="border-b border-gray-200">
                                <nav class="-mb-px flex">
                                    <button type="button"
                                        class="language-tab py-2 px-4 border-b-2 font-medium text-sm leading-5 focus:outline-none border-blue-500 text-blue-600 hover:text-gray-700 hover:border-gray-300 active-tab"
                                        data-lang="tw">中文</button>
                                    <button type="button"
                                        class="language-tab py-2 px-4 border-b-2 font-medium text-sm leading-5 focus:outline-none border-transparent text-gray-500"
                                        data-lang="en">英文</button>
                                </nav>
                            </div>
                        </div>

                        <script>
                            document.addEventListener('DOMContentLoaded', function () {
                                // Add click event listeners to language tabs
                                document.querySelectorAll('.language-tab').forEach(tab => {
                                    tab.addEventListener('click', function () {
                                        const lang = this.getAttribute('data-lang');

                                        // Hide all language content sections
                                        document.querySelectorAll('.language-content').forEach(el => {
                                            el.classList.add('hidden');
                                        });

                                        // Show the selected language content
                                        document.querySelectorAll(`.language-content-${lang}`).forEach(el => {
                                            el.classList.remove('hidden');
                                        });

                                        // Update active tab styling
                                        document.querySelectorAll('.language-tab').forEach(t => {
                                            t.classList.remove('active-tab', 'border-blue-500', 'text-blue-600');
                                            t.classList.add('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                                        });

                                        this.classList.add('active-tab', 'border-blue-500', 'text-blue-600');
                                        this.classList.remove('border-transparent', 'text-gray-500', 'hover:text-gray-700', 'hover:border-gray-300');
                                    });
                                });

                                // 初始化 - 預設顯示中文內容
                                document.querySelectorAll('.language-content').forEach(el => {
                                    if (!el.classList.contains('language-content-tw')) {
                                        el.classList.add('hidden');
                                    }
                                });
                            });
                        </script>

                        <!-- Traditional Chinese Content -->
                        <div class="language-content language-content-tw">
                            <!-- Title -->
                            <div class="mb-6">
                                <label for="title_tw" class="block text-gray-700 text-sm font-bold mb-2">標題 (中文) <span class="text-red-500">*</span></label>
                                <input required type="text" id="title_tw" name="title_tw"
                                    value="<%= newsItem ? newsItem.title_tw : '' %>"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            </div>



                            <!-- Summary -->
                            <div class="mb-6">
                                <label for="summary_tw" class="block text-gray-700 text-sm font-bold mb-2">
                                    摘要 (中文)
                                    <span class="text-gray-500 text-xs">(選填)</span>
                                </label>
                                <textarea id="summary_tw" name="summary_tw" rows="3"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"><%= newsItem ? newsItem.summary_tw : '' %></textarea>
                            </div>
                        </div>

                        <!-- English Content -->
                        <div class="language-content language-content-en hidden">
                            <!-- Title -->
                            <div class="mb-6">
                                <label for="title_en" class="block text-gray-700 text-sm font-bold mb-2">標題 (英文) <span class="text-red-500">*</span></label>
                                <input required type="text" id="title_en" name="title_en"
                                    value="<%= newsItem ? newsItem.title_en : '' %>" required
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            </div>



                            <!-- Summary -->
                            <div class="mb-6">
                                <label for="summary_en" class="block text-gray-700 text-sm font-bold mb-2">
                                    摘要 (英文)
                                    <span class="text-gray-500 text-xs">(選填)</span>
                                </label>
                                <textarea id="summary_en" name="summary_en" rows="3"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"><%= newsItem ? newsItem.summary_en : '' %></textarea>
                            </div>
                        </div>

                        <!-- Common Fields -->
                        <div class="border-t pt-4">
                            <!-- Category -->
                            <div class="mb-6">
                                <label for="categoryId" class="block text-gray-700 text-sm font-bold mb-2">分類</label>
                                <select id="categoryId" name="categoryId"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                                    <option value="">選擇分類</option>
                                    <% categories.forEach(function(category) { %>
                                        <option value="<%= category.id %>" <%=newsItem && newsItem.categoryId==category.id ? 'selected' : '' %>>
                                            <%= category.name_tw %> / <%= category.name_en %>
                                        </option>
                                        <% }); %>
                                </select>
                            </div>

                            <!-- Image Upload -->
                            <div class="mb-6">
                                <label for="image" class="block text-gray-700 text-sm font-bold mb-2">
                                    特色圖片
                                    <span class="text-gray-500 text-xs">(選填)</span>
                                </label>
                                <input type="file" id="image" name="image" accept="image/*"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">

                                <% if (newsItem && newsItem.imagePath) { %>
                                    <div class="mt-2">
                                        <p class="text-sm text-gray-600 mb-2">目前圖片：</p>
                                        <img src="<%= newsItem.imagePath %>" alt="目前圖片"
                                            class="h-32 w-auto object-cover rounded">
                                        <div class="mt-1">
                                            <label class="inline-flex items-center">
                                                <input type="checkbox" name="removeImage"
                                                    class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                                <span class="ml-2 text-sm text-gray-600">移除目前圖片</span>
                                            </label>
                                        </div>
                                    </div>
                                    <% } %>
                            </div>

                            <!-- URL -->
                            <div class="mb-6">
                                <label for="url" class="block text-gray-700 text-sm font-bold mb-2">
                                    外部連結
                                    <span class="text-gray-500 text-xs">(選填)</span>
                                </label>
                                <input type="text" id="url" name="url" value="<%= newsItem ? newsItem.url : '' %>"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                                <p class="text-xs text-gray-500 mt-1">若提供此連結，點擊消息項目將會重新導向至此網址</p>
                            </div>

                            <!-- Published Date -->
                            <div class="mb-6">
                                <label for="publishedDate"
                                    class="block text-gray-700 text-sm font-bold mb-2">發布日期</label>
                                <input type="date" id="publishedDate" name="publishedDate"
                                    value="<%= newsItem && newsItem.publishedDate ? new Date(newsItem.publishedDate).toISOString().split('T')[0] : new Date().toISOString().split('T')[0] %>"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                            </div>

                            <!-- Status - Changed to Dropdown -->
                            <div class="mb-6">
                                <label for="status" class="block text-gray-700 text-sm font-bold mb-2">狀態</label>
                                <select id="status" name="status"
                                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                                    <option value="draft" <%=!newsItem || newsItem.status==='draft' ? 'selected' : '' %>
                                        >草稿</option>
                                    <option value="published" <%=newsItem && newsItem.status==='published' ? 'selected'
                                        : '' %>>已發布</option>
                                    <option value="archived" <%=newsItem && newsItem.status==='archived' ? 'selected'
                                        : '' %>>已封存</option>
                                </select>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end">
                                <button type="submit"
                                    class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <%= newsItem ? '更新' : '建立' %> 最新消息項目
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
    </div>

    <!-- No rich text editor needed for news items -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Language tab switching
            const tabs = document.querySelectorAll('.language-tab');
            const contents = document.querySelectorAll('.language-content');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const lang = tab.getAttribute('data-lang');

                    // Update tabs
                    tabs.forEach(t => t.classList.remove('active-tab', 'border-blue-500', 'text-blue-600'));
                    tabs.forEach(t => t.classList.add('border-transparent', 'text-gray-500'));
                    tab.classList.add('active-tab', 'border-blue-500', 'text-blue-600');
                    tab.classList.remove('border-transparent', 'text-gray-500');

                    // Update content
                    contents.forEach(content => content.classList.add('hidden'));
                    document.querySelector(`.language-content-${lang}`).classList.remove('hidden');
                });
            });

            // URL handling - clean up URL if needed
            const urlInput = document.getElementById('url');
            if (urlInput) {
                document.getElementById('newsForm').addEventListener('submit', function (e) {
                    // Only process if there's a URL value
                    if (urlInput.value.trim()) {
                        // If URL starts with @ symbol, remove it
                        if (urlInput.value.startsWith('@')) {
                            urlInput.value = urlInput.value.substring(1);
                        }

                        // Make sure URL has http:// or https:// prefix if not already present
                        if (!urlInput.value.match(/^https?:\/\//i) && !urlInput.value.startsWith('/')) {
                            urlInput.value = 'https://' + urlInput.value;
                        }
                    }
                });
            }
        });
    </script>