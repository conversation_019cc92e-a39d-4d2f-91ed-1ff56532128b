<%- contentFor('body') %>

  <div class="container px-6 mx-auto grid">
    <h2 class="my-6 text-2xl font-semibold text-gray-700">
      <%= category ? '編輯最新消息分類' : '建立最新消息分類' %>
    </h2>

        <!-- Form -->
        <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
          <form action="<%= category ? `/admin/news/categories/${category.id}` : '/admin/news/categories' %>"
            method="POST">

            <!-- Language Tabs -->
            <div class="mb-6">
              <div class="border-b border-gray-200">
                <nav class="-mb-px flex">
                  <button type="button"
                      class="language-tab py-2 px-4 border-b-2 font-medium text-sm leading-5 focus:outline-none border-blue-500 text-blue-600 hover:text-gray-700 hover:border-gray-300 active-tab"
                      data-lang="tw">中文</button>
                  <button type="button"
                      class="language-tab py-2 px-4 border-b-2 font-medium text-sm leading-5 focus:outline-none border-transparent text-gray-500"
                      data-lang="en">英文</button>
                </nav>
              </div>
            </div>

            <!-- Traditional Chinese Content -->
            <div class="language-content language-content-tw">
              <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="name_tw">
                  名稱 (中文) <span class="text-red-500">*</span>
                </label>
                <input
                  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  id="name_tw" name="name_tw" type="text" value="<%= category ? category.name_tw : '' %>">
              </div>
              <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="description_tw">
                  描述 (中文)
                </label>
                <textarea
                  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  id="description_tw" name="description_tw"
                  rows="3"><%= category ? category.description_tw : '' %></textarea>
              </div>
            </div>

            <!-- English Content -->
            <div class="language-content language-content-en hidden">
              <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="name_en">
                  名稱 (英文) <span class="text-red-500">*</span>
                </label>
                <input
                  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  id="name_en" name="name_en" type="text" value="<%= category ? category.name_en : '' %>" required>
              </div>
              <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="description_en">
                  描述 (英文)
                </label>
                <textarea
                  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                  id="description_en" name="description_en"
                  rows="3"><%= category ? category.description_en : '' %></textarea>
              </div>
            </div>

            <!-- Common Fields -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="slug">
                網址代碼 <span class="text-red-500">*</span>
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="slug" name="slug" type="text" value="<%= category ? category.slug : '' %>" required>
              <p class="mt-1 text-xs text-gray-500">
                網址友好名稱。留空將自動從英文名稱生成。
              </p>
            </div>
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="order">
                顯示順序
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="order" name="order" type="number" value="<%= category ? category.order : 0 %>" min="0">
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end mt-6">
              <a href="/admin/news/categories"
                class="px-4 py-2 mr-2 text-sm font-medium leading-5 text-gray-700 transition-colors duration-150 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:outline-none focus:shadow-outline-gray">
                取消
              </a>
              <button type="submit"
                class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-blue-600 border border-transparent rounded-lg active:bg-blue-600 hover:bg-blue-700 focus:outline-none focus:shadow-outline-blue">
                <%= category ? '更新分類' : '建立分類' %>
              </button>
            </div>
          </form>
        </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Language tab switching
      const tabs = document.querySelectorAll('.language-tab');
      const contents = document.querySelectorAll('.language-content');

      tabs.forEach(tab => {
        tab.addEventListener('click', () => {
          const lang = tab.getAttribute('data-lang');

          // Update tabs
          tabs.forEach(t => t.classList.remove('active-tab', 'border-blue-500', 'text-blue-600'));
          tabs.forEach(t => t.classList.add('border-transparent', 'text-gray-500'));
          tab.classList.add('active-tab', 'border-blue-500', 'text-blue-600');
          tab.classList.remove('border-transparent', 'text-gray-500');

          // Update content
          contents.forEach(content => content.classList.add('hidden'));
          document.querySelector(`.language-content-${lang}`).classList.remove('hidden');
        });
      });

      // Slug generation from name
      const nameInput = document.getElementById('name_en');
      const slugInput = document.getElementById('slug');

      if (nameInput && slugInput) {
        nameInput.addEventListener('blur', function () {
          if (slugInput.value === '') {
            const slug = this.value
              .toLowerCase()
              .replace(/[^\w\s-]/g, '')
              .replace(/[\s_-]+/g, '-')
              .replace(/^-+|-+$/g, '');
            slugInput.value = slug;
          }
        });
      }
    });
  </script>