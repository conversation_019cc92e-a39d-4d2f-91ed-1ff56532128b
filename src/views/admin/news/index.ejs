<%- contentFor('body') %>

    <div class="container px-6 mx-auto grid">
        <div class="flex justify-between items-center my-6">
            <h2 class="text-2xl font-semibold text-gray-700">
                最新消息管理
            </h2>
            <div>
                <a href="/admin/news/items"
                    class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-bold py-2 px-4 rounded mr-2">
                    <i class="fas fa-arrow-left mr-2"></i> 返回最新消息項目
                </a>
                <a href="/admin/news/categories"
                    class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-bold py-2 px-4 rounded mr-2">
                    <i class="fas fa-folder-open mr-2"></i> 管理最新消息分類
                </a>
            </div>
        </div>


                        <!-- News Management Cards -->
                        <div class="grid gap-6 mb-8 md:grid-cols-2">
                            <!-- News Items Card -->
                            <div class="min-w-0 p-4 bg-white rounded-lg shadow-xs">
                                <h4 class="mb-4 font-semibold text-gray-800">最新消息項目</h4>
                                <p class="text-gray-600 mb-4">
                                    建立和管理具有英文和中文多語言支援的最新消息項目。
                                </p>
                                <div class="flex mt-4 space-x-3">
                                    <a href="/admin/news/items"
                                        class="flex items-center justify-between px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-purple-600 border border-transparent rounded-lg active:bg-purple-600 hover:bg-purple-700 focus:outline-none focus:shadow-outline-purple">
                                        <span>查看所有最新消息項目</span>
                                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                    <a href="/admin/news/items/create"
                                        class="flex items-center justify-between px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-green-600 border border-transparent rounded-lg active:bg-green-600 hover:bg-green-700 focus:outline-none focus:shadow-outline-green">
                                        <span>新增項目</span>
                                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>

                            <!-- News Categories Card -->
                            <div class="min-w-0 p-4 bg-white rounded-lg shadow-xs">
                                <h4 class="mb-4 font-semibold text-gray-800">最新消息分類</h4>
                                <p class="text-gray-600 mb-4">
                                    使用多語言支援將您的消息項目組織成分類。
                                </p>
                                <div class="flex mt-4 space-x-3">
                                    <a href="/admin/news/categories"
                                        class="flex items-center justify-between px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-purple-600 border border-transparent rounded-lg active:bg-purple-600 hover:bg-purple-700 focus:outline-none focus:shadow-outline-purple">
                                        <span>查看所有分類</span>
                                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </a>
                                    <a href="/admin/news/categories/create"
                                        class="flex items-center justify-between px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-green-600 border border-transparent rounded-lg active:bg-green-600 hover:bg-green-700 focus:outline-none focus:shadow-outline-green">
                                        <span>新增分類</span>
                                        <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                        </div>

    </div>