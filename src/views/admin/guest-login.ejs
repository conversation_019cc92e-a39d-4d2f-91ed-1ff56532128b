<%- contentFor('body') %>


    <div class="min-h-screen flex items-center justify-center bg-gray-100 py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <div>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                    訪客登入
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                    為廠商訪談與合作項目使用者專用
                </p>
            </div>

            <% if (typeof error_msg !=='undefined' && error_msg.length> 0) { %>
                <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                    <p>
                        <%= error_msg %>
                    </p>
                </div>
                <% } %>

                    <form class="mt-8 space-y-6" action="/admin/guest-login" method="POST" onsubmit="return validateForm()">
                        <div class="rounded-md shadow-sm -space-y-px">
                            <div>
                                <label for="username" class="sr-only">使用者名稱</label>
                                <input id="username" name="username" type="text" required
                                    class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                    placeholder="使用者名稱">
                            </div>
                            <div>
                                <label for="password" class="sr-only">密碼</label>
                                <input id="password" name="password" type="password" required
                                    class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                                    placeholder="密碼">
                            </div>
                        </div>

                        <!-- Terms Agreement Checkbox -->
                        <div class="mt-4">
                            <p>本方案旨在深入了解產業需求並推動應用導入，持續進行廠商訪談與實地考察。為確保訪廠資訊的機密性，請您在查看詳細內容前，同意遵守以下保密條款：該資訊僅供參考，不得擅自公開、分享或用於未經授權的用途。</p>
                            <div class="flex items-start">
                                <div class="flex items-center h-5">
                                    <input id="terms" name="terms" type="checkbox" required
                                        class="focus:ring-blue-500 h-4 w-4 text-blue-600 border-gray-300 rounded">
                                </div>
                                <div class="ml-3 text-sm">
                                    <label for="terms" class="font-medium text-gray-700">本人已閱讀並同意</label>
                                    <a href="#" class="font-medium text-blue-600 hover:text-blue-500">
                                        保密條款
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Captcha Section -->
                        <div class="mt-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">請輸入驗證碼</label>
                            <div class="flex items-center space-x-2 mb-2">
                                <!-- Captcha display container -->
                                <div id="captchaDisplay" class="w-full h-20 flex items-center justify-center border border-gray-300 rounded-md bg-repeat"></div>
                                
                                <!-- Hidden input for validation -->
                                <input type="hidden" id="randomfield">
                                
                                <button type="button" onclick="ChangeCaptcha()" class="py-2 px-3 bg-gray-200 rounded-md hover:bg-gray-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                                    </svg>
                                </button>
                            </div>
                            <input id="CaptchaEnter" name="captcha" size="20" maxlength="6" required
                                class="appearance-none block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                placeholder="請輸入上方驗證碼">
                        </div>

                        <div>
                            <button type="submit"
                                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                    <i class="fas fa-sign-in-alt"></i>
                                </span>
                                訪客登入
                            </button>
                        </div>
                    </form>
        </div>
    </div>

    <style>
        #randomfield { 
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            
            width: 200px;
            color: black;
            border-color: black;
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            background-color: #f0f0f0;
        }
    </style>

    <script>
        window.onload = function() {
            ChangeCaptcha();
        };
        
        function validateForm() {
            // Validate captcha
            if (!validateCaptcha()) {
                return false;
            }
            
            // Terms checkbox validation is handled by HTML required attribute
            return true;
        }
        
        function validateCaptcha() {
            const inputCaptcha = document.getElementById('CaptchaEnter').value;
            const randomNumber = document.getElementById('randomfield').value;
            
            if (inputCaptcha !== randomNumber) {
                alert('驗證碼不正確，請重新輸入');
                return false;
            }
            return true;
        }
        

        function ChangeCaptcha() {
            const chars = "0123456789abcdefghiklmnopqrstuvwxyz";
            const string_length = 6;
            let randomstring = '';
            
            for (let i = 0; i < string_length; i++) {
                const rnum = Math.floor(Math.random() * chars.length);
                randomstring += chars.substring(rnum, rnum + 1);
            }
            
            document.getElementById('randomfield').value = randomstring;
            
            // Display captcha in the captchaDisplay div
            const captchaDisplay = document.getElementById('captchaDisplay');
            captchaDisplay.style.fontFamily = "Arial, sans-serif";
            captchaDisplay.style.letterSpacing = "5px";
            captchaDisplay.style.fontWeight = "bold";
            captchaDisplay.style.fontSize = "24px";
            captchaDisplay.textContent = randomstring;
        }

    </script> 