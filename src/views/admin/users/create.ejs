<%- contentFor('body') %>

<div class="container px-6 mx-auto grid">
    <h2 class="my-6 text-2xl font-semibold text-gray-700">
        建立使用者
    </h2>

    <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
        <form action="/admin/users" method="POST" class="space-y-4">
            <!-- Username -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="username">
                    使用者名稱 <span class="text-red-500">*</span>
                </label>
                <input
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    id="username" name="username" type="text" placeholder="輸入使用者名稱" required>
            </div>

            <% if (typeof currentUser !== 'undefined' && currentUser.role !== 'super_admin') { %>
            <!-- Basic Information Section -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-700 border-b pb-2 mb-4">基本資料</h3>
                
                <!-- Organization Name (Chinese) -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="organizationName_zh">
                        單位名稱(中文)
                    </label>
                    <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="organizationName_zh" name="organizationName_zh" type="text" placeholder="請輸入單位名稱(中文)">
                </div>

                <!-- Organization Name (English) -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="organizationName_en">
                        單位名稱(英文)
                    </label>
                    <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="organizationName_en" name="organizationName_en" type="text" placeholder="請輸入單位名稱(英文)">
                </div>

                <!-- Business ID -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="businessId">
                        統一編號
                    </label>
                    <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="businessId" name="businessId" type="text" placeholder="請輸入統一編號">
                </div>

                <!-- Project Name -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="projectId">
                        分項計畫名稱
                    </label>
                    <select
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="projectId" name="projectId">
                        <option value="">-- 不選擇任何計畫 --</option>
                        <% if (typeof projects !== 'undefined' && projects.length > 0) { %>
                            <% projects.forEach(project => { %>
                                <option value="<%= project.id %>"><%= project.name_zh %></option>
                            <% }); %>
                        <% } else { %>
                            <option value="" disabled selected>目前無計劃 通知管理員新增</option>
                        <% } %>
                    </select>
                </div>
            </div>

            <!-- Contact Information Section -->
            <div class="mb-6">
                <h3 class="text-lg font-medium text-gray-700 border-b pb-2 mb-4">聯絡人資料</h3>
                
                <!-- Contact Name (Chinese) -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="contactName_zh">
                        聯絡人姓名(中文)
                    </label>
                    <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="contactName_zh" name="contactName_zh" type="text" placeholder="請輸入聯絡人姓名(中文)">
                </div>

                <!-- Contact Name (English) -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="contactName_en">
                        聯絡人姓名(英文)
                    </label>
                    <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="contactName_en" name="contactName_en" type="text" placeholder="請輸入聯絡人姓名(英文)">
                </div>

                <!-- Contact Phone -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="contactPhone">
                        聯絡人電話
                    </label>
                    <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="contactPhone" name="contactPhone" type="text" placeholder="請輸入聯絡人電話">
                </div>
                
                <!-- Email -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="email">
                        聯絡人電子郵件 <span class="text-red-500">*</span>
                    </label>
                    <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="email" name="email" type="email" placeholder="輸入電子郵件" required>
                    <p class="mt-1 text-xs text-gray-500">預設為帳號</p>
                </div>
            </div>
            <% } else { %>
            <!-- Hidden fields for super_admin -->
            <input type="hidden" name="contactName_zh" value="">
            <input type="hidden" name="contactName_en" value="">
            <input type="hidden" name="contactPhone" value="">
            <input type="hidden" name="organizationName_zh" value="">
            <input type="hidden" name="organizationName_en" value="">
            <input type="hidden" name="businessId" value="">
            <input type="hidden" name="projectId" value="">
            
            <!-- Email for super_admin -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="email">
                    電子郵件 <span class="text-red-500">*</span>
                </label>
                <input
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    id="email" name="email" type="email" placeholder="輸入電子郵件" required>
            </div>
            <% } %>

            <!-- Password -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="password">
                    密碼 <span class="text-red-500">*</span>
                </label>
                <input
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    id="password" name="password" type="password" placeholder="輸入密碼" required>
            </div>

            <!-- Role -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="roleId">
                    角色 <span class="text-red-500">*</span>
                </label>
                <select
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    id="roleId" name="roleId" required>
                    <option value="">選擇角色</option>
                    <% roles.forEach(role => { %>
                        <option value="<%= role.id %>">
                            <%= role.name %><% if (role.name === 'visitor') { %> (僅限查看訪廠資訊)<% } else if (role.name === 'guest') { %> (可查看及新增訪廠資訊)<% } %>
                        </option>
                    <% }); %>
                </select>
                <p class="mt-1 text-xs text-gray-500">
                    <strong>訪客(guest):</strong> 可以查看及新增訪廠記錄<br>
                    <strong>僅限查看訪客(visitor):</strong> 只能查看訪廠記錄，不能新增或編輯
                </p>
            </div>

            <!-- Status -->
            <div class="mb-4">
                <div class="flex items-center">
                    <input type="checkbox" name="isActive" id="isActive" 
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" checked>
                    <label for="isActive" class="ml-2 block text-sm font-bold text-gray-700">
                        啟用帳號
                    </label>
                </div>
                <p class="mt-1 text-xs text-gray-500">未啟用的帳號將無法登入系統</p>
            </div>

            <!-- Submit Button -->
            <div class="flex justify-end mt-6">
                <a href="/admin/users"
                   class="px-4 py-2 mr-2 text-sm font-medium leading-5 text-gray-700 transition-colors duration-150 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:outline-none focus:shadow-outline-gray">
                    取消
                </a>
                <button
                    class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-blue-600 border border-transparent rounded-lg active:bg-blue-600 hover:bg-blue-700 focus:outline-none focus:shadow-outline-blue"
                    type="submit">
                    建立使用者
                </button>
            </div>
        </form>
    </div>
</div>