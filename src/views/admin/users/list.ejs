<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">使用者管理</h1>
            <a href="/admin/users/new" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-plus mr-2"></i> 新增使用者
            </a>
        </div>

        <% if (users && users.length> 0) { %>
            <div class="bg-white shadow sm:rounded-lg overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                使用者
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                聯絡資訊
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                單位資訊
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                角色
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                狀態
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                建立日期
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <% users.forEach(user=> { %>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= user.username %>
                                            </div>
                                            <div class="text-sm text-gray-500">
                                                <%= user.email %>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <% if (user.contactName_zh) { %>
                                            <div class="text-sm text-gray-900">
                                                <%= user.contactName_zh %>
                                            </div>
                                        <% } %>
                                        <% if (user.contactPhone) { %>
                                            <div class="text-sm text-gray-500">
                                                <%= user.contactPhone %>
                                            </div>
                                        <% } %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <% if (user.organizationName_zh) { %>
                                            <div class="text-sm font-medium text-gray-900">
                                                <%= user.organizationName_zh %>
                                            </div>
                                        <% } %>
                                        <% if (user.businessId) { %>
                                            <div class="text-sm text-gray-500">
                                                統編: <%= user.businessId %>
                                            </div>
                                        <% } %>
                                        <% if (user.project) { %>
                                            <div class="text-sm text-blue-600">
                                                計畫: <%= user.project.name_zh %>
                                            </div>
                                        <% } else if (user.projectId) { %>
                                            <div class="text-sm text-blue-600">
                                                計畫ID: <%= user.projectId %>
                                            </div>
                                        <% } %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span
                                        class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                                        <%= user.role.name %>
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <% if (user.isActive) { %>
                                        <span
                                            class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            啟用
                                        </span>
                                        <% } else { %>
                                            <span
                                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                停用
                                            </span>
                                            <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <%= new Date(user.createdAt).toLocaleDateString() %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="/admin/users/edit/<%= user.id %>"
                                        class="text-blue-600 hover:text-blue-900 mr-4">編輯</a>
                                    <form action="/admin/users/<%= user.id %>/delete" method="POST" class="inline"
                                        onsubmit="return confirm('您確定要刪除此使用者嗎？');">
                                        <button type="submit" class="text-red-600 hover:text-red-900">刪除</button>
                                    </form>
                                </td>
                            </tr>
                            <% }); %>
                    </tbody>
                </table>
            </div>
            <% } else { %>
                <div class="bg-white shadow overflow-hidden sm:rounded-lg p-6 text-center text-gray-500">
                    尚未找到任何使用者。<a href="/admin/users/new" class="text-blue-600 hover:text-blue-900">立即建立一個</a>。
                </div>
                <% } %>
    </div>