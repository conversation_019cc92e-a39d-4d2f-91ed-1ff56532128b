<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">聯絡表單提交</h1>
      <div class="flex gap-2">
        <a href="/admin/contact/agreement" class="bg-white hover:bg-gray-100 text-green-500 border border-green-500 font-bold py-2 px-4 rounded">
          <i class="fas fa-file-signature mr-2"></i> 管理同意書
        </a>
        <a href="/admin/contact/categories" class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-bold py-2 px-4 rounded">
          <i class="fas fa-cog mr-2"></i> 管理分類
        </a>
      </div>
    </div>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <!-- Filters -->
      <div class="mb-6">
        <form action="/admin/contact" method="GET" class="flex flex-wrap items-end gap-4">
          <div>
            <label class="block text-gray-700 text-sm font-bold mb-2" for="category">
              分類
            </label>
            <select
              class="shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="category" name="category">
              <option value="">所有分類</option>
              <% categories.forEach(function(category) { %>
                <option value="<%= category.id %>" <%=currentCategory==category.id ? 'selected' : '' %>>
                  <%= category.name_tw %> / <%= category.name_en %>
                </option>
                <% }); %>
            </select>
          </div>
          <div>
            <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
              狀態
            </label>
            <select
              class="shadow appearance-none border rounded py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="status" name="status">
              <option value="">所有狀態</option>
              <option value="pending" <%=currentStatus==='pending' ? 'selected' : '' %>>待處理</option>
              <option value="processed" <%=currentStatus==='processed' ? 'selected' : '' %>>已處理</option>
              <option value="archived" <%=currentStatus==='archived' ? 'selected' : '' %>>已封存</option>
            </select>
          </div>
          <div class="flex gap-2">
            <button type="submit"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              <i class="fas fa-filter mr-2"></i> 篩選
            </button>
            <a href="/admin/contact"
              class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
              <i class="fas fa-times mr-2"></i> 清除
            </a>
          </div>
        </form>
      </div>

      <% if (contacts && contacts.length> 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white" id="dataTable">
            <thead>
              <tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left whitespace-nowrap">日期</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">姓名</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">電子郵件</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">分類</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">狀態</th>
                <th class="py-3 px-6 text-center whitespace-nowrap">操作</th>
              </tr>
            </thead>
            <tbody class="text-gray-600 text-sm">
              <% contacts.forEach(function(contact) { %>
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= new Date(contact.createdAt).toLocaleDateString() %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= contact.name %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= contact.email %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <% if (contact.category) { %>
                      <%= contact.category.name_tw %> / <%= contact.category.name_en %>
                          <% } else { %>
                            <span class="text-gray-400">無</span>
                            <% } %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <% if (contact.status==='pending' ) { %>
                      <span class="bg-yellow-200 text-yellow-800 py-1 px-3 rounded-full text-xs">待處理</span>
                      <% } else if (contact.status==='processed' ) { %>
                        <span class="bg-green-200 text-green-800 py-1 px-3 rounded-full text-xs">已處理</span>
                        <% } else if (contact.status==='archived' ) { %>
                          <span class="bg-gray-200 text-gray-800 py-1 px-3 rounded-full text-xs">已封存</span>
                          <% } %>
                  </td>
                  <td class="py-3 px-6 text-center whitespace-nowrap">
                    <div class="flex item-center justify-center">
                      <a href="/admin/contact/view/<%= contact.id %>"
                        class="text-blue-600 hover:text-blue-900 mx-1">
                        <i class="fas fa-eye"></i>
                      </a>
                      <button class="text-red-600 hover:text-red-900 mx-1"
                        onclick="document.getElementById('deleteModal<%= contact.id %>').classList.remove('hidden')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>

                <!-- Delete Modal -->
                <div id="deleteModal<%= contact.id %>"
                  class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
                  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                      <h3 class="text-lg leading-6 font-medium text-gray-900">確認刪除</h3>
                      <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                          您確定要刪除來自 <%= contact.name %> 的聯絡表單提交嗎？
                        </p>
                      </div>
                      <div class="flex justify-end mt-4">
                        <button type="button"
                          class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                          onclick="document.getElementById('deleteModal<%= contact.id %>').classList.add('hidden')">
                          取消
                        </button>
                        <form action="/admin/contact/<%= contact.id %>/delete" method="POST" class="inline">
                          <button type="submit"
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            刪除
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
                <% }); %>
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        <% if (pagination.totalPages> 1) { %>
          <div class="flex justify-center mt-6">
            <nav class="inline-flex rounded-md shadow">
              <% if (pagination.page> 1) { %>
                <a href="/admin/contact?page=<%= pagination.page - 1 %><%= currentCategory ? '&category=' + currentCategory : '' %><%= currentStatus ? '&status=' + currentStatus : '' %>"
                  class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">上一頁</span>
                  <i class="fas fa-chevron-left"></i>
                </a>
                <% } else { %>
                  <span
                    class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                    <span class="sr-only">上一頁</span>
                    <i class="fas fa-chevron-left"></i>
                  </span>
                  <% } %>

                    <% for (let i=1; i <=pagination.totalPages; i++) { %>
                      <% if (i===pagination.page) { %>
                        <span
                          class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">
                          <%= i %>
                        </span>
                        <% } else { %>
                          <a href="/admin/contact?page=<%= i %><%= currentCategory ? '&category=' + currentCategory : '' %><%= currentStatus ? '&status=' + currentStatus : '' %>"
                            class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            <%= i %>
                          </a>
                          <% } %>
                            <% } %>

                              <% if (pagination.page < pagination.totalPages) { %>
                                <a href="/admin/contact?page=<%= pagination.page + 1 %><%= currentCategory ? '&category=' + currentCategory : '' %><%= currentStatus ? '&status=' + currentStatus : '' %>"
                                  class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                                  <span class="sr-only">下一頁</span>
                                  <i class="fas fa-chevron-right"></i>
                                </a>
                                <% } else { %>
                                  <span
                                    class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 text-sm font-medium text-gray-400 cursor-not-allowed">
                                    <span class="sr-only">下一頁</span>
                                    <i class="fas fa-chevron-right"></i>
                                  </span>
                                  <% } %>
            </nav>
          </div>
          <% } %>
            <% } else { %>
              <div class="text-center py-8">
                <p class="text-gray-500 mb-4">未找到任何聯絡表單提交。</p>
              </div>
              <% } %>
    </div>
  </div>