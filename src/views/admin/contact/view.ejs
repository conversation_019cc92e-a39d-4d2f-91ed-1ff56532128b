<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">查看聯絡表單提交</h1>
      <a href="/admin/contact" class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-bold py-2 px-4 rounded">
        <i class="fas fa-arrow-left mr-2"></i> 返回列表
      </a>
    </div>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <div class="mb-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">聯絡詳情</h2>
          <div class="flex space-x-2">
            <form action="/admin/contact/<%= contact.id %>/status" method="POST" class="inline-block">
              <input type="hidden" name="status" value="<%= contact.status === 'pending' ? 'processed' : 'pending' %>">
              <button type="submit"
                class="<%= contact.status === 'pending' ? 'bg-green-500 hover:bg-green-700' : 'bg-yellow-500 hover:bg-yellow-700' %> text-white font-bold py-1 px-3 rounded">
                <i class="fas <%= contact.status === 'pending' ? 'fa-check' : 'fa-undo' %> mr-1"></i>
                <%= contact.status==='pending' ? '標記為已處理' : '標記為待處理' %>
              </button>
            </form>
            <form action="/admin/contact/<%= contact.id %>/status" method="POST" class="inline-block">
              <input type="hidden" name="status" value="archived">
              <button type="submit" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-1 px-3 rounded">
                <i class="fas fa-archive mr-1"></i> 封存
              </button>
            </form>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <div class="mb-4">
              <p class="text-sm font-semibold text-gray-600">狀態</p>
              <div class="mt-1">
                <% if (contact.status==='pending' ) { %>
                  <span class="bg-yellow-200 text-yellow-800 py-1 px-3 rounded-full text-xs">待處理</span>
                  <% } else if (contact.status==='processed' ) { %>
                    <span class="bg-green-200 text-green-800 py-1 px-3 rounded-full text-xs">已處理</span>
                    <% } else if (contact.status==='archived' ) { %>
                      <span class="bg-gray-200 text-gray-800 py-1 px-3 rounded-full text-xs">已封存</span>
                      <% } %>
              </div>
            </div>

            <div class="mb-4">
              <p class="text-sm font-semibold text-gray-600">提交日期</p>
              <p class="mt-1">
                <%= new Date(contact.createdAt).toLocaleString() %>
              </p>
            </div>

            <div class="mb-4">
              <p class="text-sm font-semibold text-gray-600">姓名</p>
              <p class="mt-1">
                <%= contact.name %>
              </p>
            </div>

            <div class="mb-4">
              <p class="text-sm font-semibold text-gray-600">電子郵件</p>
              <p class="mt-1"><a href="mailto:<%= contact.email %>" class="text-blue-600 hover:underline">
                  <%= contact.email %>
                </a></p>
            </div>
          </div>

          <div>
            <div class="mb-4">
              <p class="text-sm font-semibold text-gray-600">公司</p>
              <p class="mt-1">
                <%= contact.company || '未提供' %>
              </p>
            </div>

            <div class="mb-4">
              <p class="text-sm font-semibold text-gray-600">電話</p>
              <p class="mt-1">
                <%= contact.phone || '未提供' %>
              </p>
            </div>

            <div class="mb-4">
              <p class="text-sm font-semibold text-gray-600">分類</p>
              <p class="mt-1">
                <% if (contact.category) { %>
                  <%= contact.category.name_tw %> / <%= contact.category.name_en %>
                      <% } else { %>
                        <span class="text-gray-400">無</span>
                        <% } %>
              </p>
            </div>

            <div class="mb-4">
              <p class="text-sm font-semibold text-gray-600">條款同意</p>
              <p class="mt-1">
                <% if (contact.agreeTerms) { %>
                  <span class="text-green-600"><i class="fas fa-check-circle mr-1"></i> 已同意</span>
                  <% } else { %>
                    <span class="text-red-600"><i class="fas fa-times-circle mr-1"></i> 未同意</span>
                    <% } %>
              </p>
            </div>
          </div>
        </div>

        <div class="mt-6">
          <p class="text-sm font-semibold text-gray-600">訊息</p>
          <div class="mt-2 p-4 bg-gray-50 rounded border border-gray-200">
            <p class="whitespace-pre-wrap">
              <%= contact.message %>
            </p>
          </div>
        </div>
      </div>

      <div class="flex justify-end mt-6">
        <button class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded mr-2"
          onclick="document.getElementById('deleteModal').classList.remove('hidden')">
          <i class="fas fa-trash mr-2"></i> 刪除
        </button>
      </div>

      <!-- Delete Modal -->
      <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
          <div class="mt-3 text-center">
            <h3 class="text-lg leading-6 font-medium text-gray-900">確認刪除</h3>
            <div class="mt-2 px-7 py-3">
              <p class="text-sm text-gray-500">
                您確定要刪除來自 <%= contact.name %> 的聯絡表單提交嗎？
              </p>
            </div>
            <div class="flex justify-end mt-4">
              <button type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                onclick="document.getElementById('deleteModal').classList.add('hidden')">
                取消
              </button>
              <form action="/admin/contact/<%= contact.id %>/delete" method="POST" class="inline">
                <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                  刪除
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>