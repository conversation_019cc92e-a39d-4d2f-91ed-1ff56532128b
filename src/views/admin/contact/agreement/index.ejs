<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">管理聯絡表單同意書</h1>
      <a href="/admin/contact" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
        <i class="fas fa-arrow-left mr-2"></i> 返回聯絡表單
      </a>
    </div>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <form action="/admin/contact/agreement" method="POST" id="agreementForm">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-gray-700 text-sm font-bold mb-2" for="editor_tw">
              內容 (中文) <span class="text-red-500">*</span>
            </label>
            <div id="editor_tw" class="h-64 mt-1">
              <%- agreement && agreement.content_tw ? agreement.content_tw : '<p>請在此輸入中文同意書內容...</p>' %>
            </div>
            <input type="hidden" name="content_tw" id="content_tw">
          </div>
          <div>
            <label class="block text-gray-700 text-sm font-bold mb-2" for="editor_en">
              內容 (英文) <span class="text-red-500">*</span>
            </label>
            <div id="editor_en" class="h-64 mt-1">
              <%- agreement && agreement.content_en ? agreement.content_en : '<p>Enter English agreement content here...</p>' %>
            </div>
            <input type="hidden" name="content_en" id="content_en">
          </div>
        </div>

        <div class="flex items-center justify-end">
          <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            type="submit">
            <%= agreement ? '更新同意書' : '建立同意書' %>
          </button>
        </div>
      </form>
    </div>
  </div>


  <script>
    (function () {
      
          // Initialize English editor with simple configuration
          var quill_en = new Quill('#editor_en', {
            theme: 'snow',
            modules: {
              toolbar: [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'align': [] }],
                [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                ['link'],
                ['clean']
              ]
            },
            placeholder: 'Enter agreement content in English...'
          });

          // Initialize Traditional Chinese editor with simple configuration
          var quill_tw = new Quill('#editor_tw', {
            theme: 'snow',
            modules: {
              toolbar: [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'align': [] }],
                [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                ['link'],
                ['clean']
              ]
            },
            placeholder: '輸入中文同意書內容...'
          });

          // Function to truncate content if needed
          function truncateContent(content, maxLength) {
            // Use 100MB as the maximum length for LongText fields
            const MAX_LONGTEXT_LENGTH = 100 * 1024 * 1024; // 100MB

            // If maxLength is not provided, use the maximum LongText length
            const limit = maxLength || MAX_LONGTEXT_LENGTH;

            if (content && content.length > limit) {
              console.warn(`Content truncated from ${content.length} to ${limit} characters`);
              return content.substring(0, limit);
            }
            return content;
          }

          // Form validation before submit
          $('#agreementForm').on('submit', function (e) {
            // Update hidden inputs with Quill content
            const htmlEn = quill_en.root.innerHTML;
            const htmlTw = quill_tw.root.innerHTML;

            // Using LongText field now, so we can store much more content
            $('#content_en').val(truncateContent(htmlEn));
            $('#content_tw').val(truncateContent(htmlTw));

            return true;
          });
        
    })();
  </script>

  <style>
    /* Custom styles for Quill editors */
    #editor_en,
    #editor_tw {
      height: 500px;
      margin-bottom: 30px;
    }

    /* Fix toolbar positioning */
    .ql-toolbar.ql-snow {
      border: 1px solid #ccc;
      box-sizing: border-box;
      font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
      padding: 8px;
    }

    .ql-container.ql-snow {
      border: 1px solid #ccc;
      border-top: 0px;
    }

    /* Ensure editor has proper height */
    .ql-editor {
      min-height: 450px;
    }
  </style> 