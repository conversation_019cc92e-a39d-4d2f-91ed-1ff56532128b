<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <div class="bg-white shadow rounded-lg p-6">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold">編輯分類</h1>
                    <a href="/admin/categories" class="text-blue-600 hover:text-blue-800">
                        返回分類列表
                    </a>
                </div>

                <form id="updateForm" action="/admin/categories/edit/<%= category.id %>" method="POST">
                    <!-- Language Tabs -->
                    <%- include('../../partials/admin-language-switcher', { title: '內容語言' , activeTab: 'en' }) %>

                        <!-- English Content -->
                        <div class="language-content language-content-en">
                            <div class="mb-4">
                                <label for="name_en" class="block text-sm font-medium text-gray-700">名稱
                                    (英文)</label>
                                <input type="text" id="name_en" name="name_en"
                                    value="<%= category.name_en || category.name %>" required
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>

                            <div class="mb-4">
                                <label for="description_en" class="block text-sm font-medium text-gray-700">描述
                                    (英文)</label>
                                <textarea id="description_en" name="description_en" rows="4"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"><%= category.description_en || category.description || '' %></textarea>
                            </div>
                        </div>

                        <!-- Traditional Chinese Content -->
                        <div class="language-content language-content-tw hidden">
                            <div class="mb-4">
                                <label for="name_tw" class="block text-sm font-medium text-gray-700">名稱 (中文)</label>
                                <input type="text" id="name_tw" name="name_tw" value="<%= category.name_tw || '' %>"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            </div>

                            <div class="mb-4">
                                <label for="description_tw" class="block text-sm font-medium text-gray-700">描述
                                    (中文)</label>
                                <textarea id="description_tw" name="description_tw" rows="4"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"><%= category.description_tw || '' %></textarea>
                            </div>
                        </div>

                        <!-- Common Fields -->
                        <div class="border-t pt-4 mt-6">
                            <div class="mb-4">
                                <label for="parentId" class="block text-sm font-medium text-gray-700">上層
                                    分類</label>
                                <select id="parentId" name="parentId"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="">無 (頂層分類)</option>
                                    <% categories.forEach(function(cat) { %>
                                        <% if (cat.id !==category.id) { %>
                                            <option value="<%= cat.id %>" <%=category.parentId===cat.id ? 'selected'
                                                : '' %>>
                                                <%= cat.name_en || cat.name %>
                                            </option>
                                            <% } %>
                                                <% }); %>
                                </select>
                            </div>

                            <div class="mb-4">
                                <label for="type" class="block text-sm font-medium text-gray-700">分類類型</label>
                                <select id="type" name="type"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                    <option value="article" <%=category.type==='article' ? 'selected' : '' %>>文章
                                    </option>
                                    <option value="download" <%=category.type==='download' ? 'selected' : '' %>>下載
                                    </option>
                                    <option value="faq" <%=category.type==='faq' ? 'selected' : '' %>>常見問題</option>
                                </select>
                            </div>

                            <div class="mb-6">
                                <label for="order" class="block text-sm font-medium text-gray-700">顯示順序</label>
                                <input type="number" id="order" name="order" value="<%= category.order || 0 %>" min="0"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                                <p class="mt-1 text-sm text-gray-500">數字越小排序越前</p>
                            </div>

                            <div class="flex justify-between">
                                <button type="submit"
                                    class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    更新分類
                                </button>
                                <a href="/admin/categories"
                                    class="bg-gray-100 text-gray-700 px-4 py-2 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                    取消
                                </a>
                            </div>
                        </div>
                </form>

                <hr class="my-8">

                <% if (category.children && category.children.length> 0) { %>
                    <div class="mb-6">
                        <h2 class="text-lg font-semibold text-gray-900 mb-2">子分類</h2>
                        <div class="bg-gray-50 rounded-md p-4">
                            <ul class="space-y-2">
                                <% category.children.forEach(function(child) { %>
                                    <li class="flex justify-between items-center">
                                        <span class="text-gray-700">
                                            <%= child.name_en || child.name %>
                                        </span>
                                        <a href="/admin/categories/edit/<%= child.id %>"
                                            class="text-blue-600 hover:text-blue-800">
                                            編輯
                                        </a>
                                    </li>
                                    <% }); %>
                            </ul>
                        </div>
                    </div>
                    <% } %>

                        <form id="deleteForm" action="/admin/categories/<%= category.id %>" method="POST" class="mt-6"
                            onsubmit="return confirm('您確定要刪除此分類嗎？這將同時刪除所有子分類，且無法復原。');">
                            <input type="hidden" name="_method" value="DELETE">
                            <button type="submit"
                                class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                                刪除分類
                            </button>
                        </form>
            </div>
        </div>
    </div>