<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">製造平台分類</h1>
      <div class="flex">
        <a href="/admin/platforms/categories/create"
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded mr-2">
          <i class="fas fa-plus mr-2"></i> 新增分類
        </a>
        <a href="/admin/platforms" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
          <i class="fas fa-arrow-left mr-2"></i> 返回製造平台項目
        </a>
      </div>
    </div>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <% if (categories && categories.length> 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white" id="dataTable">
            <thead>
              <tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left whitespace-nowrap">排序</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">名稱 (中文)</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">名稱 (英文)</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">描述 (中文)</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">描述 (英文)</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">項目數</th>
                <th class="py-3 px-6 text-center whitespace-nowrap">操作</th>
              </tr>
            </thead>
            <tbody class="text-gray-600 text-sm">
              <% categories.forEach(function(category) { %>
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= category.order %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= category.name_tw %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= category.name_en %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= category.description_tw || '-' %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= category.description_en || '-' %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <span class="bg-blue-200 text-blue-800 py-1 px-3 rounded-full text-xs">
                      <%= category.platforms ? category.platforms.length : 0 %>
                    </span>
                  </td>
                  <td class="py-3 px-6 text-center whitespace-nowrap">
                    <div class="flex item-center justify-center">
                      <a href="/admin/platforms/categories/edit/<%= category.id %>"
                        class="text-blue-600 hover:text-blue-900 mr-4">
                        <i class="fas fa-edit"></i>
                      </a>
                      <% if (!category.platforms || category.platforms.length===0) { %>
                        <button class="text-red-600 hover:text-red-900"
                          onclick="document.getElementById('deleteModal<%= category.id %>').classList.remove('hidden')">
                          <i class="fas fa-trash"></i>
                        </button>
                        <% } else { %>
                          <button
                            class="bg-red-300 text-white font-bold py-1 px-3 rounded opacity-50 cursor-not-allowed"
                            title="無法刪除含有項目的分類">
                            <i class="fas fa-trash"></i> 刪除
                          </button>
                          <% } %>
                    </div>
                  </td>
                </tr>

                <!-- Delete Modal -->
                <div id="deleteModal<%= category.id %>"
                  class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
                  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                      <h3 class="text-lg leading-6 font-medium text-gray-900">確認刪除</h3>
                      <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                          您確定要刪除分類 "<%= category.name_tw %>" 嗎？
                        </p>
                      </div>
                      <div class="flex justify-center mt-4 px-4 py-3">
                        <button type="button"
                          class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2"
                          onclick="document.getElementById('deleteModal<%= category.id %>').classList.add('hidden')">
                          取消
                        </button>
                        <form action="/admin/platforms/categories/<%= category.id %>/delete" method="POST">
                          <button type="submit"
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            刪除
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
                <% }); %>
            </tbody>
          </table>
        </div>
        <% } else { %>
          <div class="text-center py-8">
            <p class="text-gray-500 mb-4">尚未建立任何製造平台分類。</p>
            <a href="/admin/platforms/categories/create"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              <i class="fas fa-plus mr-2"></i> 新增第一個分類
            </a>
          </div>
          <% } %>
    </div>
  </div>

  <%- contentFor('scripts') %>
    <script>
      $(document).ready(function () {
        $('#dataTable').DataTable({
          "order": [[0, "asc"]]
        });
      });
    </script>