<%- contentFor('title') %>
<%= title %>
<% end %>

<%- contentFor('styles') %>
<style>
  .group-card {
    transition: all 0.3s ease;
  }
  .group-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
</style>
<% end %>

<div class="container mx-auto px-4 py-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold text-gray-900"><%= title %></h1>
      <p class="text-gray-600 mt-2">管理平台附件的分組設定</p>
    </div>
    <div class="flex space-x-3">
      <a href="/admin/platforms/attachment-categories" 
         class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-lg transition-colors">
        <i class="fas fa-folder mr-2"></i>管理分類
      </a>
      <a href="/admin/platforms/attachment-groups/create" 
         class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-colors">
        <i class="fas fa-plus mr-2"></i>新增分組
      </a>
    </div>
  </div>

  <!-- Breadcrumb -->
  <nav class="flex mb-6" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li class="inline-flex items-center">
        <a href="/admin/dashboard" class="text-gray-700 hover:text-blue-600">
          <i class="fas fa-home mr-2"></i>後台首頁
        </a>
      </li>
      <li>
        <div class="flex items-center">
          <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
          <a href="/admin/platforms" class="text-gray-700 hover:text-blue-600">平台管理</a>
        </div>
      </li>
      <li>
        <div class="flex items-center">
          <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
          <span class="text-gray-500">附件分組</span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Category Filter -->
  <% if (categories && categories.length > 0) { %>
    <div class="bg-white rounded-lg shadow-md p-4 mb-6">
      <div class="flex items-center space-x-4">
        <label class="text-sm font-medium text-gray-700">篩選分類：</label>
        <select id="categoryFilter" class="border border-gray-300 rounded-md px-3 py-2 text-sm">
          <option value="">所有分類</option>
          <% categories.forEach(function(category) { %>
            <option value="<%= category.id %>" <%= selectedCategoryId === category.id ? 'selected' : '' %>>
              <%= category.name_tw %> / <%= category.name_en %>
            </option>
          <% }); %>
        </select>
      </div>
    </div>
  <% } %>

  <!-- Flash Messages -->
  <% if (typeof success_msg !== 'undefined' && success_msg.length > 0) { %>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      <%= success_msg %>
    </div>
  <% } %>

  <% if (typeof error_msg !== 'undefined' && error_msg.length > 0) { %>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <%= error_msg %>
    </div>
  <% } %>

  <!-- Groups Grid -->
  <% if (groups && groups.length > 0) { %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% groups.forEach(function(group) { %>
        <div class="group-card bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <!-- Group Header -->
          <div class="flex justify-between items-start mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-1">
                <%= group.name_tw %>
              </h3>
              <p class="text-sm text-gray-600 mb-2">
                <%= group.name_en %>
              </p>
              <% if (group.category) { %>
                <span class="inline-block bg-purple-100 text-purple-800 text-xs font-medium px-2 py-1 rounded">
                  <%= group.category.name_tw %>
                </span>
              <% } %>
            </div>
            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
              順序: <%= group.order %>
            </span>
          </div>

          <!-- Description -->
          <% if (group.description_tw || group.description_en) { %>
            <div class="mb-4">
              <% if (group.description_tw) { %>
                <p class="text-sm text-gray-700 mb-1"><%= group.description_tw %></p>
              <% } %>
              <% if (group.description_en) { %>
                <p class="text-xs text-gray-500"><%= group.description_en %></p>
              <% } %>
            </div>
          <% } %>

          <!-- Stats -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center text-sm text-gray-600">
              <i class="fas fa-paperclip mr-2"></i>
              <span><%= group._count ? group._count.attachments : 0 %> 個附件</span>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex space-x-2">
            <a href="/admin/platforms/attachment-groups/edit/<%= group.id %>" 
               class="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white text-sm font-medium py-2 px-3 rounded text-center transition-colors">
              <i class="fas fa-edit mr-1"></i>編輯
            </a>
            <% if (!group._count || group._count.attachments === 0) { %>
              <form action="/admin/platforms/attachment-groups/<%= group.id %>/delete" method="POST" class="inline" 
                    onsubmit="return confirm('確定要刪除此分組嗎？')">
                <button type="submit" 
                        class="bg-red-500 hover:bg-red-600 text-white text-sm font-medium py-2 px-3 rounded transition-colors">
                  <i class="fas fa-trash"></i>
                </button>
              </form>
            <% } else { %>
              <button disabled 
                      class="bg-gray-300 text-gray-500 text-sm font-medium py-2 px-3 rounded cursor-not-allowed"
                      title="包含附件的分組無法刪除">
                <i class="fas fa-trash"></i>
              </button>
            <% } %>
          </div>
        </div>
      <% }); %>
    </div>
  <% } else { %>
    <!-- Empty State -->
    <div class="text-center py-12">
      <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <i class="fas fa-layer-group text-3xl text-gray-400"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">尚無附件分組</h3>
      <p class="text-gray-500 mb-6">
        <% if (categories && categories.length === 0) { %>
          請先建立附件分類，然後再建立分組
        <% } else { %>
          開始建立您的第一個附件分組
        <% } %>
      </p>
      <div class="flex justify-center space-x-4">
        <% if (categories && categories.length === 0) { %>
          <a href="/admin/platforms/attachment-categories/create" 
             class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-lg transition-colors">
            <i class="fas fa-folder mr-2"></i>新增分類
          </a>
        <% } else { %>
          <a href="/admin/platforms/attachment-groups/create" 
             class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-colors">
            <i class="fas fa-plus mr-2"></i>新增分組
          </a>
        <% } %>
      </div>
    </div>
  <% } %>
</div>

<%- contentFor('scripts') %>
<script>
  // Category filter functionality
  const categoryFilter = document.getElementById('categoryFilter');
  if (categoryFilter) {
    categoryFilter.addEventListener('change', function() {
      const categoryId = this.value;
      const url = new URL(window.location);

      if (categoryId) {
        url.searchParams.set('categoryId', categoryId);
      } else {
        url.searchParams.delete('categoryId');
      }

      window.location.href = url.toString();
    });
  }
</script>
<% end %>
