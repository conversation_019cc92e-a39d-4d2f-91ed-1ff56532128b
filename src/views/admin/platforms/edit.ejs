<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">編輯製造平台項目</h1>
      <a href="/admin/platforms" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
        <i class="fas fa-arrow-left mr-2"></i> 返回列表
      </a>
    </div>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <div class="mb-4">
        <form action="/admin/platforms/<%= item.id %>" method="POST" enctype="multipart/form-data">
          <!-- Basic Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2" for="title_tw">
                標題 (中文) <span class="text-red-500">*</span>
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="title_tw" name="title_tw" type="text" value="<%= item.title_tw %>" required>
            </div>
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2" for="title_en">
                標題 (英文) <span class="text-red-500">*</span>
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="title_en" name="title_en" type="text" value="<%= item.title_en %>" required>
            </div>
          </div>

          <!-- Category and Type Selection -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2" for="categoryId">
                分類 <span class="text-red-500 category-required">*</span>
              </label>
              <select
                class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="categoryId" name="categoryId" required>
                <option value="">選擇分類</option>
                <% categories.forEach(function(category) { %>
                  <option value="<%= category.id %>" <%=item.categoryId===category.id ? 'selected' : '' %>>
                    <%= category.name_tw %> / <%= category.name_en %>
                  </option>
                  <% }); %>
              </select>
            </div>
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2" for="type">
                內容類型 <span class="text-red-500">*</span>
              </label>
              <select
                class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="type" name="type" required onchange="toggleContentFields()">
                <option value="plain_text" <%=item.type==='plain_text' ? 'selected' : '' %>>編輯器</option>
                <option value="image" <%=item.type==='image' ? 'selected' : '' %>>圖片與說明</option>
                <option value="attachment_only" <%=item.type==='attachment_only' ? 'selected' : '' %>>僅附件</option>
              </select>
            </div>
          </div>

          <!-- Content Fields -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <div class="content-field" id="content_tw_field">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="editor_tw">
                  內容 (中文) <span class="text-red-500 content-required">*</span>
                </label>
                <div id="editor_tw" class="h-64 mt-1"></div>
                <input type="hidden" name="content_tw" id="content_tw" value="<%= item.content_tw %>">
              </div>
            </div>
            <div>
              <div class="content-field" id="content_en_field">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="editor_en">
                  內容 (英文) <span class="text-red-500 content-required">*</span>
                </label>
                <div id="editor_en" class="h-64 mt-1"></div>
                <input type="hidden" name="content_en" id="content_en" value="<%= item.content_en %>">
              </div>
            </div>
          </div>

          <!-- Current Image -->
          <% /* Debug info for image path */ %>
            <% console.log('Image path in template:', item.imagePath); %>
              <% if (item.imagePath) { %>
                <div class="mb-4" id="current-image-section">
                  <div class="bg-gray-100 rounded p-4">
                    <h6 class="font-bold mb-2">目前圖片 (中文)</h6>
                    <div class="flex items-center">
                      <img src="<%= item.imagePath %>" alt="Current image"
                        class="h-24 w-auto object-contain mr-4 border rounded">
                      <div class="flex items-center">
                        <input type="checkbox" id="removeImage" name="removeImage" value="true" class="mr-2">
                        <label for="removeImage">移除此圖片</label>
                      </div>
                    </div>
                  </div>
                </div>
                <% } %>
                
              <!-- Current English Image -->
              <% if (item.imagePathEn) { %>
                <div class="mb-4" id="current-image-en-section">
                  <div class="bg-gray-100 rounded p-4">
                    <h6 class="font-bold mb-2">目前圖片 (英文)</h6>
                    <div class="flex items-center">
                      <img src="<%= item.imagePathEn %>" alt="Current English image"
                        class="h-24 w-auto object-contain mr-4 border rounded">
                      <div class="flex items-center">
                        <input type="checkbox" id="removeImageEn" name="removeImageEn" value="true" class="mr-2">
                        <label for="removeImageEn">移除此圖片</label>
                      </div>
                    </div>
                  </div>
                </div>
                <% } %>

                  <!-- Image Upload -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label class="block text-gray-700 text-sm font-bold mb-2" for="image">
                        <%= item.imagePath ? '替換圖片 (中文)' : '圖片 (中文)' %>
                      </label>
                      <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="image" name="image" type="file" accept="image/*">
                      <p class="text-gray-600 text-xs italic mt-1">建議尺寸：1200x500像素。最大檔案大小：<%= process.env.MAX_IMAGE_SIZE ?
                          (process.env.MAX_IMAGE_SIZE / (1024 * 1024)).toFixed(0) : 5 %>MB。支援格式：<%=
                            process.env.ALLOWED_IMAGE_TYPES ? process.env.ALLOWED_IMAGE_TYPES.toUpperCase()
                            : 'JPG, PNG, GIF, WebP' %>
                      </p>
                    </div>
                    <div>
                      <label class="block text-gray-700 text-sm font-bold mb-2" for="url">
                        外部連結
                      </label>
                      <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="url" name="url" type="url" value="<%= item.url || '' %>">
                      <p class="text-gray-600 text-xs italic mt-1">選填：連結到外部網站</p>
                    </div>
                  </div>
                  
                  <!-- Chinese Image Alt Text -->
                  <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="alt_tw">
                      中文圖片替代文字 (Alt Text)
                    </label>
                    <input
                      class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      id="alt_tw" name="alt_tw" type="text" value="<%= item.alt_tw || '' %>" placeholder="輸入圖片替代文字，提高無障礙性">
                    <p class="text-gray-600 text-xs italic mt-1">為視障人士描述中文版圖片內容，提高無障礙性。</p>
                  </div>
                  
                  <!-- English Image Upload -->
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div>
                      <label class="block text-gray-700 text-sm font-bold mb-2" for="imageEn">
                        <%= item.imagePathEn ? '替換圖片 (英文)' : '圖片 (英文)' %>
                      </label>
                      <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="imageEn" name="imageEn" type="file" accept="image/*">
                      <p class="text-gray-600 text-xs italic mt-1">建議尺寸：1200x500像素。最大檔案大小：<%= process.env.MAX_IMAGE_SIZE ?
                          (process.env.MAX_IMAGE_SIZE / (1024 * 1024)).toFixed(0) : 5 %>MB。支援格式：<%=
                            process.env.ALLOWED_IMAGE_TYPES ? process.env.ALLOWED_IMAGE_TYPES.toUpperCase()
                            : 'JPG, PNG, GIF, WebP' %>
                      </p>
                    </div>
                    <div>
                      <label class="block text-gray-700 text-sm font-bold mb-2" for="urlEn">
                        外部連結 (英文)
                      </label>
                      <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="urlEn" name="urlEn" type="url" value="<%= item.urlEn || '' %>">
                      <p class="text-gray-600 text-xs italic mt-1">選填：連結到外部網站 (英文)</p>
                    </div>
                  </div>

                  <!-- English Image Alt Text -->
                  <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="alt_en">
                      英文圖片替代文字 (Alt Text)
                    </label>
                    <input
                      class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                      id="alt_en" name="alt_en" type="text" value="<%= item.alt_en || '' %>" placeholder="Enter image alternative text for accessibility">
                    <p class="text-gray-600 text-xs italic mt-1">Describe image content for visually impaired users to improve accessibility.</p>
                  </div>

                  <!-- Current Attachments -->
                  <% /* Debug info console.log('Item attachments in template:', item.attachments);
                    console.log('Attachments length:', item.attachments ? item.attachments.length : 0); */ %>

                    <!-- Attachment Section - More prominent for attachment_only type -->
                    <div
                      class="mb-4 <%= item.type === 'attachment_only' ? 'border-2 border-blue-300 p-4 rounded-lg' : '' %>">
                      <h3 class="text-xl font-bold mb-4 <%= item.type === 'attachment_only' ? 'text-blue-600' : '' %>">
                        <%= item.type==='attachment_only' ? '附件 (必填)' : '附件' %>
                      </h3>

                      <!-- Current Attachments -->
                      <% if (item.attachments && item.attachments.length> 0) { %>
                        <div class="mb-4">
                          <div class="bg-gray-100 rounded p-4">
                            <h6 class="font-bold mb-2">目前附件</h6>
                            <div class="divide-y divide-gray-200">
                              <% item.attachments.forEach(function(attachment) { %>
                                <div class="flex justify-between items-center py-2">
                                  <div class="flex items-center">
                                    <i class="fas fa-file mr-2"></i>
                                    <%= attachment.originalName %>
                                      <span class="text-gray-500 text-sm ml-2">(<%= Math.round(attachment.size / 1024)
                                          %>
                                          KB)</span>
                                  </div>
                                  <div class="flex items-center">
                                    <input type="checkbox" name="removeAttachments" value="<%= attachment.id %>"
                                      class="mr-2">
                                    <label>移除</label>
                                  </div>
                                </div>
                                <!-- Custom attachment name fields -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 py-2">
                                  <div>
                                    <label class="block text-gray-700 text-sm font-bold mb-1" for="attachment_name_tw_<%= attachment.id %>">
                                      附件名稱 (中文)
                                    </label>
                                    <input
                                      class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                      id="attachment_name_tw_<%= attachment.id %>" 
                                      name="attachment_name_tw_<%= attachment.id %>" 
                                      type="text" 
                                      value="<%= attachment.attachment_name_tw || '' %>">
                                  </div>
                                  <div>
                                    <label class="block text-gray-700 text-sm font-bold mb-1" for="attachment_name_en_<%= attachment.id %>">
                                      附件名稱 (英文)
                                    </label>
                                    <input
                                      class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                      id="attachment_name_en_<%= attachment.id %>" 
                                      name="attachment_name_en_<%= attachment.id %>" 
                                      type="text" 
                                      value="<%= attachment.attachment_name_en || '' %>">
                                  </div>
                                </div>
                                <% }); %>
                            </div>
                          </div>
                        </div>
                        <% } else { %>
                          <div class="mb-4">
                            <p
                              class="text-gray-500 <%= item.type === 'attachment_only' ? 'text-red-500 font-bold' : '' %>">
                              <%= item.type==='attachment_only' ? '未找到附件。請至少添加一個附件。' : '此項目未找到附件。' %>
                            </p>
                          </div>
                          <% } %>

                            <!-- New Attachments -->
                            <div class="mb-4">
                              <label class="block text-gray-700 text-sm font-bold mb-2" for="attachments">
                                添加新附件
                                <% if (item.type==='attachment_only' ) { %>
                                  <span class="text-red-500">*</span>
                                  <% } %>
                              </label>
                              <input
                                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                id="attachments" name="attachments" type="file" multiple <% if
                                (item.type==='attachment_only' && (!item.attachments || item.attachments.length===0)) {
                                %>
                              required
                              <% } %>
                                >
                                <p class="text-gray-600 text-xs italic mt-1">
                                  每個檔案最大尺寸：<%= process.env.MAX_ATTACHMENT_SIZE ? (process.env.MAX_ATTACHMENT_SIZE /
                                    (1024 * 1024)).toFixed(0) : 50 %>MB。支援所有檔案格式。
                                </p>
                            </div>
                            
                            <!-- Dynamic attachment name fields for new uploads -->
                            <div id="new-attachment-name-fields" class="mb-4">
                              <!-- Fields will be dynamically added here when files are selected -->
                            </div>
                    </div>

                    <!-- Additional Settings -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                      <div>
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="order">
                          顯示順序
                        </label>
                        <input
                          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                          id="order" name="order" type="number" value="<%= item.order %>">
                      </div>
                      <div>
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
                          狀態 <span class="text-red-500">*</span>
                        </label>
                        <select
                          class="shadow border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                          id="status" name="status" required>
                          <option value="draft" <%=item.status==='draft' ? 'selected' : '' %>>草稿</option>
                          <option value="published" <%=item.status==='published' ? 'selected' : '' %>>已發布</option>
                        </select>
                      </div>
                      <div>
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="publishedDate">
                          發布日期 <span class="text-red-500">*</span>
                        </label>
                        <input
                          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                          id="publishedDate" name="publishedDate" type="date"
                          value="<%= new Date(item.publishedDate).toISOString().split('T')[0] %>" required>
                      </div>
                    </div>

                    <div class="flex items-center justify-end">
                      <button type="reset"
                        class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2">
                        重置
                      </button>
                      <button type="submit"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        更新平台項目
                      </button>
                    </div>
        </form>
      </div>
    </div>
  </div>

  <%- contentFor('scripts') %>
    <script>
      function toggleContentFields() {
        const type = document.getElementById('type').value;
        console.log('Selected type:', type);

        const contentEnField = document.getElementById('content_en');
        const contentTwField = document.getElementById('content_tw');
        const editorEnContainer = document.getElementById('editor_en');
        const editorTwContainer = document.getElementById('editor_tw');
        const categoryField = document.getElementById('categoryId');
        const contentRequiredMarks = document.querySelectorAll('.content-required');
        const categoryRequiredMarks = document.querySelectorAll('.category-required');

        // Image, URL, and attachments sections
        const currentImageSection = document.getElementById('current-image-section');
        const currentImageEnSection = document.getElementById('current-image-en-section');
        const imageUploadSection = document.getElementById('image-upload-section');
        const imageEnUploadSection = document.getElementById('image-en-upload-section');
        const urlSection = document.getElementById('url-section');
        const attachmentsSection = document.getElementById('attachments-section');
        const contentFieldsSection = document.getElementById('content_en_field').parentNode.parentNode;
        const addNewAttachmentsSection = document.getElementById('add-new-attachments-section');

        console.log('Sections found:', {
          currentImageSection: !!currentImageSection,
          currentImageEnSection: !!currentImageEnSection,
          imageUploadSection: !!imageUploadSection,
          imageEnUploadSection: !!imageEnUploadSection,
          urlSection: !!urlSection,
          attachmentsSection: !!attachmentsSection,
          contentFieldsSection: !!contentFieldsSection,
          addNewAttachmentsSection: !!addNewAttachmentsSection
        });

        // Reset all sections to visible first
        if (currentImageSection) currentImageSection.style.display = 'block';
        if (currentImageEnSection) currentImageEnSection.style.display = 'block';
        if (imageUploadSection) imageUploadSection.style.display = 'block';
        if (imageEnUploadSection) imageEnUploadSection.style.display = 'block';
        if (urlSection) urlSection.style.display = 'block';
        if (attachmentsSection) attachmentsSection.style.display = 'block';
        contentFieldsSection.style.display = 'grid';
        if (addNewAttachmentsSection) addNewAttachmentsSection.style.display = 'block';

        // Handle specific types
        if (type === 'image') {
          console.log('Handling image with caption type');
          // This is "image with caption" type
          // Make content fields optional
          contentEnField.required = false;
          contentTwField.required = false;
          categoryField.required = true;

          // Update content labels to show they're optional
          document.querySelector('label[for="editor_en"]').innerHTML = '內容 (英文)';
          document.querySelector('label[for="editor_tw"]').innerHTML = '內容 (中文)';

          // Hide required marks for content
          contentRequiredMarks.forEach(mark => mark.style.display = 'none');
          categoryRequiredMarks.forEach(mark => mark.style.display = 'inline');

          // Hide attachments section
          if (addNewAttachmentsSection) addNewAttachmentsSection.style.display = 'none';
          
          // Hide content fields
          contentFieldsSection.style.display = 'none';
        }
        else if (type === 'attachment_only') {
          console.log('Handling attachment only type');
          // Hide content fields, image, and URL
          contentFieldsSection.style.display = 'none';
          if (currentImageSection) currentImageSection.style.display = 'none';
          if (imageUploadSection) imageUploadSection.style.display = 'none';
          if (urlSection) urlSection.style.display = 'none';

          // Make content fields not required
          contentEnField.required = false;
          contentTwField.required = false;
          categoryField.required = false;

          // Hide required marks
          contentRequiredMarks.forEach(mark => mark.style.display = 'none');
          categoryRequiredMarks.forEach(mark => mark.style.display = 'none');
        }
        else if (type === 'plain_text') {
          console.log('Handling plain text type');
          // Make content fields required
          contentEnField.required = true;
          contentTwField.required = true;
          categoryField.required = true;

          // Show required marks
          contentRequiredMarks.forEach(mark => mark.style.display = 'inline');
          categoryRequiredMarks.forEach(mark => mark.style.display = 'inline');

          // Update content labels to show they're required
          document.querySelector('label[for="editor_en"]').innerHTML =
            '內容 (英文) <span class="text-red-500 content-required">*</span>';
          document.querySelector('label[for="editor_tw"]').innerHTML =
            '內容 (中文) <span class="text-red-500 content-required">*</span>';

          // Hide the entire attachment container - multiple approaches for better compatibility
          // 1. Find by heading
          const headings = document.querySelectorAll('h3');
          for (const heading of headings) {
            if (heading.textContent.includes('附件')) {
              const container = heading.closest('.mb-4');
              if (container) {
                container.style.display = 'none';
                console.log('Hiding attachment container with heading:', heading.textContent);
              }
            }
          }

          // 2. Direct selector for the container div
          const attachmentDiv = document.querySelector('div.mb-4[class*="border-blue-300"]');
          if (attachmentDiv) {
            attachmentDiv.style.display = 'none';
            console.log('Hiding attachment div with border-blue-300 class');
          }

          // 3. Hide by ID
          if (attachmentsSection) {
            attachmentsSection.style.display = 'none';
            console.log('Hiding attachments section by ID');
          }

          // 4. Hide the "Add New Attachments" section
          if (addNewAttachmentsSection) {
            addNewAttachmentsSection.style.display = 'none';
            console.log('Hiding add new attachments section');
          }

          // 5. Hide current attachments section if it exists
          const currentAttachmentsSection = document.querySelector('.mb-4 .bg-gray-100');
          if (currentAttachmentsSection) {
            currentAttachmentsSection.closest('.mb-4').style.display = 'none';
            console.log('Hiding current attachments section');
          }

          // 6. Direct approach - hide all attachment-related elements
          document.querySelectorAll('input[name="attachments"], label[for="attachments"]').forEach(el => {
            const container = el.closest('.mb-4');
            if (container) {
              container.style.display = 'none';
              console.log('Hiding element by direct selector:', el);
            }
          });
        }

        console.log('Toggle complete');
      }

      // Set initial state
      document.addEventListener('DOMContentLoaded', function () {
        console.log('DOM loaded, initializing form controls');

        // Add IDs to sections for easier toggling
        const imageUploadRow = document.querySelector('input[name="image"]').closest('.grid');
        imageUploadRow.id = 'image-upload-section';
        console.log('Image upload section identified:', imageUploadRow);
        
        // Add ID to the English image upload section
        const imageEnUploadRow = document.querySelector('input[name="imageEn"]').closest('.grid');
        imageEnUploadRow.id = 'image-en-upload-section';
        console.log('English image upload section identified:', imageEnUploadRow);

        // Attachments input handler for dynamic name fields
        const attachmentsInput = document.getElementById('attachments');
        const nameFieldsContainer = document.getElementById('new-attachment-name-fields');
        
        attachmentsInput.addEventListener('change', function() {
          // Clear previous fields
          nameFieldsContainer.innerHTML = '';
          
          // Create fields for each selected file
          for (let i = 0; i < this.files.length; i++) {
            const file = this.files[i];
            const fileIndex = i;
            const fileId = `file-${Date.now()}-${fileIndex}`;
            // Properly handle Chinese filename encoding
            const fileName = decodeURIComponent(escape(file.name));
            
            // Create container div
            const fileFieldset = document.createElement('fieldset');
            fileFieldset.className = 'border border-gray-300 rounded p-3 mb-3';
            
            // Add legend with filename - use textContent to properly handle Chinese characters
            const legend = document.createElement('legend');
            legend.className = 'text-sm font-bold px-2';
            legend.textContent = fileName;
            fileFieldset.appendChild(legend);
            
            // Create grid layout
            const grid = document.createElement('div');
            grid.className = 'grid grid-cols-1 md:grid-cols-2 gap-4';
            
            // English name field
            const engDiv = document.createElement('div');
            const engLabel = document.createElement('label');
            engLabel.className = 'block text-gray-700 text-sm font-bold mb-1';
            engLabel.setAttribute('for', `new_attachment_name_en_${fileId}`);
            engLabel.textContent = '附件名稱 (英文)';
            
            const engInput = document.createElement('input');
            engInput.className = 'shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline';
            engInput.id = `new_attachment_name_en_${fileId}`;
            engInput.name = `new_attachment_name_en_${fileIndex}`;
            engInput.type = 'text';
            
            engDiv.appendChild(engLabel);
            engDiv.appendChild(engInput);
            
            // Chinese name field
            const twDiv = document.createElement('div');
            const twLabel = document.createElement('label');
            twLabel.className = 'block text-gray-700 text-sm font-bold mb-1';
            twLabel.setAttribute('for', `new_attachment_name_tw_${fileId}`);
            twLabel.textContent = '附件名稱 (中文)';
            
            const twInput = document.createElement('input');
            twInput.className = 'shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline';
            twInput.id = `new_attachment_name_tw_${fileId}`;
            twInput.name = `new_attachment_name_tw_${fileIndex}`;
            twInput.type = 'text';
            
            twDiv.appendChild(twLabel);
            twDiv.appendChild(twInput);
            
            // Add fields to grid
            grid.appendChild(engDiv);
            grid.appendChild(twDiv);
            
            // Add grid to fieldset
            fileFieldset.appendChild(grid);
            
            // Add fieldset to container
            nameFieldsContainer.appendChild(fileFieldset);
          }
        });

        const urlField = document.querySelector('input[name="url"]');
        if (urlField) {
          urlField.closest('div').id = 'url-section';
          console.log('URL section identified:', urlField.closest('div'));
        }

        // Find attachments section - this is the entire attachments container
        const attachmentsHeadings = document.querySelectorAll('h3');
        let attachmentsSection = null;

        for (const heading of attachmentsHeadings) {
          if (heading.textContent.includes('附件')) {
            attachmentsSection = heading.closest('.mb-4');
            console.log('Attachments section found via heading:', heading.textContent);
            break;
          }
        }

        if (!attachmentsSection) {
          // Find the container div that has the "附件" class
          const attachmentContainer = document.querySelector('div.mb-4.border-blue-300');
          if (attachmentContainer) {
            attachmentsSection = attachmentContainer;
            console.log('Attachments section found via container class');
          } else {
            // Fallback to finding via input
            const attachmentsInput = document.querySelector('input[name="attachments"]');
            if (attachmentsInput) {
              // Go up multiple levels to find the entire attachments section
              attachmentsSection = attachmentsInput.closest('.mb-4').parentNode;
              console.log('Attachments section found via input');
            }
          }
        }

        if (attachmentsSection) {
          attachmentsSection.id = 'attachments-section';
          console.log('Attachments section identified:', attachmentsSection);
        } else {
          console.warn('Could not find attachments section');
        }

        // Specifically identify the "Add New Attachments" section
        const addNewAttachmentsLabel = document.querySelector('label[for="attachments"]');
        if (addNewAttachmentsLabel) {
          const addNewAttachmentsSection = addNewAttachmentsLabel.closest('.mb-4');
          if (addNewAttachmentsSection) {
            addNewAttachmentsSection.id = 'add-new-attachments-section';
            console.log('Add New Attachments section identified:', addNewAttachmentsSection);
          }
        }

        // Set up change event listener for type select
        document.getElementById('type').addEventListener('change', function () {
          console.log('Type changed to:', this.value);
          toggleContentFields();
        });

        // Add a small delay to ensure all elements are properly initialized
        setTimeout(toggleContentFields, 100);
      });
    </script>

    <!-- Quill.js Editor Setup -->
    <script>
      (function () {
        
            // Get the initial content from hidden inputs
            const initialContentEn = document.getElementById('content_en').value;
            const initialContentTw = document.getElementById('content_tw').value;

            // Initialize English editor with simple configuration
            var quill_en = new Quill('#editor_en', {
              theme: 'snow',
              modules: {
                toolbar: [
                  [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                  ['bold', 'italic', 'underline', 'strike'],
                  [{ 'color': [] }, { 'background': [] }],
                  [{ 'align': [] }],
                  [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                  ['link', 'image', 'video'],
                  ['clean'],
                  ['code-block']
                ]
              },
              placeholder: 'Compose an epic...'
            });

            // Initialize Traditional Chinese editor with simple configuration
            var quill_tw = new Quill('#editor_tw', {
              theme: 'snow',
              modules: {
                toolbar: [
                  [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                  ['bold', 'italic', 'underline', 'strike'],
                  [{ 'color': [] }, { 'background': [] }],
                  [{ 'align': [] }],
                  [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                  ['link', 'image', 'video'],
                  ['clean'],
                  ['code-block']
                ]
              },
              placeholder: '撰寫內容...'
            });

            // Set initial content
            if (initialContentEn) {
              quill_en.root.innerHTML = initialContentEn;
            }
            if (initialContentTw) {
              quill_tw.root.innerHTML = initialContentTw;
            }

            // Function to truncate content if needed
            function truncateContent(content, maxLength) {
              // Use 100MB as the maximum length for LongText fields
              const MAX_LONGTEXT_LENGTH = 100 * 1024 * 1024; // 100MB

              // If maxLength is not provided, use the maximum LongText length
              const limit = maxLength || MAX_LONGTEXT_LENGTH;

              if (content && content.length > limit) {
                console.warn(`Content truncated from ${content.length} to ${limit} characters`);
                return content.substring(0, limit);
              }
              return content;
            }

            // Form validation before submit
            $('form').on('submit', function (e) {
              // Update hidden inputs with Quill content
              const type = $('#type').val();

              if (type === 'plain_text') {
                // For plain text, store the HTML content
                const htmlEn = quill_en.root.innerHTML;
                const htmlTw = quill_tw.root.innerHTML;

                // Using LongText field now, so we can store much more content
                $('#content_en').val(truncateContent(htmlEn));
                $('#content_tw').val(truncateContent(htmlTw));
              }

              return true;
            });

            // Add image handler for English editor
            const imageHandlerEn = () => {
              const input = document.createElement('input');
              input.setAttribute('type', 'file');
              input.setAttribute('accept', 'image/*');
              input.click();

              input.onchange = () => {
                const file = input.files[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = () => {
                    const base64 = reader.result;
                    const range = quill_en.getSelection(true);
                    const altText = prompt("Enter alt text for the image (English):");
                    if (altText) {
                      const img = document.createElement('img');
                      img.src = base64;
                      img.alt = altText;
                      quill_en.clipboard.dangerouslyPasteHTML(range.index, img.outerHTML);
                    } else {
                      quill_en.insertEmbed(range.index, 'image', base64);
                    }
                  };
                  reader.readAsDataURL(file);
                }
              };
            };

            // Attach image handler to English editor toolbar
            quill_en.getModule('toolbar').addHandler('image', imageHandlerEn);

            // Add image handler for Traditional Chinese editor
            const imageHandlerTw = () => {
              const input = document.createElement('input');
              input.setAttribute('type', 'file');
              input.setAttribute('accept', 'image/*');
              input.click();

              input.onchange = () => {
                const file = input.files[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = () => {
                    const base64 = reader.result;
                    const range = quill_tw.getSelection(true);
                    const altText = prompt("輸入圖片替代文字 (中文):");
                    if (altText) {
                      const img = document.createElement('img');
                      img.src = base64;
                      img.alt = altText;
                      quill_tw.clipboard.dangerouslyPasteHTML(range.index, img.outerHTML);
                    } else {
                      quill_tw.insertEmbed(range.index, 'image', base64);
                    }
                  };
                  reader.readAsDataURL(file);
                }
              };
            };

            // Attach image handler to Traditional Chinese editor toolbar
            quill_tw.getModule('toolbar').addHandler('image', imageHandlerTw);
          
      })();
    </script>

    <style>
      /* Custom styles for Quill editors */
      #editor_en,
      #editor_tw {
        height: 300px;
        margin-bottom: 30px;
      }

      /* Fix toolbar positioning */
      .ql-toolbar.ql-snow {
        border: 1px solid #ccc;
        box-sizing: border-box;
        font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
        padding: 8px;
      }

      .ql-container.ql-snow {
        border: 1px solid #ccc;
        border-top: 0px;
      }

      /* Ensure editor has proper height */
      .ql-editor {
        min-height: 200px;
      }
    </style>