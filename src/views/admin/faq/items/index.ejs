<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">常見問題項目</h1>
      <div>
        <a href="/admin/faq/categories"
          class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-bold py-2 px-4 rounded mr-2">
          <i class="fas fa-cog mr-2"></i> 管理分類
        </a>
        <a href="/admin/faq/items/create"
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          <i class="fas fa-plus mr-2"></i> 新增項目
        </a>
      </div>
    </div>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <!-- Search and Filter -->
      <form action="/admin/faq/items" method="GET" class="flex flex-wrap items-end gap-4 mb-6">
        <div class="flex-1 min-w-[200px]">
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">搜尋</label>
          <input type="text" id="search" name="search" value="<%= search || '' %>"
              placeholder="依標題搜尋..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
        </div>
        <div class="w-full sm:w-auto">
          <label for="category"
              class="block text-sm font-medium text-gray-700 mb-1">分類</label>
          <select id="category" name="category"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option value="">所有分類</option>
              <% categories.forEach(function(category) { %>
                  <option value="<%= category.id %>" <%=selectedCategory==category.id ? 'selected' : '' %>>
                      <%= category.name_tw %> / <%= category.name_en %>
                  </option>
                  <% }); %>
          </select>
        </div>
        <div class="w-full sm:w-auto">
          <label for="status" class="block text-sm font-medium text-gray-700 mb-1">狀態</label>
          <select id="status" name="status"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
              <option value="">所有狀態</option>
              <option value="draft" <%=status==='draft' ? 'selected' : '' %>>草稿</option>
              <option value="published" <%=status==='published' ? 'selected' : '' %>>已發佈</option>
          </select>
        </div>
        <div class="flex gap-2">
          <button type="submit"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              篩選
          </button>
          <a href="/admin/faq/items"
              class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
              重置
          </a>
        </div>
      </form>

      <!-- Items Table -->
      <% if(items.length > 0) { %>
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white" id="dataTable">
          <thead>
            <tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
              <th class="py-3 px-6 text-left whitespace-nowrap">標題</th>
              <th class="py-3 px-6 text-left whitespace-nowrap">分類</th>
              <th class="py-3 px-6 text-left whitespace-nowrap">狀態</th>
              <th class="py-3 px-6 text-left whitespace-nowrap">排序</th>
              <th class="py-3 px-6 text-left whitespace-nowrap">作者</th>
              <th class="py-3 px-6 text-center whitespace-nowrap">操作</th>
            </tr>
          </thead>
          <tbody class="text-gray-600 text-sm">
            <% items.forEach(function(item) { %>
              <tr class="border-b border-gray-200 hover:bg-gray-50">
                <td class="py-3 px-6 text-left whitespace-nowrap">
                  <div class="flex items-center">
                    <div>
                      <p class="font-semibold"><%= item.title_tw %></p>
                      <p class="text-xs text-gray-500"><%= item.title_en %></p>
                    </div>
                  </div>
                </td>
                <td class="py-3 px-6 text-left whitespace-nowrap">
                  <p class="font-semibold"><%= item.category.name_tw %></p>
                  <p class="text-xs text-gray-500"><%= item.category.name_en %></p>
                </td>
                <td class="py-3 px-6 text-left whitespace-nowrap">
                  <% if (item.status==='published' ) { %>
                    <span class="bg-green-200 text-green-800 py-1 px-3 rounded-full text-xs">
                      已發佈
                    </span>
                  <% } else if (item.status==='draft' ) { %>
                    <span class="bg-yellow-200 text-yellow-800 py-1 px-3 rounded-full text-xs">
                      草稿
                    </span>
                  <% } else { %>
                    <span class="bg-gray-200 text-gray-800 py-1 px-3 rounded-full text-xs">
                      <%= item.status %>
                    </span>
                  <% } %>
                </td>
                <td class="py-3 px-6 text-left whitespace-nowrap">
                  <%= item.order %>
                </td>
                <td class="py-3 px-6 text-left whitespace-nowrap">
                  <%= item.author.username %>
                </td>
                <td class="py-3 px-6 text-center whitespace-nowrap">
                  <div class="flex items-center justify-center">
                    <a href="/admin/faq/items/edit/<%= item.id %>" class="text-blue-600 hover:text-blue-900 mx-1">
                      <i class="fas fa-edit"></i>
                    </a>
                    <button type="button" class="text-red-600 hover:text-red-900 mx-1"
                      onclick="openDeleteModal('<%= item.id %>', '<%= item.title_en %>')">
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>

                  <!-- Delete Modal -->
                  <div id="deleteModal<%= item.id %>"
                    class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
                    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                      <div class="mt-3 text-center">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">確認刪除</h3>
                        <div class="mt-2 px-7 py-3">
                          <p class="text-sm text-gray-500">
                            您確定要刪除「<%= item.title_en %>」嗎？
                          </p>
                        </div>
                        <div class="flex justify-end mt-4">
                          <button type="button"
                            class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                            onclick="closeDeleteModal('<%= item.id %>')">
                            取消
                          </button>
                          <form action="/admin/faq/items/<%= item.id %>/delete" method="POST" class="inline">
                            <button type="submit"
                              class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                              刪除
                            </button>
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                </td>
              </tr>
            <% }); %>
          </tbody>
        </table>
      </div>
      <% } else { %>
      <div class="text-center py-5">
        <p class="text-gray-500">尚未找到任何常見問題項目。<a href="/admin/faq/items/create" class="text-blue-600 hover:underline">立即建立一個</a>。</p>
      </div>
      <% } %>

      <!-- Pagination -->
      <% if (typeof totalPages !== 'undefined' && totalPages > 1) { %>
      <div class="flex justify-between items-center mt-6">
          <span class="text-sm text-gray-700">
              顯示 <%= (currentPage - 1) * perPage + 1 %>-<%= Math.min(currentPage * perPage, totalItems) %> 共 <%= totalItems %> 項
          </span>
          <nav aria-label="Table navigation">
              <ul class="inline-flex items-center">
                  <% if (currentPage > 1) { %>
                      <li>
                          <a href="?page=<%= currentPage - 1 %><%= search ? `&search=${search}` : '' %><%= selectedCategory ? `&category=${selectedCategory}` : '' %><%= status ? `&status=${status}` : '' %>"
                              class="px-3 py-1 rounded-md focus:outline-none focus:shadow-outline-blue"
                              aria-label="上一頁">
                              <svg aria-hidden="true" class="w-4 h-4 fill-current"
                                  viewBox="0 0 20 20">
                                  <path
                                      d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                                      clip-rule="evenodd" fill-rule="evenodd"></path>
                              </svg>
                          </a>
                      </li>
                  <% } %>

                  <% for(let i=1; i <=totalPages; i++) { %>
                      <li>
                          <a href="?page=<%= i %><%= search ? `&search=${search}` : '' %><%= selectedCategory ? `&category=${selectedCategory}` : '' %><%= status ? `&status=${status}` : '' %>"
                              class="px-3 py-1 mx-1 rounded-md <%= currentPage === i ? 'bg-blue-500 text-white' : 'text-blue-600 hover:bg-blue-100' %>">
                              <%= i %>
                          </a>
                      </li>
                  <% } %>

                  <% if (currentPage < totalPages) { %>
                      <li>
                          <a href="?page=<%= currentPage + 1 %><%= search ? `&search=${search}` : '' %><%= selectedCategory ? `&category=${selectedCategory}` : '' %><%= status ? `&status=${status}` : '' %>"
                              class="px-3 py-1 rounded-md focus:outline-none focus:shadow-outline-blue"
                              aria-label="下一頁">
                              <svg aria-hidden="true" class="w-4 h-4 fill-current"
                                  viewBox="0 0 20 20">
                                  <path
                                      d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                      clip-rule="evenodd" fill-rule="evenodd"></path>
                              </svg>
                          </a>
                      </li>
                  <% } %>
              </ul>
          </nav>
      </div>
      <% } %>
    </div>
  </div>

<%- contentFor('scripts') %>
<script>
  function openDeleteModal(id, title) {
    document.getElementById('deleteModal' + id).classList.remove('hidden');
  }

  function closeDeleteModal(id) {
    document.getElementById('deleteModal' + id).classList.add('hidden');
  }
</script>