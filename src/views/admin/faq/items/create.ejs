<%- contentFor('body') %>

    <div class="container px-6 mx-auto grid">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">編輯常見問題項目</h1>
            <a href="/admin/faq/items" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
              <i class="fas fa-arrow-left mr-2"></i> 返回列表
            </a>
          </div>

                <% if(typeof error_msg !=='undefined' && error_msg.length> 0) { %>
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                        <%= error_msg %>
                    </div>
                    <% } %>

                        <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
                            <!-- Language Switcher -->
                            <div class="mb-4">
                                <div class="flex border-b border-gray-200">
                                    <button type="button"
                                        class="py-2 px-4 text-center border-b-2 border-blue-500 text-blue-500 font-medium text-sm leading-5 focus:outline-none language-tab active"
                                        data-language="tw">
                                        中文
                                    </button>
                                    <button type="button"
                                        class="py-2 px-4 text-center border-b-2 border-transparent text-gray-500 font-medium text-sm leading-5 hover:text-gray-700 hover:border-gray-300 focus:outline-none language-tab"
                                        data-language="en">
                                        英文
                                    </button>
                                </div>
                            </div>

                            <form action="/admin/faq/items" method="POST" 
                                data-max-faq-image-size="<%= process.env.MAX_FAQ_IMAGE_SIZE || (5 * 1024 * 1024) %>"
                                data-allowed-faq-image-types="<%= process.env.ALLOWED_FAQ_IMAGE_TYPES || 'jpg,jpeg,png,gif,webp' %>">
                                
                                <!-- Title - Traditional Chinese -->
                                <div class="mb-4 language-content tw">
                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="title_tw">
                                        問題/標題 (中文) <span class="text-red-500">*</span>
                                    </label>
                                    <input
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                        type="text" id="title_tw" name="title_tw" placeholder="請輸入常見問題的問題或標題（中文）"
                                        required />
                                </div>
                                
                                <!-- Title - English -->
                                <div class="mb-4 language-content en hidden">
                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="title_en">
                                        問題/標題 (英文) <span class="text-red-500">*</span>
                                    </label>
                                    <input
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                        type="text" id="title_en" name="title_en"
                                        placeholder="Enter the FAQ question or title in English" required />
                                </div>

                                <!-- Category -->
                                <div class="mb-4">
                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="categoryId">
                                        分類 <span class="text-red-500">*</span>
                                    </label>
                                    <select
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                        id="categoryId" name="categoryId" required>
                                        <option value="">選擇分類</option>
                                        <% categories.forEach(function(category) { %>
                                            <option value="<%= category.id %>">
                                                <%= category.name_tw %> / <%= category.name_en %>
                                            </option>
                                            <% }); %>
                                    </select>
                                </div>

                                <!-- Content - English -->
                                <div class="mb-4 language-content en hidden">
                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="content_en">
                                        答案/內容 (英文) <span class="text-red-500">*</span>
                                    </label>
                                    <div id="editor-container-en" class="h-64 mt-1"></div>
                                    <input type="hidden" name="content_en" id="content_en">
                                </div>

                                <!-- Content - Traditional Chinese -->
                                <div class="mb-4 language-content tw">
                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="content_tw">
                                        答案/內容 (中文) <span class="text-red-500">*</span>
                                    </label>
                                    <div id="editor-container-tw" class="h-64 mt-1"></div>
                                    <input type="hidden" name="content_tw" id="content_tw">
                                </div>

                                <!-- Order -->
                                <div class="mb-4">
                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="order">
                                        顯示順序
                                    </label>
                                    <input
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                        type="number" id="order" name="order" placeholder="輸入顯示順序（較小的數字會先顯示）"
                                        value="0" />
                                </div>

                                <!-- Status -->
                                <div class="mb-4">
                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
                                        狀態
                                    </label>
                                    <select
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                        id="status" name="status">
                                        <option value="draft">草稿</option>
                                        <option value="published">已發布</option>
                                    </select>
                                </div>

                                <!-- Submit Button -->
                                <div class="flex justify-end mt-6">
                                    <a href="/admin/faq/items"
                                        class="px-4 py-2 mr-2 text-sm font-medium leading-5 text-gray-700 transition-colors duration-150 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:outline-none focus:shadow-outline-gray">
                                        取消
                                    </a>
                                    <button type="submit"
                                        class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-blue-600 border border-transparent rounded-lg active:bg-blue-600 hover:bg-blue-700 focus:outline-none focus:shadow-outline-blue">
                                        建立常見問題項目
                                    </button>
                                </div>
                            </form>
                        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize Quill editors for both languages
            const toolbarOptions = [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                [{ 'indent': '-1' }, { 'indent': '+1' }],
                ['link', 'image'],
                ['clean']
            ];

            // Function to handle image insertion with alt text for English editor
            function imageHandlerEn() {
                const input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.click();

                input.onchange = () => {
                    const file = input.files[0];
                    
                    // Check file size (MAX_FAQ_IMAGE_SIZE from env or default to 5MB)
                    const form = document.querySelector('form');
                    const maxSize = parseInt(form.getAttribute('data-max-faq-image-size'));
                    if (file.size > maxSize) {
                        alert(`File too large! Maximum size is ${maxSize / (1024 * 1024)}MB`);
                        return;
                    }
                    
                    // Check file type
                    const allowedTypes = form.getAttribute('data-allowed-faq-image-types').split(',');
                    const fileExtension = file.name.split('.').pop().toLowerCase();
                    if (!allowedTypes.includes(fileExtension)) {
                        alert(`Unsupported file type! Allowed types: ${allowedTypes.join(', ')}`);
                        return;
                    }
                    
                    // Ask user for alt text
                    const altText = prompt('Please enter alt text for this image (for accessibility):');
                    
                    // Simple validation - if canceled or empty, use file name
                    const finalAltText = (altText === null || altText.trim() === '') 
                        ? file.name : altText.trim();
                    
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = () => {
                            const img = document.createElement('img');
                            img.src = reader.result;
                            
                            // Insert the image with alt text attribute
                            const range = quillEn.getSelection(true);
                            quillEn.insertEmbed(range.index, 'image', reader.result);
                            
                            // Get the image that was just inserted and add alt text
                            setTimeout(() => {
                                const images = quillEn.root.querySelectorAll('img');
                                const insertedImage = images[images.length - 1];
                                insertedImage.alt = finalAltText;
                            }, 10);
                        };
                        reader.readAsDataURL(file);
                    }
                };
            }

            // Function to handle image insertion with alt text for Traditional Chinese editor
            function imageHandlerTw() {
                const input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.click();

                input.onchange = () => {
                    const file = input.files[0];
                    
                    // Check file size (MAX_FAQ_IMAGE_SIZE from env or default to 5MB)
                    const form = document.querySelector('form');
                    const maxSize = parseInt(form.getAttribute('data-max-faq-image-size'));
                    if (file.size > maxSize) {
                        alert(`檔案太大！最大檔案大小為 ${maxSize / (1024 * 1024)}MB`);
                        return;
                    }
                    
                    // Check file type
                    const allowedTypes = form.getAttribute('data-allowed-faq-image-types').split(',');
                    const fileExtension = file.name.split('.').pop().toLowerCase();
                    if (!allowedTypes.includes(fileExtension)) {
                        alert(`不支援的檔案類型！允許的類型：${allowedTypes.join(', ')}`);
                        return;
                    }
                    
                    // Ask user for alt text
                    const altText = prompt('請輸入圖片替代文字（為了無障礙網頁）:');
                    
                    // Simple validation - if canceled or empty, use file name
                    const finalAltText = (altText === null || altText.trim() === '') 
                        ? file.name : altText.trim();
                    
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = () => {
                            const img = document.createElement('img');
                            img.src = reader.result;
                            
                            // Insert the image with alt text attribute
                            const range = quillTw.getSelection(true);
                            quillTw.insertEmbed(range.index, 'image', reader.result);
                            
                            // Get the image that was just inserted and add alt text
                            setTimeout(() => {
                                const images = quillTw.root.querySelectorAll('img');
                                const insertedImage = images[images.length - 1];
                                insertedImage.alt = finalAltText;
                            }, 10);
                        };
                        reader.readAsDataURL(file);
                    }
                };
            }

            // English Quill editor
            var quillEn = new Quill('#editor-container-en', {
                theme: 'snow',
                modules: {
                    toolbar: {
                        container: toolbarOptions,
                        handlers: {
                            'image': imageHandlerEn
                        }
                    }
                },
                placeholder: 'Enter the answer content in English...'
            });

            // Traditional Chinese Quill editor
            var quillTw = new Quill('#editor-container-tw', {
                theme: 'snow',
                modules: {
                    toolbar: {
                        container: toolbarOptions,
                        handlers: {
                            'image': imageHandlerTw
                        }
                    }
                },
                placeholder: '請輸入答案內容（中文）...'
            });

            // When form is submitted, update hidden inputs with Quill content
            document.querySelector('form').addEventListener('submit', function () {
                var contentEnInput = document.querySelector('#content_en');
                contentEnInput.value = JSON.stringify(quillEn.getContents());

                var contentTwInput = document.querySelector('#content_tw');
                contentTwInput.value = JSON.stringify(quillTw.getContents());
            });

            // Language switcher functionality
            const languageTabs = document.querySelectorAll('.language-tab');
            const languageContents = document.querySelectorAll('.language-content');

            languageTabs.forEach(tab => {
                tab.addEventListener('click', function () {
                    const language = this.getAttribute('data-language');

                    // Update active tab
                    languageTabs.forEach(t => {
                        t.classList.remove('border-blue-500', 'text-blue-500', 'active');
                        t.classList.add('border-transparent', 'text-gray-500');
                    });
                    this.classList.remove('border-transparent', 'text-gray-500');
                    this.classList.add('border-blue-500', 'text-blue-500', 'active');

                    // Show/hide content sections
                    languageContents.forEach(content => {
                        if (content.classList.contains(language)) {
                            content.classList.remove('hidden');
                        } else {
                            content.classList.add('hidden');
                        }
                    });
                });
            });
        });
    </script>