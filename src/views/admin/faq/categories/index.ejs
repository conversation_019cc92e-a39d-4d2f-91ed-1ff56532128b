<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">常見問題分類管理</h1>
            <div class="flex items-center">
                <a href="/admin/faq/items"
                    class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-bold py-2 px-4 rounded mr-2">
                    <i class="fas fa-arrow-left mr-2"></i> 返回常見問題
                </a>
                <a href="/admin/faq/categories/create"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-plus mr-2"></i> 新增分類
                </a>
            </div>
        </div>

        <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">


            <% if(categories.length > 0) { %>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white" id="dataTable">
                        <thead>
                            <tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                                <th class="py-3 px-6 text-left whitespace-nowrap">順序</th>
                                <th class="py-3 px-6 text-left whitespace-nowrap">名稱 (中)</th>
                                <th class="py-3 px-6 text-left whitespace-nowrap">名稱 (英)</th>
                                <th class="py-3 px-6 text-left whitespace-nowrap">網址代碼</th>
                                <th class="py-3 px-6 text-left whitespace-nowrap">項目數</th>
                                <th class="py-3 px-6 text-center whitespace-nowrap">操作</th>
                            </tr>
                        </thead>
                        <tbody class="text-gray-600 text-sm">
                            <% categories.forEach(function(category) { %>
                                <tr class="border-b border-gray-200 hover:bg-gray-50">
                                    <td class="py-3 px-6 text-left whitespace-nowrap">
                                        <%= category.order %>
                                    </td>
                                    <td class="py-3 px-6 text-left whitespace-nowrap">
                                        <%= category.name_tw %>
                                    </td>
                                    <td class="py-3 px-6 text-left whitespace-nowrap">
                                        <%= category.name_en %>
                                    </td>
                                    <td class="py-3 px-6 text-left whitespace-nowrap">
                                        <%= category.slug %>
                                    </td>
                                    <td class="py-3 px-6 text-left whitespace-nowrap">
                                        <%= category._count.faqItems %>
                                    </td>
                                    <td class="py-3 px-6 text-center whitespace-nowrap">
                                        <div class="flex item-center justify-center">
                                            <a href="/admin/faq/categories/edit/<%= category.id %>"
                                                class="text-blue-600 hover:text-blue-900 mx-1">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="text-red-600 hover:text-red-900 mx-1"
                                                onclick="document.getElementById('deleteModal<%= category.id %>').classList.remove('hidden')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Delete Modal -->
                                <div id="deleteModal<%= category.id %>"
                                    class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
                                    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                                        <div class="mt-3 text-center">
                                            <h3 class="text-lg leading-6 font-medium text-gray-900">確認刪除</h3>
                                            <div class="mt-2 px-7 py-3">
                                                <p class="text-sm text-gray-500">
                                                    您確定要刪除分類「<%= category.name_en %>」嗎？這將同時刪除該分類下的所有項目。
                                                </p>
                                            </div>
                                            <div class="flex justify-end mt-4">
                                                <button type="button"
                                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                                                    onclick="document.getElementById('deleteModal<%= category.id %>').classList.add('hidden')">
                                                    取消
                                                </button>
                                                <form action="/admin/faq/categories/<%= category.id %>/delete" method="POST" class="inline">
                                                    <button type="submit"
                                                        class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                                        刪除
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <% }); %>
                        </tbody>
                    </table>
                </div>
            <% } else { %>
                <div class="text-center py-8">
                    <p class="text-gray-500 mb-4">未找到任何常見問題分類。</p>
                    <a href="/admin/faq/categories/create"
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                        <i class="fas fa-plus mr-2"></i> 新增第一個分類
                    </a>
                </div>
            <% } %>
        </div>
    </div>

<%- contentFor('script') %>
<script>
    $(document).ready(function () {
        $('#dataTable').DataTable({
            "order": [[0, "asc"]]
        });
    });
</script>