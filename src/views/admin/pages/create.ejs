<%- contentFor('body') %>

    <div class="container px-6 mx-auto grid">
        <h2 class="my-6 text-2xl font-semibold text-gray-700">
            建立新頁面
        </h2>

        <!-- Flash messages -->
        <% if (success_msg && success_msg.length> 0) { %>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                <p>
                    <%= success_msg %>
                </p>
            </div>
            <% } %>

                <% if (error_msg && error_msg.length> 0) { %>
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                        <p>
                            <%= error_msg %>
                        </p>
                    </div>
                    <% } %>

                        <!-- Page Creation Form -->
                        <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
                            <form action="/admin/pages" method="POST" enctype="multipart/form-data" id="pageForm">

                                <!-- Language Tabs -->
                                <div class="mb-6 border-b border-gray-200">
                                    <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="languageTabs"
                                        role="tablist">
                                        <li class="mr-2" role="presentation">
                                            <button
                                                class="inline-block p-4 border-b-2 border-blue-600 rounded-t-lg active"
                                                id="chinese-tab" data-tabs-target="#chinese" type="button" role="tab"
                                                aria-controls="chinese" aria-selected="false">
                                                中文
                                            </button>
                                        </li>
                                        <li class="mr-2" role="presentation">
                                            <button
                                                class="inline-block p-4 border-b-2 border-transparent rounded-t-lg hover:border-gray-300"
                                                id="english-tab" data-tabs-target="#english" type="button" role="tab"
                                                aria-controls="english" aria-selected="true">
                                                英文
                                            </button>
                                        </li>
                                    </ul>
                                </div>

                                <!-- Traditional Chinese Content -->
                                <div id="chinese" class="" role="tabpanel" aria-labelledby="chinese-tab">
                                    <!-- Chinese Title -->
                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="title_tw">
                                            頁面標題 (中文) <span class="text-red-500">*</span>
                                        </label>
                                        <input
                                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                            type="text" id="title_tw" name="title_tw" placeholder="輸入頁面標題" required>
                                    </div>

                                    <!-- Chinese Content -->
                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="editor_tw">
                                            頁面內容 (中文) <span class="text-red-500">*</span>
                                        </label>
                                        <!-- Quill editor container -->
                                        <div id="editor_tw"
                                            class="border rounded w-full focus:outline-none focus:shadow-outline"
                                            style="min-height: 300px;"></div>

                                        <!-- Hidden input to store content -->
                                        <input type="hidden" name="content_tw" id="content_tw">
                                    </div>
                                </div>

                                <!-- English Content -->
                                <div id="english" class="hidden" role="tabpanel" aria-labelledby="english-tab">
                                    <!-- English Title -->
                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="title_en">
                                            頁面標題 (英文) <span class="text-red-500">*</span>
                                        </label>
                                        <input
                                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                            type="text" id="title_en" name="title_en" placeholder="輸入頁面標題" required>
                                    </div>

                                    <!-- English Content -->
                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="editor_en">
                                            頁面內容 (英文) <span class="text-red-500">*</span>
                                        </label>
                                        <!-- Quill editor container -->
                                        <div id="editor_en"
                                            class="border rounded w-full focus:outline-none focus:shadow-outline"
                                            style="min-height: 300px;"></div>

                                        <!-- Hidden input to store content -->
                                        <input type="hidden" name="content_en" id="content_en">
                                    </div>
                                </div>


                                <!-- Common Fields (Not Language Specific) -->
                                <!-- Attachments -->
                                <div class="mb-4 mt-6 pt-4 border-t border-gray-200">
                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="attachments">
                                        附件
                                    </label>
                                    <input
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                        type="file" id="attachments" name="attachments" multiple>
                                    <p class="mt-1 text-xs text-gray-500">
                                        允許的檔案類型：JPG、PNG、PDF、Word、Excel、MP4。每個檔案最大 50MB。
                                    </p>
                                </div>

                                <!-- Status -->
                                <div class="mb-4">
                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
                                        狀態 <span class="text-red-500">*</span>
                                    </label>
                                    <select
                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                        id="status" name="status" required>
                                        <option value="draft">草稿</option>
                                        <option value="published">已發布</option>
                                        <option value="archived">已封存</option>
                                        <option value="pending">待審核</option>
                                    </select>
                                    <p class="mt-1 text-xs text-gray-500">選擇頁面的發布狀態</p>
                                </div>

                                <!-- Submit Button -->
                                <div class="mt-6 flex justify-end">
                                    <button type="submit"
                                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        建立頁面
                                    </button>
                                </div>
                            </form>
                        </div>
    </div>


    <script>
        // Initialize Quill editors
        const quillOptions = {
            theme: 'snow',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'align': [] }],
                    [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                    ['link', 'image', 'video'],
                    ['clean'],
                    ['code-block', 'blockquote']
                ]
            },
            placeholder: '撰寫內容...'
        };

        var quill_en = new Quill('#editor_en', quillOptions);
        var quill_tw = new Quill('#editor_tw', { ...quillOptions, placeholder: '撰寫內容...' });

        // Handle language tabs
        const tabs = document.querySelectorAll('[data-tabs-target]');
        const tabContents = document.querySelectorAll('[role="tabpanel"]');

        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const target = document.querySelector(tab.dataset.tabsTarget);

                tabContents.forEach(tabContent => {
                    tabContent.classList.add('hidden');
                });

                tabs.forEach(tab => {
                    tab.classList.remove('active', 'border-blue-600');
                    tab.classList.add('border-transparent');
                    tab.setAttribute('aria-selected', false);
                });

                tab.classList.add('active', 'border-blue-600');
                tab.classList.remove('border-transparent');
                tab.setAttribute('aria-selected', true);

                target.classList.remove('hidden');
            });
        });

        // Function to truncate content if needed
        function truncateContent(content, maxLength) {
            // Use 100MB as the maximum length for LongText fields
            const MAX_LONGTEXT_LENGTH = 100 * 1024 * 1024; // 100MB

            // If maxLength is not provided, use the maximum LongText length
            const limit = maxLength || MAX_LONGTEXT_LENGTH;

            if (content && content.length > limit) {
                console.warn(`Content truncated from ${content.length} to ${limit} characters`);
                return content.substring(0, limit);
            }
            return content;
        }

        // Add a function to check content length before submission
        function checkContentLength() {
            const MAX_CONTENT_LENGTH = 100 * 1024 * 1024; // 100MB limit
            const contentEn = document.getElementById('content_en').value;
            const contentTw = document.getElementById('content_tw').value;

            let warningMessage = '';

            if (contentEn && contentEn.length > MAX_CONTENT_LENGTH) {
                warningMessage += `英文內容太大 (${(contentEn.length / (1024 * 1024)).toFixed(2)}MB)。將被截斷至 100MB。\n`;
            }

            if (contentTw && contentTw.length > MAX_CONTENT_LENGTH) {
                warningMessage += `中文內容太大 (${(contentTw.length / (1024 * 1024)).toFixed(2)}MB)。將被截斷至 100MB。\n`;
            }

            if (warningMessage) {
                return confirm(warningMessage + '是否要繼續？');
            }

            return true;
        }

        // Handle form submission
        document.getElementById('pageForm').addEventListener('submit', function (e) {
            e.preventDefault();

            // Add hidden field for editor mode
            const editorModeInput = document.createElement('input');
            editorModeInput.type = 'hidden';
            editorModeInput.name = 'editorMode';
            editorModeInput.value = 'editor';
            this.appendChild(editorModeInput);

            // Get Quill content
            document.getElementById('content_en').value = JSON.stringify(quill_en.getContents());
            document.getElementById('content_tw').value = JSON.stringify(quill_tw.getContents());

            // Check content length before submission
            if (checkContentLength()) {
                this.submit();
            }
        });

        // Add image handler for English editor
        const imageHandlerEn = () => {
            const input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.click();

            input.onchange = () => {
                const file = input.files[0];
                
                // Check file size (using environment variable or default to 5MB)
                const maxSize = <%= parseInt(process.env.MAX_IMAGE_SIZE) || 5 * 1024 * 1024 %>;
                if (file.size > maxSize) {
                    alert(`File too large! Maximum size is ${maxSize / (1024 * 1024)}MB`);
                    return;
                }
                
                // Ask user for alt text
                const altText = prompt('Please enter alt text for this image (for accessibility):');
                
                // Simple validation - if canceled or empty, use file name
                const finalAltText = (altText === null || altText.trim() === '') 
                    ? file.name : altText.trim();
                
                if (file) {
                    const reader = new FileReader();
                    reader.onload = () => {
                        const base64 = reader.result;
                        const range = quill_en.getSelection(true);
                        quill_en.insertEmbed(range.index, 'image', base64);
                        
                        // Get the image that was just inserted and add alt text
                        setTimeout(() => {
                            const images = quill_en.root.querySelectorAll('img');
                            const insertedImage = images[images.length - 1];
                            insertedImage.alt = finalAltText;
                        }, 10);
                    };
                    reader.readAsDataURL(file);
                }
            };
        };

        // Add image handler for Traditional Chinese editor
        const imageHandlerTw = () => {
            const input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.click();

            input.onchange = () => {
                const file = input.files[0];
                
                // Check file size (using environment variable or default to 5MB)
                const maxSize = <%= parseInt(process.env.MAX_IMAGE_SIZE) || 5 * 1024 * 1024 %>;
                if (file.size > maxSize) {
                    alert(`檔案太大！最大檔案大小為 ${maxSize / (1024 * 1024)}MB`);
                    return;
                }
                
                // Ask user for alt text
                const altText = prompt('請輸入圖片替代文字（為了無障礙網頁）:');
                
                // Simple validation - if canceled or empty, use file name
                const finalAltText = (altText === null || altText.trim() === '') 
                    ? file.name : altText.trim();
                
                if (file) {
                    const reader = new FileReader();
                    reader.onload = () => {
                        const base64 = reader.result;
                        const range = quill_tw.getSelection(true);
                        quill_tw.insertEmbed(range.index, 'image', base64);
                        
                        // Get the image that was just inserted and add alt text
                        setTimeout(() => {
                            const images = quill_tw.root.querySelectorAll('img');
                            const insertedImage = images[images.length - 1];
                            insertedImage.alt = finalAltText;
                        }, 10);
                    };
                    reader.readAsDataURL(file);
                }
            };
        };

        // Attach image handlers to editor toolbars
        quill_en.getModule('toolbar').addHandler('image', imageHandlerEn);
        quill_tw.getModule('toolbar').addHandler('image', imageHandlerTw);
    </script>

    <style>
        /* Custom styles for Quill editors */
        #editor_en,
        #editor_tw {
            height: 300px;
            margin-bottom: 30px;
        }

        /* Fix toolbar positioning */
        .ql-toolbar.ql-snow {
            border: 1px solid #ccc;
            box-sizing: border-box;
            font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
            padding: 8px;
        }

        .ql-container.ql-snow {
            border: 1px solid #ccc;
            border-top: 0px;
        }

        /* Ensure editor has proper height */
        .ql-editor {
            min-height: 200px;
        }
    </style>