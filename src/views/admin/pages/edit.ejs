<%- contentFor('body') %>

    <div class="container px-6 mx-auto grid">
        <h2 class="my-6 text-2xl font-semibold text-gray-700">
            編輯頁面: <%= page.title %>
        </h2>

        <!-- Flash messages -->
        <% if (success_msg && success_msg.length> 0) { %>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                <p>
                    <%= success_msg %>
                </p>
            </div>
            <% } %>

                <% if (error_msg && error_msg.length> 0) { %>
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                        <p>
                            <%= error_msg %>
                        </p>
                    </div>
                    <% } %>

                        <!-- Page Edit Form -->
                        <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
                            <form action="/admin/pages/<%= page.slug %>" method="POST" enctype="multipart/form-data"
                                id="pageForm">
                                <input type="hidden" name="_method" value="PUT">

                                <!-- Language Tabs -->
                                <div class="mb-6 border-b border-gray-200">
                                    <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" id="languageTabs"
                                        role="tablist">
                                        <li class="mr-2" role="presentation">
                                            <button
                                                class="inline-block p-4 border-b-2 rounded-t-lg border-blue-600 active"
                                                id="chinese-tab" data-tabs-target="#chinese" type="button" role="tab"
                                                aria-controls="chinese" aria-selected="false">
                                                中文
                                            </button>
                                        </li>
                                        <li class="mr-2" role="presentation">
                                            <button
                                                class="inline-block p-4 border-b-2 rounded-t-lg border-transparent hover:border-gray-300"
                                                id="english-tab" data-tabs-target="#english" type="button" role="tab"
                                                aria-controls="english" aria-selected="true">
                                                英文
                                            </button>
                                        </li>
                                    </ul>
                                </div>

                                <!-- Traditional Chinese Content -->
                                <div id="chinese" class="" role="tabpanel" aria-labelledby="chinese-tab">
                                    <!-- Chinese Title -->
                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="title_tw">
                                            頁面標題 (中文) <span class="text-red-500">*</span>
                                        </label>
                                        <input
                                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                            type="text" id="title_tw" name="title_tw" value="<%= page.title_tw %>"
                                            placeholder="輸入頁面標題" required>
                                    </div>

                                    <!-- Chinese Content -->
                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="editor_tw">
                                            頁面內容 (中文) <span class="text-red-500">*</span>
                                        </label>
                                        <!-- Quill editor container -->
                                        <div id="editor_tw"
                                            class="border rounded w-full focus:outline-none focus:shadow-outline"
                                            style="min-height: 300px;"></div>

                                        <!-- Hidden input to store content -->
                                        <input type="hidden" name="content_tw" id="content_tw"
                                            value="<%= page.content_tw %>">
                                    </div>
                                </div>

                                <!-- English Content -->
                                <div id="english" class="hidden" role="tabpanel" aria-labelledby="english-tab">
                                    <!-- English Title -->
                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="title_en">
                                            頁面標題 (英文) <span class="text-red-500">*</span>
                                        </label>
                                        <input
                                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                            type="text" id="title_en" name="title_en" value="<%= page.title_en %>"
                                            placeholder="輸入頁面標題" required>
                                    </div>

                                    <!-- English Content -->
                                    <div class="mb-4">
                                        <label class="block text-gray-700 text-sm font-bold mb-2" for="editor_en">
                                            頁面內容 (英文) <span class="text-red-500">*</span>
                                        </label>
                                        <!-- Quill editor container -->
                                        <div id="editor_en"
                                            class="border rounded w-full focus:outline-none focus:shadow-outline"
                                            style="min-height: 300px;"></div>

                                        <!-- Hidden input to store content -->
                                        <input type="hidden" name="content_en" id="content_en"
                                            value="<%= page.content_en %>">
                                    </div>
                                </div>

                                <!-- Existing Attachments -->
                                <% if (page.attachments && page.attachments.length> 0) { %>
                                    <div class="mb-4 mt-6 pt-4 border-t border-gray-200">
                                        <label class="block text-gray-700 text-sm font-bold mb-2">
                                            現有附件
                                        </label>
                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            <% page.attachments.forEach(attachment=> { %>
                                                <div class="border rounded-lg p-3 relative attachment-item"
                                                    data-id="<%= attachment.id %>">
                                                    <div class="flex items-center">
                                                        <% if (attachment.mimeType.startsWith('image/')) { %>
                                                            <img src="<%= attachment.path %>"
                                                                alt="<%= attachment.originalName %>"
                                                                class="w-16 h-16 object-cover mr-3">
                                                            <% } else if (attachment.mimeType.startsWith('video/')) { %>
                                                                <div
                                                                    class="w-16 h-16 bg-gray-200 flex items-center justify-center mr-3">
                                                                    <svg class="w-8 h-8 text-gray-500"
                                                                        fill="currentColor" viewBox="0 0 20 20">
                                                                        <path
                                                                            d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zm12.553 1.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z">
                                                                        </path>
                                                                    </svg>
                                                                </div>
                                                                <% } else if (attachment.mimeType==='application/pdf' )
                                                                    { %>
                                                                    <div
                                                                        class="w-16 h-16 bg-gray-200 flex items-center justify-center mr-3">
                                                                        <svg class="w-8 h-8 text-gray-500"
                                                                            fill="currentColor" viewBox="0 0 20 20">
                                                                            <path fill-rule="evenodd"
                                                                                d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                                                                                clip-rule="evenodd"></path>
                                                                        </svg>
                                                                    </div>
                                                                    <% } else if (attachment.mimeType.includes('word')
                                                                        || attachment.mimeType.includes('document')) {
                                                                        %>
                                                                        <div
                                                                            class="w-16 h-16 bg-gray-200 flex items-center justify-center mr-3">
                                                                            <svg class="w-8 h-8 text-gray-500"
                                                                                fill="currentColor" viewBox="0 0 20 20">
                                                                                <path fill-rule="evenodd"
                                                                                    d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
                                                                                    clip-rule="evenodd"></path>
                                                                            </svg>
                                                                        </div>
                                                                        <% } else if
                                                                            (attachment.mimeType.includes('excel') ||
                                                                            attachment.mimeType.includes('spreadsheet'))
                                                                            { %>
                                                                            <div
                                                                                class="w-16 h-16 bg-gray-200 flex items-center justify-center mr-3">
                                                                                <svg class="w-8 h-8 text-gray-500"
                                                                                    fill="currentColor"
                                                                                    viewBox="0 0 20 20">
                                                                                    <path fill-rule="evenodd"
                                                                                        d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z"
                                                                                        clip-rule="evenodd"></path>
                                                                                </svg>
                                                                            </div>
                                                                            <% } else { %>
                                                                                <div
                                                                                    class="w-16 h-16 bg-gray-200 flex items-center justify-center mr-3">
                                                                                    <svg class="w-8 h-8 text-gray-500"
                                                                                        fill="currentColor"
                                                                                        viewBox="0 0 20 20">
                                                                                        <path fill-rule="evenodd"
                                                                                            d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
                                                                                            clip-rule="evenodd"></path>
                                                                                    </svg>
                                                                                </div>
                                                                                <% } %>
                                                                                    <div class="flex-1 min-w-0">
                                                                                        <p
                                                                                            class="text-sm font-medium text-gray-900 truncate">
                                                                                            <%= attachment.originalName
                                                                                                %>
                                                                                        </p>
                                                                                        <p
                                                                                            class="text-xs text-gray-500">
                                                                                            <%= (attachment.size /
                                                                                                1024).toFixed(2) %> KB
                                                                                        </p>
                                                                                        <div class="flex mt-1">
                                                                                            <a href="<%= attachment.path %>"
                                                                                                target="_blank"
                                                                                                class="text-xs text-blue-600 hover:underline mr-3">查看</a>
                                                                                            <button type="button"
                                                                                                class="text-xs text-red-600 hover:underline delete-attachment"
                                                                                                data-id="<%= attachment.id %>">刪除</button>
                                                                                        </div>
                                                                                    </div>
                                                    </div>
                                                    <input type="checkbox" name="deletedAttachments"
                                                        value="<%= attachment.id %>"
                                                        class="hidden attachment-delete-checkbox">
                                                </div>
                                                <% }); %>
                                        </div>
                                    </div>
                                    <% } %>

                                        <!-- New Attachments -->
                                        <div class="mb-4 mt-6 pt-4 border-t border-gray-200">
                                            <label class="block text-gray-700 text-sm font-bold mb-2" for="attachments">
                                                添加新附件
                                            </label>
                                            <input
                                                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                type="file" id="attachments" name="attachments" multiple>
                                            <p class="mt-1 text-xs text-gray-500">
                                                允許的檔案類型：JPG、PNG、PDF、Word、Excel、MP4。每個檔案最大 50MB。
                                            </p>
                                        </div>

                                        <!-- Status -->
                                        <div class="mb-4">
                                            <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
                                                狀態 <span class="text-red-500">*</span>
                                            </label>
                                            <select
                                                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                id="status" name="status" required>
                                                <option value="draft" <%=page.status==='draft' ? 'selected' : '' %>
                                                    >草稿</option>
                                                <option value="published" <%=page.status==='published' ? 'selected' : ''
                                                    %>>已發布</option>
                                                <option value="archived" <%=page.status==='archived' ? 'selected' : ''
                                                    %>>已封存</option>
                                            </select>
                                            <p class="mt-1 text-xs text-gray-500">選擇頁面的發布狀態</p>
                                        </div>

                                        <!-- Form Actions -->
                                        <div class="mt-6 flex justify-end space-x-2">
                                            <a href="/admin/pages"
                                                class="px-4 py-2 text-sm font-medium leading-5 text-gray-700 transition-colors duration-150 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                                                取消
                                            </a>
                                            <% if (page.status==='published' ) { %>
                                                <a href="/page/<%= page.slug %>" target="_blank"
                                                    class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                                                    查看頁面
                                                </a>
                                                <% } %>
                                                    <button type="submit"
                                                        class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                        更新頁面
                                                    </button>
                                        </div>
                            </form>
                        </div>
    </div>

    <%- contentFor('head') %>
       <script>
            // Initialize Quill editors
            const quillOptions = {
                theme: 'snow',
                modules: {
                    toolbar: [
                        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                        ['bold', 'italic', 'underline', 'strike'],
                        [{ 'color': [] }, { 'background': [] }],
                        [{ 'align': [] }],
                        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                        ['link', 'image', 'video'],
                        ['clean'],
                        ['code-block', 'blockquote']
                    ]
                },
                placeholder: '撰寫內容...'
            };

            var quill_en = new Quill('#editor_en', quillOptions);
            var quill_tw = new Quill('#editor_tw', { ...quillOptions, placeholder: '撰寫內容...' });

            // Handle language tabs
            const tabs = document.querySelectorAll('[data-tabs-target]');
            const tabContents = document.querySelectorAll('[role="tabpanel"]');

            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const target = document.querySelector(tab.dataset.tabsTarget);

                    tabContents.forEach(tabContent => {
                        tabContent.classList.add('hidden');
                    });

                    tabs.forEach(tab => {
                        tab.classList.remove('active', 'border-blue-600');
                        tab.classList.add('border-transparent');
                        tab.setAttribute('aria-selected', false);
                    });

                    tab.classList.add('active', 'border-blue-600');
                    tab.classList.remove('border-transparent');
                    tab.setAttribute('aria-selected', true);

                    target.classList.remove('hidden');
                });
            });

            // Set initial content
            try {
                // English content
                const content_en = document.getElementById('content_en').value;
                if (content_en) {
                    try {
                        const delta_en = JSON.parse(content_en);
                        quill_en.setContents(delta_en);
                    } catch (e) {
                        // If not valid JSON, set as HTML
                        quill_en.clipboard.dangerouslyPasteHTML(content_en);
                    }
                }

                // Traditional Chinese content
                const content_tw = document.getElementById('content_tw').value;
                if (content_tw) {
                    try {
                        const delta_tw = JSON.parse(content_tw);
                        quill_tw.setContents(delta_tw);
                    } catch (e) {
                        // If not valid JSON, set as HTML
                        quill_tw.clipboard.dangerouslyPasteHTML(content_tw);
                    }
                }
            } catch (e) {
                console.error('Error setting editor content:', e);
            }

            // Function to truncate content if needed
            function truncateContent(content, maxLength) {
                // Use 100MB as the maximum length for LongText fields
                const MAX_LONGTEXT_LENGTH = 100 * 1024 * 1024; // 100MB

                // If maxLength is not provided, use the maximum LongText length
                const limit = maxLength || MAX_LONGTEXT_LENGTH;

                if (content && content.length > limit) {
                    console.warn(`Content truncated from ${content.length} to ${limit} characters`);
                    return content.substring(0, limit);
                }
                return content;
            }

            // Add a function to check content length before submission
            function checkContentLength() {
                const MAX_CONTENT_LENGTH = 100 * 1024 * 1024; // 100MB limit
                const contentEn = document.getElementById('content_en').value;
                const contentTw = document.getElementById('content_tw').value;

                let warningMessage = '';

                if (contentEn && contentEn.length > MAX_CONTENT_LENGTH) {
                    warningMessage += `英文內容太大 (${(contentEn.length / (1024 * 1024)).toFixed(2)}MB)。將被截斷至 100MB。\n`;
                }

                if (contentTw && contentTw.length > MAX_CONTENT_LENGTH) {
                    warningMessage += `中文內容太大 (${(contentTw.length / (1024 * 1024)).toFixed(2)}MB)。將被截斷至 100MB。\n`;
                }

                if (warningMessage) {
                    return confirm(warningMessage + '是否要繼續？');
                }

                return true;
            }

            // When form is submitted, update the hidden input with the active editor's contents
            document.getElementById('pageForm').addEventListener('submit', function (e) {
                e.preventDefault();

                try {
                    // Get Quill content
                    const content_en = quill_en.getContents();
                    const content_tw = quill_tw.getContents();

                    // Validate content
                    if (!content_en || !content_tw) {
                        alert('請確保中英文內容都已填寫');
                        return;
                    }

                    // Store Quill content as Delta JSON
                    document.getElementById('content_en').value = JSON.stringify(content_en);
                    document.getElementById('content_tw').value = JSON.stringify(content_tw);

                    // Check content length before submission
                    if (checkContentLength()) {
                        this.submit();
                    }
                } catch (error) {
                    console.error('Error preparing form data:', error);
                    alert('處理表單數據時發生錯誤，請重試');
                }
            });

            // Add image handler for English editor
            const imageHandlerEn = () => {
                const input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.click();

                input.onchange = () => {
                    const file = input.files[0];
                    
                    // Check file size (using environment variable or default to 5MB)
                    const maxSize = <%= parseInt(process.env.MAX_IMAGE_SIZE) || 5 * 1024 * 1024 %>;
                    if (file.size > maxSize) {
                        alert(`File too large! Maximum size is ${maxSize / (1024 * 1024)}MB`);
                        return;
                    }
                    
                    // Ask user for alt text
                    const altText = prompt('Please enter alt text for this image (for accessibility):');
                    
                    // Simple validation - if canceled or empty, use file name
                    const finalAltText = (altText === null || altText.trim() === '') 
                        ? file.name : altText.trim();
                    
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = () => {
                            const base64 = reader.result;
                            const range = quill_en.getSelection(true);
                            quill_en.insertEmbed(range.index, 'image', base64);
                            
                            // Get the image that was just inserted and add alt text
                            setTimeout(() => {
                                const images = quill_en.root.querySelectorAll('img');
                                const insertedImage = images[images.length - 1];
                                insertedImage.alt = finalAltText;
                            }, 10);
                        };
                        reader.readAsDataURL(file);
                    }
                };
            };

            // Add image handler for Traditional Chinese editor
            const imageHandlerTw = () => {
                const input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.click();

                input.onchange = () => {
                    const file = input.files[0];
                    
                    // Check file size (using environment variable or default to 5MB)
                    const maxSize = <%= parseInt(process.env.MAX_IMAGE_SIZE) || 5 * 1024 * 1024 %>;
                    if (file.size > maxSize) {
                        alert(`檔案太大！最大檔案大小為 ${maxSize / (1024 * 1024)}MB`);
                        return;
                    }
                    
                    // Ask user for alt text
                    const altText = prompt('請輸入圖片替代文字（為了無障礙網頁）:');
                    
                    // Simple validation - if canceled or empty, use file name
                    const finalAltText = (altText === null || altText.trim() === '') 
                        ? file.name : altText.trim();
                    
                    if (file) {
                        const reader = new FileReader();
                        reader.onload = () => {
                            const base64 = reader.result;
                            const range = quill_tw.getSelection(true);
                            quill_tw.insertEmbed(range.index, 'image', base64);
                            
                            // Get the image that was just inserted and add alt text
                            setTimeout(() => {
                                const images = quill_tw.root.querySelectorAll('img');
                                const insertedImage = images[images.length - 1];
                                insertedImage.alt = finalAltText;
                            }, 10);
                        };
                        reader.readAsDataURL(file);
                    }
                };
            };

            // Attach image handlers to editor toolbars
            quill_en.getModule('toolbar').addHandler('image', imageHandlerEn);
            quill_tw.getModule('toolbar').addHandler('image', imageHandlerTw);

            // Handle attachment deletion
            document.querySelectorAll('.delete-attachment').forEach(button => {
                button.addEventListener('click', function () {
                    const attachmentId = this.getAttribute('data-id');
                    const attachmentItem = document.querySelector(`.attachment-item[data-id="${attachmentId}"]`);
                    const checkbox = attachmentItem.querySelector('.attachment-delete-checkbox');

                    if (confirm('確定要刪除此附件嗎？')) {
                        checkbox.checked = true;
                        attachmentItem.style.opacity = '0.5';
                        this.textContent = '已標記刪除';
                        this.disabled = true;
                    }
                });
            });
        </script>

        <style>
            /* Custom styles for Quill editors */
            #editor_en,
            #editor_tw {
                height: 300px;
                margin-bottom: 30px;
            }

            /* Fix toolbar positioning */
            .ql-toolbar.ql-snow {
                border: 1px solid #ccc;
                box-sizing: border-box;
                font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
                padding: 8px;
            }

            .ql-container.ql-snow {
                border: 1px solid #ccc;
                border-top: 0px;
            }

            /* Ensure editor has proper height */
            .ql-editor {
                min-height: 200px;
            }
        </style>