<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">文章管理</h1>
            <a href="/admin/articles/create" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
                建立新文章
            </a>
        </div>

        <div class="bg-white shadow-sm rounded-lg overflow-hidden">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">標題
                            (英/中)</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分類
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">作者
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">狀態
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">建立日期
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">操作
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <% if (articles && articles.length> 0) { %>
                        <% articles.forEach(function(article) { %>
                            <tr>
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900 mb-1">
                                        <span
                                            class="inline-block bg-blue-100 text-blue-800 text-xs px-2 rounded mr-2">英</span>
                                        <%= article.title_en %>
                                    </div>
                                    <div class="text-sm font-medium text-gray-900">
                                        <span
                                            class="inline-block bg-red-100 text-red-800 text-xs px-2 rounded mr-2">中</span>
                                        <%= article.title_tw %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        <% if (article.category) { %>
                                            <div><span class="text-xs text-blue-600">英:</span>
                                                <%= article.category.name_en %>
                                            </div>
                                            <div><span class="text-xs text-red-600">中:</span>
                                                <%= article.category.name_tw %>
                                            </div>
                                            <% } else { %>
                                                未分類
                                                <% } %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        <%= article.author ? article.author.username : '未知' %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <% if (article.status==='published' ) { %>
                                        <span
                                            class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            已發佈
                                        </span>
                                        <% } else { %>
                                            <span
                                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                                草稿
                                            </span>
                                            <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-500">
                                        <%= new Date(article.createdAt).toLocaleDateString() %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="/admin/articles/edit/<%= article.id %>"
                                        class="text-blue-600 hover:text-blue-900 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="/admin/articles/delete/<%= article.id %>" method="POST"
                                        class="inline">
                                        <button type="submit" class="text-red-600 hover:text-red-900"
                                            onclick="return confirm('您確定要刪除此文章嗎？')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            <% }); %>
                                <% } else { %>
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 text-center text-gray-500">
                                            尚未找到任何文章。
                                        </td>
                                    </tr>
                                    <% } %>
                </tbody>
            </table>
        </div>
    </div>