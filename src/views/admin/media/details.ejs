<%- include('../../partials/admin/header') %>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">媒體詳情</h3>
                        <div class="card-tools">
                            <a href="/admin/media" class="btn btn-sm btn-secondary">返回媒體庫</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <% if (media.mimeType.startsWith('image/')) { %>
                                    <img src="/<%= media.path %>" alt="<%= media.alt %>" class="img-fluid">
                                    <% } else { %>
                                        <div class="file-preview">
                                            <i class="fas fa-file fa-5x"></i>
                                            <p class="mt-2">
                                                <%= media.originalName %>
                                            </p>
                                        </div>
                                        <% } %>
                            </div>
                            <div class="col-md-6">
                                <table class="table">
                                    <tbody>
                                        <tr>
                                            <th>原始檔名</th>
                                            <td>
                                                <%= media.originalName %>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>檔案類型</th>
                                            <td>
                                                <%= media.mimeType %>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>檔案大小</th>
                                            <td>
                                                <%= (media.size / 1024).toFixed(2) %> KB
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>替代文字</th>
                                            <td>
                                                <%= media.alt || '未設定' %>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>說明文字</th>
                                            <td>
                                                <%= media.caption || '未設定' %>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>上傳者</th>
                                            <td>
                                                <%= media.uploader.username %>
                                            </td>
                                        </tr>
                                        <tr>
                                            <th>上傳日期</th>
                                            <td>
                                                <%= media.createdAt.toLocaleString() %>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <div class="mt-3">
                                    <a href="/admin/media/edit/<%= media.id %>" class="btn btn-primary">編輯</a>
                                    <form action="/admin/media/<%= media.id %>" method="POST" class="d-inline"
                                        onsubmit="return confirm('您確定要刪除此媒體檔案嗎？');">
                                        <input type="hidden" name="_method" value="DELETE">
                                        <button type="submit" class="btn btn-danger">刪除</button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%- include('../../partials/admin/footer') %>