<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">媒體庫</h1>
            <button onclick="document.getElementById('uploadModal').classList.remove('hidden')"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-upload mr-2"></i> 上傳媒體
            </button>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <% media.forEach(function(item) { %>
                <div class="bg-white rounded-lg shadow-md overflow-hidden">
                    <% if (item.mimeType.startsWith('image/')) { %>
                        <div class="aspect-w-16 aspect-h-9">
                            <img src="<%= item.path %>" class="object-cover w-full h-full"
                                alt="<%= item.originalName %>">
                        </div>
                        <% } else { %>
                            <div class="aspect-w-16 aspect-h-9 bg-gray-100 flex items-center justify-center">
                                <i class="fas fa-file fa-3x text-gray-400"></i>
                            </div>
                            <% } %>
                                <div class="p-4">
                                    <h5 class="text-lg font-semibold text-gray-800 truncate"
                                        title="<%= item.originalName %>">
                                        <%= item.originalName %>
                                    </h5>
                                    <p class="text-sm text-gray-600 mt-1">
                                        <span>上傳者: <%= item.uploader.username %></span><br>
                                        <span>大小: <%= (item.size / 1024).toFixed(2) %> KB</span>
                                    </p>
                                    <div class="mt-4 flex justify-between">
                                        <button class="text-blue-600 hover:text-blue-800 copy-url"
                                            data-url="<%= item.path %>">
                                            <i class="fas fa-copy"></i> 複製網址
                                        </button>
                                        <form action="/admin/media/<%= item.id %>/delete" method="POST" class="inline">
                                            <button type="submit" class="text-red-600 hover:text-red-800"
                                                onclick="return confirm('您確定要刪除此檔案嗎？')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </div>
                </div>
                <% }); %>
        </div>
    </div>

    <!-- Upload Modal -->
    <div id="uploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">上傳媒體</h3>
                <form action="/admin/media/upload" method="POST" enctype="multipart/form-data">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2" for="file">
                            選擇檔案
                        </label>
                        <input type="file" name="file" id="file" required
                            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline">
                    </div>
                    <div class="flex items-center justify-between mt-6">
                        <button type="button" onclick="document.getElementById('uploadModal').classList.add('hidden')"
                            class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            取消
                        </button>
                        <button type="submit"
                            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            上傳
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <%- contentFor('scripts') %>
        <script>
            document.querySelectorAll('.copy-url').forEach(button => {
                button.addEventListener('click', function () {
                    const url = this.dataset.url;
                    navigator.clipboard.writeText(window.location.origin + url).then(() => {
                        // Change button text temporarily
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-check"></i> 已複製!';
                        setTimeout(() => {
                            this.innerHTML = originalText;
                        }, 2000);
                    });
                });
            });
        </script>

        <%- contentFor('style') %>
            <style>
                .aspect-w-16 {
                    position: relative;
                    padding-bottom: 56.25%;
                }

                .aspect-w-16>* {
                    position: absolute;
                    height: 100%;
                    width: 100%;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                }
            </style>