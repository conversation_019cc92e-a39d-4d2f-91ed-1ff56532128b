<%- contentFor('body') %>

  <div class="container px-6 mx-auto grid">
    <h2 class="my-6 text-2xl font-semibold text-gray-700">
      <%= category ? '編輯推動方案分類' : '建立推動方案分類' %>
    </h2>

            <!-- Category Form -->
            <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
              <form
                action="<%= category ? `/admin/promotions/categories/${category.id}` : '/admin/promotions/categories' %>"
                method="POST">

                <!-- Chinese Name -->
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700" for="name_tw">
                    名稱 (中文) <span class="text-red-500">*</span>
                  </label>
                  <input
                    class="block w-full mt-1 text-sm border-gray-300 rounded-md px-3 py-2 border shadow-sm focus:border-purple-400 focus:outline-none focus:shadow-outline-purple form-input"
                    placeholder="請輸入中文分類名稱" type="text" id="name_tw" name="name_tw"
                    value="<%= category ? category.name_tw : '' %>" required />
                </div>

                <!-- English Name -->
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700" for="name_en">
                    名稱 (英文) <span class="text-red-500">*</span>
                  </label>
                  <input
                    class="block w-full mt-1 text-sm border-gray-300 rounded-md px-3 py-2 border shadow-sm focus:border-purple-400 focus:outline-none focus:shadow-outline-purple form-input"
                    placeholder="請輸入英文分類名稱" type="text" id="name_en" name="name_en"
                    value="<%= category ? category.name_en : '' %>" required />
                  <p class="mt-1 text-xs text-gray-500">此名稱將用於生成網址代碼。</p>
                </div>

                <!-- Chinese Description -->
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700" for="description_tw">
                    描述 (中文)
                  </label>
                  <textarea
                    class="block w-full mt-1 text-sm border-gray-300 rounded-md px-3 py-2 border shadow-sm focus:border-purple-400 focus:outline-none focus:shadow-outline-purple form-textarea"
                    rows="3" placeholder="請輸入中文分類描述" id="description_tw"
                    name="description_tw"><%= category ? category.description_tw : '' %></textarea>
                </div>

                <!-- English Description -->
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700" for="description_en">
                    描述 (英文)
                  </label>
                  <textarea
                    class="block w-full mt-1 text-sm border-gray-300 rounded-md px-3 py-2 border shadow-sm focus:border-purple-400 focus:outline-none focus:shadow-outline-purple form-textarea"
                    rows="3" placeholder="請輸入英文分類描述" id="description_en"
                    name="description_en"><%= category ? category.description_en : '' %></textarea>
                </div>

                <!-- Display Order -->
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700" for="order">
                    顯示順序
                  </label>
                  <input
                    class="block w-full mt-1 text-sm border-gray-300 rounded-md px-3 py-2 border shadow-sm focus:border-purple-400 focus:outline-none focus:shadow-outline-purple form-input"
                    placeholder="請輸入顯示順序（數字越小排序越前）" type="number" id="order" name="order"
                    value="<%= category ? category.order : '0' %>" min="0" />
                  <p class="mt-1 text-xs text-gray-500">數字越小的分類在列表中顯示越靠前。</p>
                </div>

                <!-- Form Actions -->
                <div class="flex justify-end mt-6 space-x-3">
                  <a href="/admin/promotions/categories"
                    class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-gray-600 border border-transparent rounded-lg active:bg-gray-600 hover:bg-gray-700 focus:outline-none focus:shadow-outline-gray">
                    取消
                  </a>
                  <button type="submit"
                    class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-purple-600 border border-transparent rounded-lg active:bg-purple-600 hover:bg-purple-700 focus:outline-none focus:shadow-outline-purple">
                    <%= category ? '更新分類' : '建立分類' %>
                  </button>
                </div>
              </form>
            </div>

  </div>