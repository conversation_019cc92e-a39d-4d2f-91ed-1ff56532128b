<%- contentFor('title') %>
<%= title %>
<% end %>

<div class="container mx-auto px-4 py-6">
  <!-- Header -->
  <div class="mb-6">
    <h1 class="text-3xl font-bold text-gray-900"><%= title %></h1>
    <p class="text-gray-600 mt-2">編輯推動方案分類資訊</p>
  </div>

  <!-- Breadcrumb -->
  <nav class="flex mb-6" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li class="inline-flex items-center">
        <a href="/admin/dashboard" class="text-gray-700 hover:text-blue-600">
          <i class="fas fa-home mr-2"></i>後台首頁
        </a>
      </li>
      <li>
        <div class="flex items-center">
          <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
          <a href="/admin/promotions/categories" class="text-gray-700 hover:text-blue-600">推動方案分類</a>
        </div>
      </li>
      <li>
        <div class="flex items-center">
          <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
          <span class="text-gray-500">編輯分類</span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Flash Messages -->
  <% if (typeof success_msg !== 'undefined' && success_msg.length > 0) { %>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      <%= success_msg %>
    </div>
  <% } %>

  <% if (typeof error_msg !== 'undefined' && error_msg.length > 0) { %>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <%= error_msg %>
    </div>
  <% } %>

  <!-- Form -->
  <div class="bg-white shadow-md rounded-lg p-6">
    <form action="/admin/promotions/categories/<%= category.id %>" method="POST">
      <!-- Category Names -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label class="block text-gray-700 text-sm font-bold mb-2" for="name_tw">
            分類名稱 (中文) <span class="text-red-500">*</span>
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="name_tw" name="name_tw" type="text" required
            value="<%= category.name_tw %>"
            placeholder="輸入中文分類名稱">
        </div>

        <div>
          <label class="block text-gray-700 text-sm font-bold mb-2" for="name_en">
            分類名稱 (英文) <span class="text-red-500">*</span>
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="name_en" name="name_en" type="text" required
            value="<%= category.name_en %>"
            placeholder="Enter English category name">
        </div>
      </div>

      <!-- Descriptions -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div>
          <label class="block text-gray-700 text-sm font-bold mb-2" for="description_tw">
            描述 (中文)
          </label>
          <textarea
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="description_tw" name="description_tw" rows="3"
            placeholder="輸入中文描述（選填）"><%= category.description_tw || '' %></textarea>
        </div>

        <div>
          <label class="block text-gray-700 text-sm font-bold mb-2" for="description_en">
            描述 (英文)
          </label>
          <textarea
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="description_en" name="description_en" rows="3"
            placeholder="Enter English description (optional)"><%= category.description_en || '' %></textarea>
        </div>
      </div>

      <!-- Order -->
      <div class="mb-6">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="order">
          顯示順序
        </label>
        <input
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="order" name="order" type="number" value="<%= category.order %>" min="0"
          placeholder="輸入顯示順序（數字越小越前面）">
        <p class="text-gray-600 text-xs italic mt-1">數字越小越前面顯示</p>
      </div>

      <!-- Actions -->
      <div class="flex items-center justify-end space-x-4">
        <a href="/admin/promotions/categories" 
           class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded transition-colors">
          取消
        </a>
        <button type="submit" 
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded transition-colors">
          <i class="fas fa-save mr-2"></i>更新分類
        </button>
      </div>
    </form>
  </div>
</div>

<%- contentFor('scripts') %>
<script>
  // Form validation
  document.querySelector('form').addEventListener('submit', function(e) {
    const nameTw = document.getElementById('name_tw').value.trim();
    const nameEn = document.getElementById('name_en').value.trim();
    
    if (!nameTw || !nameEn) {
      e.preventDefault();
      alert('請填寫分類名稱（中英文）');
      return false;
    }
  });
</script>
<% end %>
