<%- contentFor('title') %>
<%= title %>
<% end %>

<%- contentFor('styles') %>
<style>
  .category-card {
    transition: all 0.3s ease;
  }
  .category-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
</style>
<% end %>

<div class="container mx-auto px-4 py-6">
  <!-- Header -->
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-3xl font-bold text-gray-900"><%= title %></h1>
      <p class="text-gray-600 mt-2">管理推動方案的分類設定</p>
    </div>
    <a href="/admin/promotions/categories/create" 
       class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-colors">
      <i class="fas fa-plus mr-2"></i>新增分類
    </a>
  </div>

  <!-- Breadcrumb -->
  <nav class="flex mb-6" aria-label="Breadcrumb">
    <ol class="inline-flex items-center space-x-1 md:space-x-3">
      <li class="inline-flex items-center">
        <a href="/admin/dashboard" class="text-gray-700 hover:text-blue-600">
          <i class="fas fa-home mr-2"></i>後台首頁
        </a>
      </li>
      <li>
        <div class="flex items-center">
          <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
          <span class="text-gray-500">推動方案分類</span>
        </div>
      </li>
    </ol>
  </nav>

  <!-- Flash Messages -->
  <% if (typeof success_msg !== 'undefined' && success_msg.length > 0) { %>
    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
      <%= success_msg %>
    </div>
  <% } %>

  <% if (typeof error_msg !== 'undefined' && error_msg.length > 0) { %>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <%= error_msg %>
    </div>
  <% } %>

  <!-- Categories Grid -->
  <% if (categories && categories.length > 0) { %>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <% categories.forEach(function(category) { %>
        <div class="category-card bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <!-- Category Header -->
          <div class="flex justify-between items-start mb-4">
            <div class="flex-1">
              <h3 class="text-lg font-semibold text-gray-900 mb-1">
                <%= category.name_tw %>
              </h3>
              <p class="text-sm text-gray-600">
                <%= category.name_en %>
              </p>
            </div>
            <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded">
              順序: <%= category.order %>
            </span>
          </div>

          <!-- Description -->
          <% if (category.description_tw || category.description_en) { %>
            <div class="mb-4">
              <% if (category.description_tw) { %>
                <p class="text-sm text-gray-700 mb-1"><%= category.description_tw %></p>
              <% } %>
              <% if (category.description_en) { %>
                <p class="text-xs text-gray-500"><%= category.description_en %></p>
              <% } %>
            </div>
          <% } %>

          <!-- Stats -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center text-sm text-gray-600">
              <i class="fas fa-layer-group mr-2"></i>
              <span><%= category._count.groups %> 個分組</span>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex space-x-2">
            <a href="/admin/promotions/categories/edit/<%= category.id %>" 
               class="flex-1 bg-yellow-500 hover:bg-yellow-600 text-white text-sm font-medium py-2 px-3 rounded text-center transition-colors">
              <i class="fas fa-edit mr-1"></i>編輯
            </a>
            <a href="/admin/promotions/groups?categoryId=<%= category.id %>" 
               class="flex-1 bg-green-500 hover:bg-green-600 text-white text-sm font-medium py-2 px-3 rounded text-center transition-colors">
              <i class="fas fa-layer-group mr-1"></i>分組
            </a>
            <% if (category._count.groups === 0) { %>
              <form action="/admin/promotions/categories/<%= category.id %>/delete" method="POST" class="inline" 
                    onsubmit="return confirm('確定要刪除此分類嗎？')">
                <button type="submit" 
                        class="bg-red-500 hover:bg-red-600 text-white text-sm font-medium py-2 px-3 rounded transition-colors">
                  <i class="fas fa-trash"></i>
                </button>
              </form>
            <% } else { %>
              <button disabled 
                      class="bg-gray-300 text-gray-500 text-sm font-medium py-2 px-3 rounded cursor-not-allowed"
                      title="包含分組的分類無法刪除">
                <i class="fas fa-trash"></i>
              </button>
            <% } %>
          </div>
        </div>
      <% }); %>
    </div>
  <% } else { %>
    <!-- Empty State -->
    <div class="text-center py-12">
      <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <i class="fas fa-folder-open text-3xl text-gray-400"></i>
      </div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">尚無推動方案分類</h3>
      <p class="text-gray-500 mb-6">開始建立您的第一個推動方案分類</p>
      <a href="/admin/promotions/categories/create" 
         class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg transition-colors">
        <i class="fas fa-plus mr-2"></i>新增分類
      </a>
    </div>
  <% } %>
</div>

<%- contentFor('scripts') %>
<script>
  // Add any JavaScript functionality here if needed
</script>
<% end %>
