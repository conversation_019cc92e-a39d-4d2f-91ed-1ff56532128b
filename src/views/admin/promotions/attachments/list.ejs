<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">推動方案附件</h1>
      <div class="space-x-2">
        <a href="/admin/promotions"
          class="bg-white hover:bg-gray-100 text-gray-600 border border-gray-300 font-bold py-2 px-4 rounded">
          <i class="fas fa-arrow-left mr-2"></i> 返回推動方案頁面
        </a>
        <a href="/admin/promotions/groups"
          class="bg-white hover:bg-gray-100 text-green-500 border border-green-500 font-bold py-2 px-4 rounded">
          <i class="fas fa-layer-group mr-2"></i> 管理分組
        </a>
        <a href="/admin/promotions/attachments/create"
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          <i class="fas fa-plus mr-2"></i> 新增附件
        </a>
      </div>
    </div>

    <!-- Flash Messages -->
    <% if(typeof success_msg !=='undefined' && success_msg.length> 0) { %>
      <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
        <%= success_msg %>
      </div>
      <% } %>
        <% if(typeof error_msg !=='undefined' && error_msg.length> 0) { %>
          <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
            <%= error_msg %>
          </div>
          <% } %>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <% if(attachments.length> 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white" id="dataTable">
            <thead>
              <tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left whitespace-nowrap">分類/分組</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">檔案名稱</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">顯示名稱</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">檔案大小</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">建立時間</th>
                <th class="py-3 px-6 text-center whitespace-nowrap">操作</th>
              </tr>
            </thead>
            <tbody class="text-gray-600 text-sm">
              <% attachments.forEach(function(attachment) { %>
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">
                      <%= attachment.group?.category?.name_tw || '未分類' %>
                    </div>
                    <div class="text-sm text-gray-500">
                      <%= attachment.group?.name_tw || '未分組' %>
                    </div>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">
                      <%= attachment.originalName %>
                    </div>
                    <div class="text-sm text-gray-500">
                      <%= attachment.mimeType %>
                    </div>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">
                      <%= attachment.attachment_name_tw || attachment.title_tw || '-' %>
                    </div>
                    <div class="text-sm text-gray-500">
                      <%= attachment.attachment_name_en || attachment.title_en || '-' %>
                    </div>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= Math.round(attachment.size / 1024) %> KB
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= new Date(attachment.createdAt).toLocaleDateString('zh-TW') %>
                  </td>
                  <td class="py-3 px-6 text-center whitespace-nowrap">
                    <div class="flex item-center justify-center">
                      <a href="/admin/promotions/attachments/<%= attachment.id %>/download"
                        class="text-green-600 hover:text-green-900 mx-1" title="下載">
                        <i class="fas fa-download"></i>
                      </a>
                      <a href="/admin/promotions/attachments/edit/<%= attachment.id %>"
                        class="text-blue-600 hover:text-blue-900 mx-1" title="編輯">
                        <i class="fas fa-edit"></i>
                      </a>
                      <button class="text-red-600 hover:text-red-900 mx-1" title="刪除"
                        onclick="document.getElementById('deleteModal<%= attachment.id %>').classList.remove('hidden')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>

                <!-- Delete Modal -->
                <div id="deleteModal<%= attachment.id %>"
                  class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
                  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                      <h3 class="text-lg leading-6 font-medium text-gray-900">確認刪除</h3>
                      <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                          您確定要刪除附件「<%= attachment.originalName %>」嗎？此操作無法復原。
                        </p>
                      </div>
                      <div class="flex justify-end mt-4">
                        <button type="button"
                          class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                          onclick="document.getElementById('deleteModal<%= attachment.id %>').classList.add('hidden')">
                          取消
                        </button>
                        <form action="/admin/promotions/attachments/<%= attachment.id %>/delete" method="POST" class="inline">
                          <button type="submit"
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            刪除
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
                <% }); %>
            </tbody>
          </table>
        </div>
        <% } else { %>
          <div class="text-center py-8">
            <p class="text-gray-500 mb-4">未找到任何推動方案附件。</p>
            <a href="/admin/promotions/attachments/create"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              <i class="fas fa-plus mr-2"></i> 新增第一個附件
            </a>
          </div>
          <% } %>
    </div>
  </div>

<%- contentFor('script') %>
<script>
  $(document).ready(function () {
    $('#dataTable').DataTable({
      "order": [[4, "desc"]]
    });
  });
</script>
