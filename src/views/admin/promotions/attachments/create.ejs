<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold">新增推動方案附件</h1>
      <div class="space-x-2">
        <a href="/admin/promotions"
          class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-semibold py-2 px-4 rounded">
          <i class="fas fa-home mr-2"></i> 推動方案頁面
        </a>
        <a href="/admin/promotions/attachments"
          class="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded">
          <i class="fas fa-arrow-left mr-2"></i> 返回列表
        </a>
      </div>
    </div>

    <%- include('../../../partials/messages') %>

      <!-- Attachment Form -->
      <div class="bg-white shadow rounded-lg p-6">
        <form action="/admin/promotions/attachments" method="POST" enctype="multipart/form-data">
          <!-- File Upload -->
          <div class="mb-6 pb-4 border-b border-gray-200">
            <div class="flex items-center mb-4">
              <i class="fas fa-file-upload mr-2 text-gray-600"></i>
              <h3 class="text-lg font-medium text-gray-700">檔案上傳</h3>
            </div>

            <div class="mb-4">
              <label for="file" class="block text-sm font-medium text-gray-700 mb-2">
                選擇檔案 <span class="text-red-500">*</span>
              </label>
              <input type="file" id="file" name="file" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                accept=".pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar,.jpg,.jpeg,.png,.gif" />
              <p class="mt-1 text-xs text-gray-500">支援格式：PDF, Word, Excel, PowerPoint, 壓縮檔, 圖片檔</p>
            </div>
          </div>

          <!-- Group Selection -->
          <div class="mb-6 pb-4 border-b border-gray-200">
            <div class="flex items-center mb-4">
              <i class="fas fa-layer-group mr-2 text-gray-600"></i>
              <h3 class="text-lg font-medium text-gray-700">分組選擇</h3>
            </div>

            <div class="mb-4">
              <label for="categoryId" class="block text-sm font-medium text-gray-700 mb-2">
                分類
              </label>
              <select id="categoryId" name="categoryId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                <option value="">選擇分類</option>
                <% categories.forEach(function(category) { %>
                  <option value="<%= category.id %>">
                    <%= category.name_tw %> / <%= category.name_en %>
                  </option>
                  <% }); %>
              </select>
            </div>

            <div class="mb-4">
              <label for="groupId" class="block text-sm font-medium text-gray-700 mb-2">
                分組 <span class="text-red-500">*</span>
              </label>
              <select id="groupId" name="groupId" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                <option value="">請先選擇分類</option>
              </select>
              <p class="mt-1 text-xs text-gray-500">請選擇此附件所屬的分組</p>
            </div>
          </div>

          <!-- Display Names -->
          <div class="mb-6 pb-4 border-b border-gray-200">
            <div class="flex items-center mb-4">
              <i class="fas fa-tag mr-2 text-gray-600"></i>
              <h3 class="text-lg font-medium text-gray-700">顯示名稱</h3>
            </div>

            <!-- Attachment Name (Traditional Chinese) -->
            <div class="mb-4">
              <label for="attachment_name_tw" class="block text-sm font-medium text-gray-700 mb-2">
                附件名稱 (中文)
              </label>
              <input type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                id="attachment_name_tw" name="attachment_name_tw" placeholder="例如：操作手冊" />
              <p class="mt-1 text-xs text-gray-500">留空則使用原始檔案名稱</p>
            </div>

            <!-- Attachment Name (English) -->
            <div class="mb-4">
              <label for="attachment_name_en" class="block text-sm font-medium text-gray-700 mb-2">
                附件名稱 (英文)
              </label>
              <input type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                id="attachment_name_en" name="attachment_name_en" placeholder="例如：Operation Manual" />
              <p class="mt-1 text-xs text-gray-500">留空則使用原始檔案名稱</p>
            </div>
          </div>

          <!-- Titles -->
          <div class="mb-6 pb-4 border-b border-gray-200">
            <div class="flex items-center mb-4">
              <i class="fas fa-heading mr-2 text-gray-600"></i>
              <h3 class="text-lg font-medium text-gray-700">標題 (選填)</h3>
            </div>

            <!-- Title (Traditional Chinese) -->
            <div class="mb-4">
              <label for="title_tw" class="block text-sm font-medium text-gray-700 mb-2">
                標題 (中文)
              </label>
              <input type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                id="title_tw" name="title_tw" />
            </div>

            <!-- Title (English) -->
            <div class="mb-4">
              <label for="title_en" class="block text-sm font-medium text-gray-700 mb-2">
                標題 (英文)
              </label>
              <input type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                id="title_en" name="title_en" />
            </div>
          </div>

          <!-- Descriptions -->
          <div class="mb-6">
            <div class="flex items-center mb-4">
              <i class="fas fa-align-left mr-2 text-gray-600"></i>
              <h3 class="text-lg font-medium text-gray-700">描述 (選填)</h3>
            </div>

            <!-- Description (Traditional Chinese) -->
            <div class="mb-4">
              <label for="description_tw" class="block text-sm font-medium text-gray-700 mb-2">
                描述 (中文)
              </label>
              <textarea
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                id="description_tw" name="description_tw" rows="3"></textarea>
            </div>

            <!-- Description (English) -->
            <div class="mb-4">
              <label for="description_en" class="block text-sm font-medium text-gray-700 mb-2">
                描述 (英文)
              </label>
              <textarea
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                id="description_en" name="description_en" rows="3"></textarea>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end">
            <a href="/admin/promotions/attachments"
              class="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded mr-2">
              取消
            </a>
            <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded">
              新增附件
            </button>
          </div>
        </form>
      </div>
  </div>

<%- contentFor('script') %>
<script>
  // Dynamic group loading based on category selection
  document.getElementById('categoryId').addEventListener('change', function () {
    const categoryId = this.value;
    const groupSelect = document.getElementById('groupId');

    // Clear existing options
    groupSelect.innerHTML = '<option value="">載入中...</option>';

    if (categoryId) {
      fetch(`/admin/promotions/groups/by-category/${categoryId}`)
        .then(response => response.json())
        .then(groups => {
          groupSelect.innerHTML = '<option value="">選擇分組</option>';
          groups.forEach(group => {
            const option = document.createElement('option');
            option.value = group.id;
            option.textContent = `${group.name_tw} / ${group.name_en}`;
            groupSelect.appendChild(option);
          });
        })
        .catch(error => {
          console.error('Error loading groups:', error);
          groupSelect.innerHTML = '<option value="">載入失敗</option>';
        });
    } else {
      groupSelect.innerHTML = '<option value="">請先選擇分類</option>';
    }
  });
</script>
