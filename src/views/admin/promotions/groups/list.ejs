<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">推動方案分組</h1>
      <div class="space-x-2">
        <a href="/admin/promotions"
          class="bg-white hover:bg-gray-100 text-gray-600 border border-gray-300 font-bold py-2 px-4 rounded">
          <i class="fas fa-arrow-left mr-2"></i> 返回推動方案頁面
        </a>
        <a href="/admin/promotions/categories"
          class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-bold py-2 px-4 rounded">
          <i class="fas fa-cog mr-2"></i> 管理分類
        </a>
        <a href="/admin/promotions/groups/create"
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          <i class="fas fa-plus mr-2"></i> 新增分組
        </a>
      </div>
    </div>

    <!-- Flash Messages -->
    <% if(typeof success_msg !=='undefined' && success_msg.length> 0) { %>
      <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
        <%= success_msg %>
      </div>
      <% } %>
        <% if(typeof error_msg !=='undefined' && error_msg.length> 0) { %>
          <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
            <%= error_msg %>
          </div>
          <% } %>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <% if(groups.length> 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white" id="dataTable">
            <thead>
              <tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left whitespace-nowrap">分類</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">順序</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">名稱 (英)</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">名稱 (中)</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">附件數</th>
                <th class="py-3 px-6 text-center whitespace-nowrap">操作</th>
              </tr>
            </thead>
            <tbody class="text-gray-600 text-sm">
              <% groups.forEach(function(group) { %>
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900">
                      <%= group.category.name_tw %>
                    </div>
                    <div class="text-sm text-gray-500">
                      <%= group.category.name_en %>
                    </div>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= group.order %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= group.name_en %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= group.name_tw %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= group._count?.attachments || 0 %>
                  </td>
                  <td class="py-3 px-6 text-center whitespace-nowrap">
                    <div class="flex item-center justify-center">
                      <a href="/admin/promotions/groups/edit/<%= group.id %>"
                        class="text-blue-600 hover:text-blue-900 mx-1">
                        <i class="fas fa-edit"></i>
                      </a>
                      <button class="text-red-600 hover:text-red-900 mx-1"
                        onclick="document.getElementById('deleteModal<%= group.id %>').classList.remove('hidden')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>

                <!-- Delete Modal -->
                <div id="deleteModal<%= group.id %>"
                  class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
                  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                      <h3 class="text-lg leading-6 font-medium text-gray-900">確認刪除</h3>
                      <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                          您確定要刪除分組「<%= group.name_tw %>」嗎？這將同時刪除此分組中的所有附件。
                        </p>
                      </div>
                      <div class="flex justify-end mt-4">
                        <button type="button"
                          class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                          onclick="document.getElementById('deleteModal<%= group.id %>').classList.add('hidden')">
                          取消
                        </button>
                        <form action="/admin/promotions/groups/<%= group.id %>/delete" method="POST" class="inline">
                          <button type="submit"
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            刪除
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
                <% }); %>
            </tbody>
          </table>
        </div>
        <% } else { %>
          <div class="text-center py-8">
            <p class="text-gray-500 mb-4">未找到任何推動方案分組。</p>
            <a href="/admin/promotions/groups/create"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              <i class="fas fa-plus mr-2"></i> 新增第一個分組
            </a>
          </div>
          <% } %>
    </div>
  </div>

<%- contentFor('script') %>
<script>
  $(document).ready(function () {
    $('#dataTable').DataTable({
      "order": [[0, "asc"], [1, "asc"]]
    });
  });
</script>
