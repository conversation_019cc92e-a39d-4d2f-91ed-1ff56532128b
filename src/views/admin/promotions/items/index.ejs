<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">推動方案項目</h1>
      <div class="flex space-x-2">
        <a href="/admin/promotions/categories"
          class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
          <i class="fas fa-cog mr-2"></i> 管理分類
        </a>
        <a href="/admin/promotions/items/create"
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          <i class="fas fa-plus mr-2"></i> 新增項目
        </a>
      </div>
    </div>

    <!-- Search and Filter -->
    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <!-- Items Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white" id="dataTable">
          <thead>
            <tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
              <th class="py-3 px-6 text-left whitespace-nowrap">標題</th>
              <th class="py-3 px-6 text-left whitespace-nowrap">分類</th>
              <th class="py-3 px-6 text-left whitespace-nowrap">狀態</th>
              <th class="py-3 px-6 text-left whitespace-nowrap">發佈日期</th>
              <th class="py-3 px-6 text-center whitespace-nowrap">操作</th>
            </tr>
          </thead>
          <tbody class="text-gray-600 text-sm">
            <% if (items.length> 0) { %>
              <% items.forEach(item=> { %>
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                  <td class="px-6 py-3 whitespace-nowrap">
                    <div class="flex items-center text-sm">
                      <% if (item.imagePath) { %>
                        <div class="relative hidden w-8 h-8 mr-3 rounded-full md:block">
                          <img class="object-cover w-full h-full rounded-full" src="/<%= item.imagePath %>"
                            alt="<%= item.title_en %>" loading="lazy" />
                          <div class="absolute inset-0 rounded-full shadow-inner" aria-hidden="true"></div>
                        </div>
                        <% } %>
                          <div>
                            <p class="font-semibold">
                              <%= item.title_tw %>
                            </p>
                            <p class="text-xs text-gray-600">
                              <%= item.title_en %>
                            </p>
                          </div>
                    </div>
                  </td>
                  <td class="px-6 py-3 whitespace-nowrap text-sm">
                    <%= item.category ? item.category.name_tw : '未分類' %>
                  </td>
                  <td class="px-6 py-3 whitespace-nowrap text-sm">
                    <% if (item.status==='published' ) { %>
                      <span class="px-2 py-1 font-semibold leading-tight text-green-700 bg-green-100 rounded-full">
                        已發佈
                      </span>
                      <% } else if (item.status==='draft' ) { %>
                        <span class="px-2 py-1 font-semibold leading-tight text-orange-700 bg-orange-100 rounded-full">
                          草稿
                        </span>
                        <% } else { %>
                          <span class="px-2 py-1 font-semibold leading-tight text-gray-700 bg-gray-100 rounded-full">
                            已封存
                          </span>
                          <% } %>
                  </td>
                  <td class="px-6 py-3 whitespace-nowrap text-sm">
                    <%= item.publishedDate ? new Date(item.publishedDate).toLocaleDateString() : '未設定' %>
                  </td>
                  <td class="px-6 py-3 whitespace-nowrap text-center">
                    <div class="flex item-center justify-center">
                      <a href="/admin/promotions/items/edit/<%= item.id %>"
                        class="text-blue-600 hover:text-blue-900 mr-2">
                        <i class="fas fa-edit"></i>
                      </a>
                      <button class="text-red-600 hover:text-red-900"
                        onclick="document.getElementById('deleteModal<%= item.id %>').classList.remove('hidden')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>

                <!-- Delete Modal -->
                <div id="deleteModal<%= item.id %>"
                  class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
                  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                      <h3 class="text-lg leading-6 font-medium text-gray-900">確認刪除</h3>
                      <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                          您確定要刪除項目「<%= item.title_en %>」嗎？
                        </p>
                      </div>
                      <div class="flex justify-center mt-4 px-4 py-3">
                        <button type="button"
                          class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2"
                          onclick="document.getElementById('deleteModal<%= item.id %>').classList.add('hidden')">
                          取消
                        </button>
                        <form action="/admin/promotions/items/<%= item.id %>/delete" method="POST">
                          <button type="submit"
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            刪除
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
                <% }); %>
                  <% } else { %>
                    <tr>
                      <td colspan="5" class="px-4 py-3 whitespace-nowrap text-center text-gray-500">
                        尚未找到任何推動方案項目。<a href="/admin/promotions/items/create"
                          class="text-purple-600 hover:underline">立即建立一個</a>。
                      </td>
                    </tr>
                    <% } %>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <% if (pagination && pagination.totalPages> 1) { %>
        <div class="flex justify-between items-center mt-6">
          <span class="text-sm text-gray-700">
            顯示 <%= (pagination.currentPage - 1) * pagination.perPage + 1 %> 至 <%= Math.min(pagination.currentPage *
                pagination.perPage, pagination.totalItems) %> 項，共 <%= pagination.totalItems %> 項
          </span>
          <nav aria-label="頁面導航">
            <ul class="inline-flex items-center">
              <li>
                <a href="<%= pagination.currentPage > 1 ? `/admin/promotions/items?page=${pagination.currentPage - 1}` : '#' %>"
                  class="<%= pagination.currentPage > 1 ? 'px-3 py-1 rounded-md focus:outline-none focus:shadow-outline-purple' : 'px-3 py-1 text-gray-400 cursor-not-allowed' %>">
                  上一頁
                </a>
              </li>
              <% for (let i=1; i <=pagination.totalPages; i++) { %>
                <li>
                  <a href="/admin/promotions/items?page=<%= i %>"
                    class="<%= pagination.currentPage === i ? 'px-3 py-1 text-white transition-colors duration-150 bg-purple-600 border border-r-0 border-purple-600 rounded-md focus:outline-none focus:shadow-outline-purple' : 'px-3 py-1 rounded-md focus:outline-none focus:shadow-outline-purple' %>">
                    <%= i %>
                  </a>
                </li>
                <% } %>
                  <li>
                    <a href="<%= pagination.currentPage < pagination.totalPages ? `/admin/promotions/items?page=${pagination.currentPage + 1}` : '#' %>"
                      class="<%= pagination.currentPage < pagination.totalPages ? 'px-3 py-1 rounded-md focus:outline-none focus:shadow-outline-purple' : 'px-3 py-1 text-gray-400 cursor-not-allowed' %>">
                      下一頁
                    </a>
                  </li>
            </ul>
          </nav>
        </div>
        <% } %>
    </div>
  </div>

  <%- contentFor('script') %>
    <script>
      $(document).ready(function () {
        $('#dataTable').DataTable({
          "order": [[3, "desc"]],
          "paging": false,
          "info": false
        });
      });
    </script>