<%- contentFor('body') %>

  <div class="container px-6 mx-auto grid">
    <div class="flex justify-between items-center my-6">
      <h2 class="text-2xl font-semibold text-gray-700">
        <%= title %>
      </h2>
      <a href="/admin/promotions/categories"
        class="bg-gray-500 hover:bg-gray-600 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center">
        <i class="fas fa-cog mr-2"></i> 管理分類
      </a>
    </div>

    <!-- Item Form -->
    <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
      <form action="<%= item ? `/admin/promotions/items/${item.id}` : '/admin/promotions/items' %>" method="POST"
        enctype="multipart/form-data">
        <!-- Basic Information Section -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b">基本資訊</h3>

          <!-- Category -->
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2" for="categoryId">
              分類 <span class="text-red-500">*</span>
            </label>
            <select
              class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="categoryId" name="categoryId" required>
              <option value="">選擇分類</option>
              <% if (categories && categories.length> 0) { %>
                <% categories.forEach(category=> { %>
                  <option value="<%= category.id %>" <%=item && item.categoryId===category.id ? 'selected' : '' %>>
                    <%= category.name_tw %> / <%= category.name_en %>
                  </option>
                  <% }); %>
                    <% } else { %>
                      <option value="" disabled>沒有可用的分類</option>
                      <% } %>
            </select>
            <p class="mt-1 text-xs text-gray-500">請為此推動方案項目選擇一個分類。</p>
          </div>

          <!-- Status -->
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
              狀態 <span class="text-red-500">*</span>
            </label>
            <select
              class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="status" name="status" required>
              <option value="draft" <%=item && item.status==='draft' ? 'selected' : '' %>>草稿</option>
              <option value="published" <%=item && item.status==='published' ? 'selected' : '' %>>已發布
              </option>
              <option value="archived" <%=item && item.status==='archived' ? 'selected' : '' %>>已封存
              </option>
            </select>
          </div>

          <!-- Published Date -->
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2" for="publishedDate">
              發布日期 <span class="text-red-500">*</span>
            </label>
            <input
              class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              type="date" id="publishedDate" name="publishedDate"
              value="<%= item ? item.formattedPublishedDate : new Date().toISOString().split('T')[0] %>" required />
          </div>
        </div>

        <!-- Chinese Content Section -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b">中文內容</h3>

          <!-- Chinese Title -->
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2" for="title_tw">
              標題 (中文) <span class="text-red-500">*</span>
            </label>
            <input
              class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="請輸入中文標題" type="text" id="title_tw" name="title_tw" value="<%= item ? item.title_tw : '' %>"
              required />
          </div>

          <!-- Chinese Content -->
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2" for="content_tw">
              內容 (中文) <span class="text-red-500">*</span>
            </label>
            <div id="editor_tw" class="mt-1 quill-editor" data-required="true"></div>
            <textarea
              class="hidden shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              rows="10" placeholder="請輸入中文HTML內容" id="content_tw"
              name="content_tw"><%= item ? item.content_tw : '' %></textarea>
            <p class="mt-1 text-xs text-gray-500">您可以使用HTML標籤進行格式化。您也可以嵌入平台項目。</p>
          </div>
        </div>

        <!-- English Content Section -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b">英文內容</h3>

          <!-- English Title -->
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2" for="title_en">
              標題 (英文) <span class="text-red-500">*</span>
            </label>
            <input
              class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              placeholder="請輸入英文標題" type="text" id="title_en" name="title_en" value="<%= item ? item.title_en : '' %>"
              required />
            <p class="mt-1 text-xs text-gray-500">此標題將用於生成網址代碼。</p>
          </div>

          <!-- English Content -->
          <div class="mb-4">
            <label class="block text-gray-700 text-sm font-bold mb-2" for="content_en">
              內容 (英文) <span class="text-red-500">*</span>
            </label>
            <div id="editor_en" class="mt-1 quill-editor" data-required="true"></div>
            <textarea
              class="hidden shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              rows="10" placeholder="請輸入英文HTML內容" id="content_en"
              name="content_en"><%= item ? item.content_en : '' %></textarea>
            <p class="mt-1 text-xs text-gray-500">您可以使用HTML標籤進行格式化。您也可以嵌入平台項目。</p>
          </div>
        </div>

        <!-- External Link -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b">連結設定</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2" for="url">
                外部連結 (中文)
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="請輸入外部網址 (例如：https://example.com)" type="url" id="url" name="url"
                value="<%= item ? item.url : '' %>" />
              <p class="mt-1 text-xs text-gray-500">如果提供，中文介面按鈕將連結到此網址。</p>
            </div>
            <div>
              <label class="block text-gray-700 text-sm font-bold mb-2" for="urlEn">
                外部連結 (英文)
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                placeholder="Please enter external URL (e.g., https://example.com)" type="url" id="urlEn" name="urlEn"
                value="<%= item ? item.urlEn : '' %>" />
              <p class="mt-1 text-xs text-gray-500">如果提供，英文介面按鈕將連結到此網址。</p>
            </div>
          </div>
        </div>

        <!-- Image Section -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-700 mb-4 pb-2 border-b">特色圖片</h3>

          <!-- Current Image (if editing) -->
          <% if (item && item.imagePath) { %>
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2">
                目前圖片 (中文)
              </label>
              <div class="relative w-64 h-48 mb-2">
                <img src="/<%= item.imagePath %>" alt="<%= item.title_en %>"
                  class="object-cover w-full h-full rounded-md" />
              </div>
              <div class="flex items-center">
                <input type="checkbox" id="removeImage" name="removeImage" value="true"
                  class="mr-2 text-purple-600 border-gray-300 rounded focus:ring-purple-500" />
                <label for="removeImage" class="text-sm text-gray-700">
                  移除目前圖片
                </label>
              </div>
            </div>
            <% } %>

          <!-- Current English Image (if editing) -->
          <% if (item && item.imagePathEn) { %>
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2">
                目前圖片 (英文)
              </label>
              <div class="relative w-64 h-48 mb-2">
                <img src="/<%= item.imagePathEn %>" alt="<%= item.title_en %>"
                  class="object-cover w-full h-full rounded-md" />
              </div>
              <div class="flex items-center">
                <input type="checkbox" id="removeImageEn" name="removeImageEn" value="true"
                  class="mr-2 text-purple-600 border-gray-300 rounded focus:ring-purple-500" />
                <label for="removeImageEn" class="text-sm text-gray-700">
                  移除目前英文圖片
                </label>
              </div>
            </div>
            <% } %>

              <!-- Upload New Image -->
              <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="image">
                  <%= item && item.imagePath ? '替換圖片 (中文)' : '上傳圖片 (中文)' %>
                </label>
                <input type="file" id="image" name="image" accept="image/*"
                  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" />
                <p class="mt-1 text-xs text-gray-500">建議尺寸：1200x500像素。最大檔案大小：5MB。</p>
              </div>
              
              <!-- Upload New English Image -->
              <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="imageEn">
                  <%= item && item.imagePathEn ? '替換圖片 (英文)' : '上傳圖片 (英文)' %>
                </label>
                <input type="file" id="imageEn" name="imageEn" accept="image/*"
                  class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" />
                <p class="mt-1 text-xs text-gray-500">建議尺寸：1200x500像素。最大檔案大小：5MB。</p>
              </div>
        </div>

        <!-- Form Actions -->
        <div class="flex justify-end mt-6 space-x-3">
          <a href="/admin/promotions/items"
            class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-gray-600 border border-transparent rounded-lg active:bg-gray-600 hover:bg-gray-700 focus:outline-none focus:shadow-outline-gray">
            取消
          </a>
          <button type="submit"
            class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-purple-600 border border-transparent rounded-lg active:bg-purple-600 hover:bg-purple-700 focus:outline-none focus:shadow-outline-purple">
            <%= item ? '更新推動方案項目' : '建立推動方案項目' %>
          </button>
        </div>
      </form>
    </div>

  </div>

  <!-- Platform Embed Handler -->
  <script>
    // Platform Embed Handler
    window.platformEmbedHandler = function () {
      const activeEditor = this.quill;
      const editorId = activeEditor.container.id;
      const language = editorId === 'editor_en' ? 'en' : 'tw';

      // Create a platform ID input dialog
      const platformId = prompt(`輸入要插入的平台ID（目前在${language === 'en' ? '英文' : '中文'}編輯器）：`);

      if (platformId && !isNaN(platformId)) {
        // Instead of trying to insert HTML directly, we'll insert a text placeholder
        // that will be processed by the backend
        const platformPlaceholder = `[PLATFORM:${platformId}:${language}]`;

        // Insert at current cursor position
        const range = activeEditor.getSelection(true);
        activeEditor.insertText(range.index, platformPlaceholder, 'user');
        activeEditor.setSelection(range.index + platformPlaceholder.length, 'silent');

        // Feedback to user
        console.log(`已插入平台ID ${platformId}，語言：${language}`);
      }
    };
  </script>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Initialize Quill editors
      const toolbarOptions = [
        [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
        ['bold', 'italic', 'underline', 'strike'],
        [{ 'color': [] }, { 'background': [] }],
        [{ 'align': [] }],
        [{ 'list': 'ordered' }, { 'list': 'bullet' }],
        ['link', 'image'],
        ['clean'],
        ['platform'] // Custom button for platform embed
      ];

      // Function to enhance platform placeholders in the editor
      function enhancePlatformPlaceholders(editor) {
        const content = editor.container.querySelector('.ql-editor');
        if (!content) return;

        // Find all platform placeholders
        const regex = /\[PLATFORM:(\d+):([a-z]{2})\]/g;
        const html = content.innerHTML;

        // Replace placeholders with styled spans
        const enhancedHtml = html.replace(regex, (match, id, lang) => {
          return `<span class="platform-placeholder" data-id="${id}" data-lang="${lang}">[Platform: ${id}]</span>`;
        });

        if (html !== enhancedHtml) {
          content.innerHTML = enhancedHtml;
        }
      }

      // Add event listener to update placeholders after changes
      function setupPlatformPlaceholderEnhancement(editor) {
        editor.on('text-change', function () {
          // Use setTimeout to let Quill finish its rendering
          setTimeout(() => {
            enhancePlatformPlaceholders(editor);
          }, 10);
        });
      }

      // Initialize English editor
      const quill_en = new Quill('#editor_en', {
        theme: 'snow',
        modules: {
          toolbar: {
            container: toolbarOptions,
            handlers: {
              'platform': window.platformEmbedHandler,
              'image': function() {
                // Image handler for English editor
                const input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.click();

                input.onchange = () => {
                  const file = input.files[0];
                  if (file) {
                    const reader = new FileReader();
                    reader.onload = () => {
                      const base64 = reader.result;
                      const range = quill_en.getSelection(true);
                      const altText = prompt("Enter alt text for the image (English):");
                      if (altText) {
                        const img = document.createElement('img');
                        img.src = base64;
                        img.alt = altText;
                        quill_en.clipboard.dangerouslyPasteHTML(range.index, img.outerHTML);
                      } else {
                        quill_en.insertEmbed(range.index, 'image', base64);
                      }
                    };
                    reader.readAsDataURL(file);
                  }
                };
              }
            }
          }
        },
        placeholder: '撰寫英文內容...'
      });

      // Initialize Traditional Chinese editor
      const quill_tw = new Quill('#editor_tw', {
        theme: 'snow',
        modules: {
          toolbar: {
            container: toolbarOptions,
            handlers: {
              'platform': window.platformEmbedHandler,
              'image': function() {
                // Image handler for Traditional Chinese editor
                const input = document.createElement('input');
                input.setAttribute('type', 'file');
                input.setAttribute('accept', 'image/*');
                input.click();

                input.onchange = () => {
                  const file = input.files[0];
                  if (file) {
                    const reader = new FileReader();
                    reader.onload = () => {
                      const base64 = reader.result;
                      const range = quill_tw.getSelection(true);
                      const altText = prompt("輸入圖片替代文字 (中文):");
                      if (altText) {
                        const img = document.createElement('img');
                        img.src = base64;
                        img.alt = altText;
                        quill_tw.clipboard.dangerouslyPasteHTML(range.index, img.outerHTML);
                      } else {
                        quill_tw.insertEmbed(range.index, 'image', base64);
                      }
                    };
                    reader.readAsDataURL(file);
                  }
                };
              }
            }
          }
        },
        placeholder: '撰寫中文內容...'
      });

      // Set initial content
      const content_en = document.getElementById('content_en').value;
      const content_tw = document.getElementById('content_tw').value;

      if (content_en) {
        quill_en.root.innerHTML = content_en;
      }

      if (content_tw) {
        quill_tw.root.innerHTML = content_tw;
      }

      // Apply platform placeholder enhancement
      enhancePlatformPlaceholders(quill_en);
      enhancePlatformPlaceholders(quill_tw);

      // Set up enhancement to run on text changes
      setupPlatformPlaceholderEnhancement(quill_en);
      setupPlatformPlaceholderEnhancement(quill_tw);

      // Update hidden textarea on form submit
      const form = document.querySelector('form');
      form.addEventListener('submit', function (event) {
        // Transfer content from Quill to hidden textareas
        document.getElementById('content_en').value = quill_en.root.innerHTML;
        document.getElementById('content_tw').value = quill_tw.root.innerHTML;

        // Validate required Quill editors
        const requiredEditors = document.querySelectorAll('.quill-editor[data-required="true"]');
        let isValid = true;

        requiredEditors.forEach(editor => {
          const editorId = editor.id;
          const quillInstance = editorId === 'editor_en' ? quill_en : quill_tw;
          const content = quillInstance.getText().trim();

          if (content.length === 0) {
            isValid = false;
            // Add visual indication of error
            editor.style.border = '1px solid #EF4444';

            // Show error message
            const errorMsg = document.createElement('p');
            errorMsg.className = 'mt-1 text-xs text-red-500';
            errorMsg.textContent = '此欄位為必填';

            // Remove any existing error message
            const existingError = editor.parentNode.querySelector('.text-red-500');
            if (existingError) {
              editor.parentNode.removeChild(existingError);
            }

            editor.parentNode.insertBefore(errorMsg, editor.nextSibling);
          } else {
            // Reset any error styling
            editor.style.border = '';
            const existingError = editor.parentNode.querySelector('.text-red-500');
            if (existingError) {
              editor.parentNode.removeChild(existingError);
            }
          }
        });

        if (!isValid) {
          event.preventDefault();
        }
      });

      // Add platform icon to toolbar
      const platformButtons = document.querySelectorAll('.ql-platform');
      platformButtons.forEach(button => {
        button.innerHTML = '<i class="fas fa-cube"></i>';
        button.title = '插入平台項目';
      });
    });
  </script>

  <style>
    .quill-editor {
      height: 300px;
      margin-bottom: 1rem;
    }

    .ql-toolbar.ql-snow {
      border-top-left-radius: 0.375rem;
      border-top-right-radius: 0.375rem;
    }

    .ql-container.ql-snow {
      border-bottom-left-radius: 0.375rem;
      border-bottom-right-radius: 0.375rem;
    }

    .ql-editor {
      min-height: 200px;
    }
  </style>