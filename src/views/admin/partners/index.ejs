<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">合作夥伴項目</h1>
      <div>
        <a href="/admin/partners/categories"
          class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-bold py-2 px-4 rounded mr-2">
          <i class="fas fa-folder-open mr-2"></i> 管理分類
        </a>
        <a href="/admin/partners/create" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          <i class="fas fa-plus mr-2"></i> 新增項目
        </a>
      </div>
    </div>

    <% if (partners && partners.length > 0) { %>
      <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white" id="dataTable">
            <thead>
              <tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left whitespace-nowrap">ID</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">標題 (中文)</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">標題 (英文)</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">分類</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">狀態</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">順序</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">建立者</th>
                <th class="py-3 px-6 text-center whitespace-nowrap">操作</th>
              </tr>
            </thead>
            <tbody class="text-gray-600 text-sm">
              <% partners.forEach(function(partner) { %>
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= partner.id %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= partner.title_tw %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= partner.title_en %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= partner.category ? partner.category.name_tw : '無' %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <% if (partner.status === 'published') { %>
                      <span class="bg-green-200 text-green-800 py-1 px-3 rounded-full text-xs">已發布</span>
                    <% } else { %>
                      <span class="bg-yellow-200 text-yellow-800 py-1 px-3 rounded-full text-xs">草稿</span>
                    <% } %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= partner.order %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= partner.author ? partner.author.username : '未知' %>
                  </td>
                  <td class="py-3 px-6 text-center whitespace-nowrap">
                    <div class="flex items-center justify-center">
                      <a href="/admin/partners/<%= partner.id %>/edit" class="text-blue-600 hover:text-blue-900 mx-1">
                        <i class="fas fa-edit"></i>
                      </a>
                      <button type="button" class="text-red-600 hover:text-red-900 mx-1"
                        onclick="openDeleteModal('<%= partner.id %>', '<%= partner.title_en %>')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>

                    <!-- Delete Modal -->
                    <div id="deleteModal<%= partner.id %>"
                      class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden overflow-y-auto h-full w-full z-50">
                      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                        <div class="mt-3 text-center">
                          <h3 class="text-lg leading-6 font-medium text-gray-900">確認刪除</h3>
                          <div class="mt-2 px-7 py-3">
                            <p class="text-sm text-gray-500">
                              您確定要刪除「<%= partner.title_en %>」嗎？
                            </p>
                          </div>
                          <div class="flex justify-end mt-4">
                            <button type="button"
                              class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded mr-2"
                              onclick="closeDeleteModal('<%= partner.id %>')">
                              取消
                            </button>
                            <form action="/admin/partners/<%= partner.id %>/delete" method="POST" class="inline">
                              <button type="submit"
                                class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                                刪除
                              </button>
                            </form>
                          </div>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              <% }); %>
            </tbody>
          </table>
        </div>
      </div>
    <% } else { %>
      <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
        <div class="text-center py-5">
          <p class="text-gray-500">未找到合作夥伴項目。建立您的第一個項目！</p>
        </div>
      </div>
    <% } %>
  </div>

  <%- contentFor('scripts') %>
    <script>
      function openDeleteModal(id, title) {
        document.getElementById('deleteModal' + id).classList.remove('hidden');
      }

      function closeDeleteModal(id) {
        document.getElementById('deleteModal' + id).classList.add('hidden');
      }
    </script> 