<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">合作夥伴分類管理</h1>
      <a href="/admin/partners" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
        <i class="fas fa-arrow-left mr-2"></i> 返回合作夥伴
      </a>
    </div>

    <div class="grid md:grid-cols-2 gap-8">
      <!-- Category List -->
      <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="bg-gray-200 px-6 py-4">
          <h2 class="text-xl font-semibold">現有分類</h2>
        </div>
        <div class="p-6 overflow-x-auto">
          <% if (categories && categories.length > 0) { %>
            <table class="min-w-full divide-y divide-gray-200">
              <thead>
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">名稱</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">排序</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">操作</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <% categories.forEach(function(category) { %>
                  <tr>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900"><%= category.name_tw %></div>
                      <div class="text-sm text-gray-500"><%= category.name_en %></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900"><%= category.order %></div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button 
                        onclick="editCategory('<%= category.id %>', '<%= category.name_tw %>', '<%= category.name_en %>', '<%= category.description_tw || '' %>', '<%= category.description_en || '' %>', '<%= category.order %>')" 
                        class="text-indigo-600 hover:text-indigo-900 mr-3">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button 
                        onclick="confirmDelete('<%= category.id %>')" 
                        class="text-red-600 hover:text-red-900">
                        <i class="fas fa-trash"></i>
                      </button>
                    </td>
                  </tr>
                <% }); %>
              </tbody>
            </table>
          <% } else { %>
            <div class="text-center py-4 text-gray-500">
              尚未建立任何合作夥伴分類
            </div>
          <% } %>
        </div>
      </div>

      <!-- Category Form -->
      <div class="bg-white shadow-md rounded-lg overflow-hidden">
        <div class="bg-gray-200 px-6 py-4" id="formTitle">
          <h2 class="text-xl font-semibold">新增分類</h2>
        </div>
        <div class="p-6">
          <form id="categoryForm" action="/admin/partners/categories" method="POST">
            <input type="hidden" id="categoryId" name="categoryId">
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="name_tw">
                分類名稱 (中文) <span class="text-red-500">*</span>
              </label>
              <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" 
                id="name_tw" name="name_tw" type="text" required>
            </div>
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="name_en">
                分類名稱 (英文) <span class="text-red-500">*</span>
              </label>
              <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" 
                id="name_en" name="name_en" type="text" required>
            </div>
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="description_tw">
                描述 (中文)
              </label>
              <textarea class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" 
                id="description_tw" name="description_tw" rows="3"></textarea>
            </div>
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="description_en">
                描述 (英文)
              </label>
              <textarea class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" 
                id="description_en" name="description_en" rows="3"></textarea>
            </div>
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="order">
                排序
              </label>
              <input class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" 
                id="order" name="order" type="number" min="1" value="1">
              <div class="text-xs text-gray-600">最小值為 1，數字越小顯示越前面</div>
            </div>
            <div class="flex items-center justify-between">
              <button id="resetButton" type="button" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                重置
              </button>
              <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline" id="submitButton">
                建立分類
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center">
    <div class="bg-white rounded-lg p-8 max-w-md mx-auto">
      <h3 class="text-xl font-bold mb-4">確認刪除</h3>
      <p class="mb-6">您確定要刪除此分類嗎？此操作無法復原。</p>
      <div class="flex justify-end space-x-4">
        <button onclick="cancelDelete()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
          取消
        </button>
        <form id="deleteForm" method="POST">
          <input type="hidden" name="_method" value="DELETE">
          <button type="submit" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
            確認刪除
          </button>
        </form>
      </div>
    </div>
  </div>

  <%- contentFor('scripts') %>
    <script>
      function resetForm() {
        document.getElementById('categoryForm').reset();
        document.getElementById('categoryId').value = '';
        document.getElementById('formTitle').innerHTML = '<h2 class="text-xl font-semibold">新增分類</h2>';
        document.getElementById('submitButton').textContent = '建立分類';
        document.getElementById('categoryForm').action = '/admin/partners/categories';
      }

      function editCategory(id, name_tw, name_en, description_tw, description_en, order) {
        document.getElementById('categoryId').value = id;
        document.getElementById('name_tw').value = name_tw;
        document.getElementById('name_en').value = name_en;
        document.getElementById('description_tw').value = description_tw;
        document.getElementById('description_en').value = description_en;
        document.getElementById('order').value = Math.max(1, order);
        
        document.getElementById('formTitle').innerHTML = '<h2 class="text-xl font-semibold">編輯分類</h2>';
        document.getElementById('submitButton').textContent = '更新分類';

        // Update the form
        const form = document.getElementById('categoryForm');
        form.action = `/admin/partners/categories/${id}`;
        form.method = 'POST';
        
        // Add method override input if it doesn't exist
        let methodInput = form.querySelector('input[name="_method"]');
        if (!methodInput) {
          methodInput = document.createElement('input');
          methodInput.type = 'hidden';
          methodInput.name = '_method';
          form.appendChild(methodInput);
        }
        methodInput.value = 'PUT';
      }

      function confirmDelete(id) {
        document.getElementById('deleteForm').action = `/admin/partners/categories/${id}?_method=DELETE`;
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.add('flex');
      }

      function cancelDelete() {
        document.getElementById('deleteModal').classList.remove('flex');
        document.getElementById('deleteModal').classList.add('hidden');
      }

      document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('resetButton').addEventListener('click', resetForm);
      });
    </script>
</code_block_to_apply_changes_from> 