<%- contentFor('body') %>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">新增計畫</h1>
    <a href="/admin/projects" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
      <i class="fas fa-arrow-left mr-2"></i> 返回計畫列表
    </a>
  </div>

  <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
    <form action="/admin/projects" method="POST">
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="name_zh">
          計畫名稱 (中文) <span class="text-red-500">*</span>
        </label>
        <input
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="name_zh" name="name_zh" type="text" placeholder="請輸入計畫中文名稱" required>
      </div>
      
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="name_en">
          計畫名稱 (英文)
        </label>
        <input
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="name_en" name="name_en" type="text" placeholder="請輸入計畫英文名稱">
      </div>
      
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="description">
          計畫描述
        </label>
        <textarea
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="description" name="description" rows="4" placeholder="請輸入計畫描述"></textarea>
      </div>
      
      <div class="mb-4">
        <label class="flex items-center">
          <input type="radio" name="isActive" value="true" checked class="mr-2">
          <span class="text-sm">啟用</span>
        </label>
        <label class="flex items-center mt-2">
          <input type="radio" name="isActive" value="false" class="mr-2">
          <span class="text-sm">停用</span>
        </label>
      </div>
      
      <div class="flex items-center justify-end">
        <button
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          type="submit">
          建立計畫
        </button>
      </div>
    </form>
  </div>
</div> 