<%- contentFor('body') %>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-2xl font-bold">計畫管理</h1>
    <a href="/admin/projects/create" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
      <i class="fas fa-plus mr-2"></i> 新增計畫
    </a>
  </div>

  <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white">
        <thead>
          <tr class="bg-gray-200 text-gray-700 uppercase text-sm leading-normal">
            <th class="py-3 px-6 text-left">ID</th>
            <th class="py-3 px-6 text-left">計畫名稱 (中文)</th>
            <th class="py-3 px-6 text-left">計畫名稱 (英文)</th>
            <th class="py-3 px-6 text-left">狀態</th>
            <th class="py-3 px-6 text-left">操作</th>
          </tr>
        </thead>
        <tbody class="text-gray-600 text-sm">
          <% if (locals.projects && projects.length > 0) { %>
            <% projects.forEach(project => { %>
              <tr class="border-b border-gray-200 hover:bg-gray-100">
                <td class="py-3 px-6 text-left"><%= project.id %></td>
                <td class="py-3 px-6 text-left"><%= project.name_zh %></td>
                <td class="py-3 px-6 text-left"><%= project.name_en || '未設定' %></td>
                <td class="py-3 px-6 text-left">
                  <span class="px-2 py-1 rounded <%= project.isActive ? 'bg-green-200 text-green-800' : 'bg-red-200 text-red-800' %>">
                    <%= project.isActive ? '啟用' : '停用' %>
                  </span>
                </td>
                <td class="py-3 px-6 text-left">
                  <div class="flex items-center space-x-2">
                    <a href="/admin/projects/edit/<%= project.id %>" class="text-blue-600 hover:text-blue-900" title="編輯">
                      <i class="fas fa-edit"></i>
                    </a>
                    <form action="/admin/projects/<%= project.id %>/delete" method="POST" class="inline-block delete-form" 
                          data-confirm="確定要刪除「<%= project.name_zh %>」計畫嗎？這將影響所有關聯的用戶和訪廠記錄。">
                      <button type="submit" class="text-red-600 hover:text-red-900" title="刪除">
                        <i class="fas fa-trash-alt"></i>
                      </button>
                    </form>
                  </div>
                </td>
              </tr>
            <% }); %>
          <% } else { %>
            <tr>
              <td class="py-3 px-6 text-center" colspan="5">目前沒有計畫資料</td>
            </tr>
          <% } %>
        </tbody>
      </table>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const deleteForms = document.querySelectorAll('.delete-form');
    
    deleteForms.forEach(form => {
      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const confirmMessage = this.getAttribute('data-confirm');
        if (confirm(confirmMessage)) {
          this.submit();
        }
      });
    });
  });
</script> 