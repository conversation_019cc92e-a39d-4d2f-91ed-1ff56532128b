<%- contentFor('body') %>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">訪廠資訊管理</h1>
    <% if (user.role !== 'visitor') { %>
    <a href="/admin/visits/create" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
      <i class="fas fa-plus mr-2"></i> 新增訪廠紀錄
    </a>
    <% } %>
  </div>

  <% if (typeof success_msg !== 'undefined' && success_msg.length > 0) { %>
  <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
    <p><%= success_msg %></p>
  </div>
  <% } %>

  <% if (typeof error_msg !== 'undefined' && error_msg.length > 0) { %>
  <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
    <p><%= error_msg %></p>
  </div>
  <% } %>

  <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
    <!-- Add search form -->
    <form action="/admin/visits" method="GET" class="flex flex-wrap items-end gap-4 mb-6">
      <div class="flex-1 min-w-[200px]">
        <label for="search" class="block text-sm font-medium text-gray-700 mb-1">搜尋</label>
        <input type="text" id="search" name="search" value="<%= locals.search || '' %>" placeholder="依廠商名稱搜尋..."
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
      </div>
      <div class="w-full sm:w-auto">
        <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">狀態</label>
        <select id="statusFilter" name="status" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          <option value="all">全部狀態</option>
          <option value="待安排" <%= locals.status === '待安排' ? 'selected' : '' %>>待安排</option>
          <option value="已安排" <%= locals.status === '已安排' ? 'selected' : '' %>>已安排</option>
          <option value="完成拜訪" <%= locals.status === '完成拜訪' ? 'selected' : '' %>>完成拜訪</option>
        </select>
      </div>
      <div class="w-full sm:w-auto">
        <label for="mouStatusFilter" class="block text-sm font-medium text-gray-700 mb-1">MOU</label>
        <select id="mouStatusFilter" name="mouStatus"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          <option value="all">全部</option>
          <option value="0" <%= locals.mouStatus === '0' ? 'selected' : '' %>>未用印</option>
          <option value="1" <%= locals.mouStatus === '1' ? 'selected' : '' %>>已用印</option>
        </select>
      </div>
      <div class="w-full sm:w-auto">
        <label for="projectFilter" class="block text-sm font-medium text-gray-700 mb-1">計畫</label>
        <select id="projectFilter" name="project"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
          <option value="all">全部計畫</option>
          <% if (locals.projects && projects.length > 0) { %>
            <% projects.forEach(project => { %>
              <option value="<%= project.id %>" <%= locals.selectedProject == project.id ? 'selected' : '' %>><%= project.name_zh %></option>
            <% }); %>
          <% } %>
        </select>
      </div>
      <div class="flex gap-2">
        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          篩選
        </button>
        <a href="/admin/visits" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
          重置
        </a>
      </div>
    </form>
    <!-- End search form -->
    
    <div class="flex flex-col md:flex-row justify-between items-center mb-4">
      <h2 class="text-2xl font-semibold mb-2 md:mb-0">訪廠紀錄列表</h2>
    </div>
    <div class="overflow-x-auto">
      <table class="min-w-full bg-white" id="visitsTable">
        <thead>
          <tr class="bg-gray-200 text-gray-700 uppercase text-sm leading-normal">
            <th class="py-3 px-6 text-left cursor-pointer" data-sort="visitDate">訪談日期 <i class="fas fa-sort"></i></th>
            <th class="py-3 px-6 text-left cursor-pointer" data-sort="companyName">廠商名稱 <i class="fas fa-sort"></i></th>
            <th class="py-3 px-6 text-left cursor-pointer" data-sort="contactPerson">訪談對象 <i class="fas fa-sort"></i></th>
            <th class="py-3 px-6 text-left cursor-pointer" data-sort="project">分項計畫名稱 <i class="fas fa-sort"></i></th>
            <th class="py-3 px-6 text-left cursor-pointer" data-sort="projectManager">本案負責人 <i class="fas fa-sort"></i></th>
            <th class="py-3 px-6 text-left cursor-pointer" data-sort="status">狀態 <i class="fas fa-sort"></i></th>
            <th class="py-3 px-6 text-left cursor-pointer" data-sort="mouStatus">MOU用印狀態 <i class="fas fa-sort"></i></th>
            <th class="py-3 px-6 text-left">操作</th>
          </tr>
        </thead>
        <tbody class="text-gray-600 text-sm">
          <% if (locals.visits && visits.length > 0) { %>
            <% visits.forEach(visit => { %>
              <tr class="border-b border-gray-200 hover:bg-gray-100" data-project-id="<%= visit.projectId || 'none' %>" data-visit-id="<%= visit.id %>" data-mou-status="<%= visit.mouStatus %>">
                <td class="py-3 px-6 text-left"><%= visit.visitDate ? visit.visitDate.toISOString().split('T')[0] : '未設定' %></td>
                <td class="py-3 px-6 text-left"><%= visit.companyName || '未設定' %></td>
                <td class="py-3 px-6 text-left"><%= visit.contactPerson || '未設定' %></td>
                <td class="py-3 px-6 text-left"><%= visit.project ? visit.project.name_zh : '未設定' %></td>
                <td class="py-3 px-6 text-left"><%= visit.projectManager || '未設定' %></td>
                <td class="py-3 px-6 text-left">
                  <span class="px-2 py-1 rounded <%= visit.status === '待安排' ? 'bg-yellow-200 text-yellow-800' : visit.status === '已安排' ? 'bg-blue-200 text-blue-800' : 'bg-green-200 text-green-800' %>">
                    <%= visit.status %>
                  </span>
                </td>
                <td class="py-3 px-6 text-left">
                  <span class="px-2 py-1 rounded <%= visit.mouStatus === 0 ? 'bg-red-200 text-red-800' : 'bg-green-200 text-green-800' %>">
                    <%= visit.mouStatus === 0 ? '未用印' : '已用印' %>
                  </span>
                </td>
                <td class="py-3 px-6 text-left">
                  <div class="flex items-center space-x-2">
                    <a href="/admin/visits/view/<%= visit.id %>" class="text-blue-600 hover:text-blue-900" title="查看詳情">
                      <i class="fas fa-eye"></i>
                    </a>
                    <% if (user.role === 'visitor') { %>
                      <!-- Visitor can only view, no edit/delete buttons -->
                    <% } else if (user.role === 'super_admin' || user.role === 'admin') { %>
                      <a href="/admin/visits/edit/<%= visit.id %>" class="text-green-600 hover:text-green-900" title="編輯">
                        <i class="fas fa-edit"></i>
                      </a>
                      <form action="/admin/visits/<%= visit.id %>/delete" method="POST" class="inline-block delete-form">
                        <button type="submit" class="text-red-600 hover:text-red-900 border-none bg-transparent cursor-pointer" title="刪除" onclick="return confirm('確定要刪除這個訪廠紀錄嗎？')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </form>
                    <% } else if (user.role === 'editor' && visit.createdById === user.id) { %>
                      <a href="/admin/visits/edit/<%= visit.id %>" class="text-green-600 hover:text-green-900" title="編輯">
                        <i class="fas fa-edit"></i>
                      </a>
                      <form action="/admin/visits/<%= visit.id %>/delete" method="POST" class="inline-block delete-form">
                        <button type="submit" class="text-red-600 hover:text-red-900 border-none bg-transparent cursor-pointer" title="刪除" onclick="return confirm('確定要刪除這個訪廠紀錄嗎？')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </form>
                    <% } else if (visit.createdById === user.id) { %>
                      <a href="/admin/visits/edit/<%= visit.id %>" class="text-green-600 hover:text-green-900" title="編輯">
                        <i class="fas fa-edit"></i>
                      </a>
                    <% } %>
                  </div>
                </td>
              </tr>
            <% }); %>
          <% } else { %>
            <tr>
              <td class="py-3 px-6 text-center" colspan="8">沒有找到訪廠紀錄</td>
            </tr>
          <% } %>
        </tbody>
      </table>
    </div>

    <!-- Pagination Controls -->
    <div class="mt-6 flex justify-between items-center">
      <div class="text-sm text-gray-700">
        顯示 <span id="currentDisplayStart">1</span> 到 <span id="currentDisplayEnd"><%= Math.min(10, locals.visits ? visits.length : 0) %></span> 筆，共 <span id="totalEntries"><%= locals.visits ? visits.length : 0 %></span> 筆記錄
      </div>
      <div class="flex items-center space-x-2">
        <select id="pageSizeSelector" class="shadow border rounded py-1 px-2 text-gray-700 text-sm leading-tight focus:outline-none focus:shadow-outline">
          <option value="10">10筆/頁</option>
          <option value="25">25筆/頁</option>
          <option value="50">50筆/頁</option>
          <option value="100">100筆/頁</option>
        </select>
        <button id="prevPage" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-1 px-3 rounded disabled:opacity-50 disabled:cursor-not-allowed">
          <i class="fas fa-chevron-left"></i>
        </button>
        <span id="currentPage" class="text-sm">第 <span id="pageNumber">1</span> 頁</span>
        <button id="nextPage" class="bg-gray-200 hover:bg-gray-300 text-gray-700 py-1 px-3 rounded disabled:opacity-50 disabled:cursor-not-allowed">
          <i class="fas fa-chevron-right"></i>
        </button>
      </div>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Table state management
    let state = {
      rows: Array.from(document.querySelectorAll('#visitsTable tbody tr')).filter(row => !row.querySelector('td[colspan]')),
      filteredRows: [],
      currentPage: 1,
      pageSize: 10,
      sortColumn: 'project',
      sortDirection: 'asc'
    };
    
    // Initialize filteredRows
    state.filteredRows = [...state.rows];
    
    // Select elements
    const prevPage = document.getElementById('prevPage');
    const nextPage = document.getElementById('nextPage');
    const pageSizeSelector = document.getElementById('pageSizeSelector');
    const currentDisplayStart = document.getElementById('currentDisplayStart');
    const currentDisplayEnd = document.getElementById('currentDisplayEnd');
    const totalEntries = document.getElementById('totalEntries');
    const pageNumber = document.getElementById('pageNumber');
    
    // Sorting function
    function sortTable(column, direction) {
      state.sortColumn = column;
      state.sortDirection = direction;
      
      state.filteredRows.sort((a, b) => {
        let aValue, bValue;
        
        switch (column) {
          case 'visitDate':
            aValue = a.cells[0].textContent.trim();
            bValue = b.cells[0].textContent.trim();
            break;
          case 'companyName':
            aValue = a.cells[1].textContent.trim();
            bValue = b.cells[1].textContent.trim();
            break;
          case 'contactPerson':
            aValue = a.cells[2].textContent.trim();
            bValue = b.cells[2].textContent.trim();
            break;
          case 'project':
            aValue = a.cells[3].textContent.trim();
            bValue = b.cells[3].textContent.trim();
            break;
          case 'projectManager':
            aValue = a.cells[4].textContent.trim();
            bValue = b.cells[4].textContent.trim();
            break;
          case 'status':
            aValue = a.cells[5].textContent.trim();
            bValue = b.cells[5].textContent.trim();
            break;
          case 'mouStatus':
            // Use the data attribute for proper numeric comparison
            aValue = parseInt(a.getAttribute('data-mou-status'));
            bValue = parseInt(b.getAttribute('data-mou-status'));
            if (direction === 'asc') {
              return aValue - bValue;
            } else {
              return bValue - aValue;
            }
            break;
          default:
            aValue = a.cells[0].textContent.trim();
            bValue = b.cells[0].textContent.trim();
        }
        
        if (direction === 'asc') {
          return aValue > bValue ? 1 : -1;
        } else {
          return aValue < bValue ? 1 : -1;
        }
      });
      
      updateTable();
    }
    
    // Update table with current state
    function updateTable() {
      const tbody = document.querySelector('#visitsTable tbody');
      
      // Clear table
      while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
      }
      
      // Calculate pagination
      const startIndex = (state.currentPage - 1) * state.pageSize;
      const endIndex = Math.min(startIndex + state.pageSize, state.filteredRows.length);
      const visibleRows = state.filteredRows.slice(startIndex, endIndex);
      
      // Update pagination display
      currentDisplayStart.textContent = state.filteredRows.length > 0 ? startIndex + 1 : 0;
      currentDisplayEnd.textContent = endIndex;
      totalEntries.textContent = state.filteredRows.length;
      pageNumber.textContent = state.currentPage;
      
      // Disable/enable pagination buttons
      prevPage.disabled = state.currentPage === 1;
      nextPage.disabled = endIndex >= state.filteredRows.length;
      
      // Show rows or "no results" message
      if (visibleRows.length > 0) {
        visibleRows.forEach(row => tbody.appendChild(row.cloneNode(true)));
      } else {
        const noResultsRow = document.createElement('tr');
        const noResultsCell = document.createElement('td');
        noResultsCell.setAttribute('colspan', '8'); // Updated to match the number of columns
        noResultsCell.setAttribute('class', 'py-3 px-6 text-center');
        noResultsCell.textContent = '沒有找到符合條件的訪廠紀錄';
        noResultsRow.appendChild(noResultsCell);
        tbody.appendChild(noResultsRow);
      }
    }
    
    // Sort headers
    const headers = document.querySelectorAll('[data-sort]');
    headers.forEach(header => {
      header.addEventListener('click', function() {
        const column = this.getAttribute('data-sort');
        const direction = state.sortColumn === column && state.sortDirection === 'asc' ? 'desc' : 'asc';
        
        // Reset all headers
        headers.forEach(h => {
          h.querySelector('i').className = 'fas fa-sort';
        });
        
        // Update clicked header
        this.querySelector('i').className = direction === 'asc' ? 'fas fa-sort-up' : 'fas fa-sort-down';
        
        sortTable(column, direction);
      });
    });
    
    // Pagination
    prevPage.addEventListener('click', function() {
      if (state.currentPage > 1) {
        state.currentPage--;
        updateTable();
      }
    });
    
    nextPage.addEventListener('click', function() {
      const maxPage = Math.ceil(state.filteredRows.length / state.pageSize);
      if (state.currentPage < maxPage) {
        state.currentPage++;
        updateTable();
      }
    });
    
    pageSizeSelector.addEventListener('change', function() {
      state.pageSize = parseInt(this.value);
      state.currentPage = 1;
      updateTable();
    });
    
    // Initialize table with sorting
    document.querySelector('[data-sort="project"]').click();
  });
</script> 