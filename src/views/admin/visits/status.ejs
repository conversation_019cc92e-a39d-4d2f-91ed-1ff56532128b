<%- contentFor('body') %>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">更改訪廠狀態</h1>
    <a href="/admin/visits" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
      <i class="fas fa-arrow-left mr-2"></i> 返回列表
    </a>
  </div>

  <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
    <div class="mb-6">
      <h2 class="text-2xl font-semibold mb-4">訪廠紀錄資訊</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <p class="text-gray-600 text-sm">統一編號</p>
          <p class="text-lg font-medium"><%= visit.businessId %></p>
        </div>
        <div>
          <p class="text-gray-600 text-sm">訪談日期與時間</p>
          <p class="text-lg font-medium"><%= new Date(visit.visitDate).toLocaleDateString('zh-TW') %> <%= visit.visitTime %></p>
        </div>
        <div>
          <p class="text-gray-600 text-sm">計畫名稱</p>
          <p class="text-lg font-medium"><%= visit.project ? visit.project.name_zh : '未設定' %></p>
        </div>
        <div>
          <p class="text-gray-600 text-sm">目前狀態</p>
          <p class="text-lg">
            <span class="px-2 py-1 rounded text-sm <%= visit.status === '待安排' ? 'bg-yellow-200 text-yellow-800' : visit.status === '已安排' ? 'bg-blue-200 text-blue-800' : 'bg-green-200 text-green-800' %>">
              <%= visit.status %>
            </span>
          </p>
        </div>
      </div>
    </div>

    <form action="/admin/visits/<%= visit.id %>/status" method="POST">
      <div class="mb-6">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
          更改狀態為 <span class="text-red-500">*</span>
        </label>
        <select
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="status" name="status" required>
          <option value="待安排" <%= visit.status === '待安排' ? 'selected' : '' %>>待安排</option>
          <option value="已安排" <%= visit.status === '已安排' ? 'selected' : '' %>>已安排</option>
          <option value="完成拜訪" <%= visit.status === '完成拜訪' ? 'selected' : '' %>>完成拜訪</option>
        </select>
      </div>

      <div class="mb-6">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="mouStatus">
          MOU用印狀態
        </label>
        <select
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="mouStatus" name="mouStatus">
          <option value="0" <%= visit.mouStatus === 0 ? 'selected' : '' %>>未用印</option>
          <option value="1" <%= visit.mouStatus === 1 ? 'selected' : '' %>>已用印</option>
        </select>
      </div>

      <div class="mb-6">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="statusNotes">
          狀態備註
        </label>
        <textarea
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="statusNotes" name="statusNotes" rows="4" placeholder="請輸入狀態更改備註（可選）"></textarea>
      </div>

      <div class="flex items-center justify-end">
        <button
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          type="submit">
          更新狀態
        </button>
      </div>
    </form>
  </div>
</div> 