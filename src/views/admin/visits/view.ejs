<%- contentFor('body') %>

<!-- Quill CSS for rendering content properly -->
<link href="/css/<EMAIL>" rel="stylesheet">
<script src="/js/<EMAIL>"></script>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">查看訪廠紀錄</h1>
    <div class="flex space-x-2">
      <a href="/admin/visits" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
        <i class="fas fa-arrow-left mr-2"></i> 返回列表
      </a>
      <% if (user.role !== 'visitor' && (user.role === 'super_admin' || user.role === 'admin' || (user.role === 'editor' && visit.createdById === user.id) || visit.createdById === user.id)) { %>
        <a href="/admin/visits/edit/<%= visit.id %>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          <i class="fas fa-edit mr-2"></i> 編輯
        </a>
      <% } %>
    </div>
  </div>

  <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
    <div class="mb-6 border-b pb-4">
      <h2 class="text-2xl font-semibold mb-4">基本資訊</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <p class="text-gray-600 text-sm">廠商名稱</p>
          <p class="text-lg font-medium"><%= visit.companyName || '未設定' %></p>
        </div>
        <div>
          <p class="text-gray-600 text-sm">統一編號</p>
          <p class="text-lg font-medium"><%= visit.businessId %></p>
        </div>
        <div>
          <p class="text-gray-600 text-sm">計畫名稱</p>
          <p class="text-lg font-medium"><%= visit.project ? visit.project.name_zh : '未設定' %></p>
        </div>
        <div>
          <p class="text-gray-600 text-sm">狀態</p>
          <p class="text-lg">
            <span class="px-2 py-1 rounded text-sm <%= visit.status === '待安排' ? 'bg-yellow-200 text-yellow-800' : visit.status === '已安排' ? 'bg-blue-200 text-blue-800' : 'bg-green-200 text-green-800' %>">
              <%= visit.status %>
            </span>
          </p>
        </div>
        <div>
          <p class="text-gray-600 text-sm">MOU用印狀態</p>
          <p class="text-lg">
            <span class="px-2 py-1 rounded text-sm <%= visit.mouStatus === 0 ? 'bg-red-200 text-red-800' : 'bg-green-200 text-green-800' %>">
              <%= visit.mouStatus === 0 ? '未用印' : '已用印' %>
            </span>
          </p>
        </div>
        <div>
          <p class="text-gray-600 text-sm">訪談日期與時間</p>
          <p class="text-lg font-medium"><%= new Date(visit.visitDate).toLocaleDateString('zh-TW') %> <%= visit.visitTime %></p>
        </div>
      </div>
    </div>

    <div class="mb-6 border-b pb-4">
      <h2 class="text-2xl font-semibold mb-4">公司資訊</h2>
      <div class="mb-4">
        <p class="text-gray-600 text-sm">公司地址</p>
        <p class="text-lg font-medium"><%= visit.address %></p>
      </div>
    </div>

    <div class="mb-6 border-b pb-4">
      <h2 class="text-2xl font-semibold mb-4">出席人員</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <p class="text-gray-600 text-sm">訪談對象</p>
          <p class="text-lg font-medium"><%= visit.contactPerson || '未提供' %></p>
        </div>
        <div>
          <p class="text-gray-600 text-sm">本案負責人</p>
          <p class="text-lg font-medium"><%= visit.projectManager || '未提供' %></p>
        </div>
        <div>
          <p class="text-gray-600 text-sm">負責人電話</p>
          <p class="text-lg font-medium"><%= visit.projectManagerPhone || '未提供' %></p>
        </div>
      </div>
    </div>

    <div class="mb-6">
      <h2 class="text-2xl font-semibold mb-4">訪談記錄</h2>
      <div class="prose max-w-none border p-4 rounded bg-gray-50">
        <div class="ql-container ql-snow" style="border: none;">
          <div class="ql-editor quill-content-wrapper" id="quill-content-<%= visit.id %>">
            <%- visit.notes %>
          </div>
        </div>
      </div>
    </div>

    <div class="mt-8 text-sm text-gray-500">
      <p>創建時間: <%= new Date(visit.createdAt).toLocaleString('zh-TW') %></p>
      <% if (visit.updatedAt && visit.updatedAt.getTime() !== visit.createdAt.getTime()) { %>
        <p>最後更新: <%= new Date(visit.updatedAt).toLocaleString('zh-TW') %></p>
      <% } %>
    </div>
  </div>
</div>

<!-- CSS for Quill Content -->
<style>
  /* Quill Content Styles */
  .ql-editor {
    padding: 0;
  }

  .quill-content-wrapper h1 {
    font-size: 2em;
    margin-bottom: 0.5em;
    font-weight: bold;
  }

  .quill-content-wrapper h2 {
    font-size: 1.5em;
    margin-bottom: 0.5em;
    font-weight: bold;
  }

  .quill-content-wrapper h3 {
    font-size: 1.3em;
    margin-bottom: 0.5em;
    font-weight: bold;
  }

  .quill-content-wrapper p {
    margin-bottom: 1em;
  }

  .quill-content-wrapper ul, 
  .quill-content-wrapper ol {
    padding-left: 2em;
    margin-bottom: 1em;
  }

  .quill-content-wrapper ul {
    list-style-type: disc;
  }

  .quill-content-wrapper ol {
    list-style-type: decimal;
  }

  .quill-content-wrapper blockquote {
    border-left: 4px solid #ccc;
    padding-left: 16px;
    margin-bottom: 1em;
  }

  .quill-content-wrapper img {
    max-width: 100%;
    height: auto;
  }

  .quill-content-wrapper .ql-align-center {
    text-align: center;
  }

  .quill-content-wrapper .ql-align-right {
    text-align: right;
  }

  .quill-content-wrapper .ql-align-justify {
    text-align: justify;
  }
</style> 