<%- contentFor('body') %>

<div class="container mx-auto px-4 py-8">
  <div class="flex justify-between items-center mb-6">
    <h1 class="text-3xl font-bold">編輯訪廠紀錄</h1>
    <a href="/admin/visits" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
      <i class="fas fa-arrow-left mr-2"></i> 返回列表
    </a>
  </div>

  <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
    <form action="/admin/visits/<%= visit.id %>" method="POST" id="visitForm" enctype="application/x-www-form-urlencoded">
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="companyName">
          廠商名稱 <span class="text-red-500">*</span>
        </label>
        <input
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="companyName" name="companyName" type="text" placeholder="請輸入廠商名稱" 
          value="<%= visit.companyName || (locals.companyInfo && companyInfo.companyName ? companyInfo.companyName : '') %>" required>
      </div>
      
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="contactPerson">
          訪談對象 <span class="text-red-500">*</span>
        </label>
        <input
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="contactPerson" name="contactPerson" type="text" placeholder="請輸入訪談對象" 
          value="<%= visit.contactPerson || '' %>" required>
      </div>
      
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="businessId">
          統一編號 <span class="text-red-500">*</span>
        </label>
        <input
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="businessId" name="businessId" type="text" placeholder="請輸入統一編號" value="<%= visit.businessId %>" required>
      </div>

      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="address">
          公司地址 <span class="text-red-500">*</span>
        </label>
        <input
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="address" name="address" type="text" placeholder="請輸入公司地址" value="<%= visit.address %>" required>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label class="block text-gray-700 text-sm font-bold mb-2" for="visitDate">
            訪談日期 <span class="text-red-500">*</span>
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="visitDate" name="visitDate" type="date" value="<%= visit.visitDate ? visit.visitDate.toISOString().split('T')[0] : '' %>" required>
        </div>
        <div>
          <label class="block text-gray-700 text-sm font-bold mb-2" for="visitTime">
            訪談時間 <span class="text-red-500">*</span>
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="visitTime" name="visitTime" type="time" value="<%= visit.visitTime %>" required>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label class="block text-gray-700 text-sm font-bold mb-2" for="projectManager">
            本案負責人 <span class="text-red-500">*</span>
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="projectManager" name="projectManager" type="text" placeholder="請輸入本案負責人姓名" 
            value="<%= visit.projectManager || '' %>" required>
        </div>
        <div>
          <label class="block text-gray-700 text-sm font-bold mb-2" for="projectManagerPhone">
            負責人電話 <span class="text-red-500">*</span>
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="projectManagerPhone" name="projectManagerPhone" type="text" placeholder="請輸入負責人電話" 
            value="<%= visit.projectManagerPhone || '' %>" required>
        </div>
      </div>

      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="projectId">
          分項計畫名稱
        </label>
        <% if (user.role === 'guest' && user.projectId) { %>
          <input type="hidden" name="projectId" value="<%= user.projectId %>">
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline bg-gray-100"
            type="text" value="<%= locals.projectName || (visit.project ? visit.project.name_zh : '') %>" readonly>
        <% } else { %>
          <select
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="projectId" name="projectId">
            <option value="">-- 選擇計畫 --</option>
            <% if (typeof projects !== 'undefined' && projects.length > 0) { %>
              <% projects.forEach(project => { %>
                <option value="<%= project.id %>" <%= visit.projectId === project.id ? 'selected' : '' %>><%= project.name_zh %></option>
              <% }); %>
            <% } else { %>
              <option value="" disabled selected>目前無計劃 通知管理員新增</option>
            <% } %>
          </select>
        <% } %>
      </div>

      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="status">
          狀態
        </label>
        <select
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="status" name="status">
          <option value="待安排" <%= visit.status === '待安排' ? 'selected' : '' %>>待安排</option>
          <option value="已安排" <%= visit.status === '已安排' ? 'selected' : '' %>>已安排</option>
          <option value="完成拜訪" <%= visit.status === '完成拜訪' ? 'selected' : '' %>>完成拜訪</option>
        </select>
        <input type="hidden" name="direct_status" value="已安排">
      </div>
      
      <div class="mb-4">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="mouStatus">
          MOU用印狀態
        </label>
        <select
          class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
          id="mouStatus" name="mouStatus">
          <option value="0" <%= (!visit.mouStatus || visit.mouStatus === 0) ? 'selected' : '' %>>未用印</option>
          <option value="1" <%= visit.mouStatus === 1 ? 'selected' : '' %>>已用印</option>
        </select>
        <input type="hidden" name="direct_mouStatus" value="1">
      </div>

      <div class="mb-6">
        <label class="block text-gray-700 text-sm font-bold mb-2" for="editor">
          訪談記錄 <span class="text-red-500">*</span>
        </label>
        <div id="editor" class="h-64 mt-1">
          <%- visit.notes %>
        </div>
        <input type="hidden" name="notes" id="notes">
        <p class="text-gray-600 text-xs italic mt-1">詳細紀錄訪談內容，可使用工具列格式化文字</p>
      </div>

      <div class="flex items-center justify-between">
        <!-- <a 
          href="/admin/visits/<%= visit.id %>/update-status/已安排/已簽約" 
          class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          onclick="return confirm('確定要設定狀態為「已安排」和簽約狀態為「已簽約」嗎？')">
          設定狀態為「已安排」和「已簽約」
        </a> -->
        
        <button
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
          type="submit">
          儲存變更
        </button>
      </div>
    </form>
  </div>
</div>

<script>
  (function() {
    // Initialize Quill editor
    var quill = new Quill('#editor', {
      theme: 'snow',
      modules: {
        toolbar: [
          [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
          ['bold', 'italic', 'underline', 'strike'],
          [{ 'color': [] }, { 'background': [] }],
          [{ 'align': [] }],
          [{ 'list': 'ordered' }, { 'list': 'bullet' }],
          ['link'],
          ['clean']
        ]
      },
      placeholder: '撰寫訪談紀錄...'
    });
    
    // Handle form submission
    document.getElementById('visitForm').onsubmit = function() {
      // Get editor content and set to hidden input
      var notes = quill.root.innerHTML;
      document.getElementById('notes').value = notes;
      
      // Convert mouStatus to integer
      var mouStatusSelect = document.getElementById('mouStatus');
      mouStatusSelect.value = parseInt(mouStatusSelect.value);
      
      // Log form values for debugging
      console.log('Status value:', document.getElementById('status').value);
      console.log('MOU Status value:', document.getElementById('mouStatus').value);
      
      // Basic validation
      if (notes === '<p><br></p>' || notes === '') {
        alert('請輸入訪談記錄內容');
        return false;
      }
      
      return true;
    };
  })();
</script>

<style>
  /* Custom styles for Quill editor */
  #editor {
    height: 300px;
    margin-bottom: 30px;
  }

  /* Fix toolbar positioning */
  .ql-toolbar.ql-snow {
    border: 1px solid #ccc;
    box-sizing: border-box;
    font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
    padding: 8px;
  }

  .ql-container.ql-snow {
    border: 1px solid #ccc;
    border-top: 0px;
  }

  /* Ensure editor has proper height */
  .ql-editor {
    min-height: 200px;
  }
</style> 