<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold mb-8">歡迎， <%= user.contactName_zh ? user.contactName_zh : user.username %></h1>
        
        <% if (user.role !== 'guest' && user.role !== 'editor') { %>
        <h2 class="text-xl font-bold mb-8">
            瀏覽人數: 今日 <%= stats.visitors %> / 總計 <%= stats.totalVisitors %>
        </h2>

        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-8">

            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-gray-500 text-sm font-medium">使用者總數</h3>
                <p class="text-3xl font-bold text-gray-900">
                    <%= stats.users %>
                </p>
                <% if (user.role === 'super_admin' || user.role === 'admin') { %>
                    <a href="/admin/users" class="mt-2 inline-block text-blue-600 hover:text-blue-800">管理使用者 →</a>
                <% } %>
            </div>
        </div>
        <% } %>
        
        <% if (user.role === 'guest') { %>
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <div class="flex items-center">
                <div class="bg-blue-500 rounded-full p-3 mr-4">
                    <i class="fas fa-info-circle text-white text-xl"></i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900">訪客功能引導</h3>
                    <p class="text-gray-600">您可以在此系統中使用訪廠資訊功能。</p>
                </div>
            </div>
            <div class="mt-4">
                <a href="/admin/visits" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-building mr-2"></i> 前往訪廠資訊
                </a>
            </div>
        </div>
        <% } %>
        
        <% if (user.role === 'editor') { %>
        <div class="bg-white rounded-lg shadow p-6 mb-8">
            <div class="flex items-center">
                <div class="bg-blue-500 rounded-full p-3 mr-4">
                    <i class="fas fa-user-edit text-white text-xl"></i>
                </div>
                <div>
                    <h3 class="text-xl font-bold text-gray-900">編輯功能引導</h3>
                    <p class="text-gray-600">您可以在此系統中編輯您的個人資料。</p>
                </div>
            </div>
            <div class="mt-4">
                <a href="/admin/users/edit/<%= user.id %>" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                    <i class="fas fa-user-edit mr-2"></i> 編輯個人資料
                </a>
            </div>
        </div>
        <% } %>
    </div>