<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">關於我們頁面項目</h1>
      <a href="/admin/about/create" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
        <i class="fas fa-plus mr-2"></i> 新增項目
      </a>
    </div>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <% if (items && items.length> 0) { %>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white" id="dataTable">
            <thead>
              <tr class="bg-gray-100 text-gray-700 uppercase text-sm leading-normal">
                <th class="py-3 px-6 text-left whitespace-nowrap">順序</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">標題 (中)</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">標題 (英)</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">類型</th>
                <th class="py-3 px-6 text-left whitespace-nowrap">建立日期</th>
                <th class="py-3 px-6 text-center whitespace-nowrap">操作</th>
              </tr>
            </thead>
            <tbody class="text-gray-600 text-sm">
              <% items.forEach(function(item) { %>
                <tr class="border-b border-gray-200 hover:bg-gray-50">
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= item.order %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= item.title_tw %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= item.title_en %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <% if (item.type==='plain_text' ) { %>
                      <span class="bg-blue-200 text-blue-800 py-1 px-3 rounded-full text-xs">編輯器</span>
                      <% } else if (item.type==='image' ) { %>
                        <span class="bg-green-200 text-green-800 py-1 px-3 rounded-full text-xs">圖片</span>
                        <% } else if (item.type==='bullet_points' ) { %>
                          <span class="bg-yellow-200 text-yellow-800 py-1 px-3 rounded-full text-xs">項目符號</span>
                          <% } %>
                  </td>
                  <td class="py-3 px-6 text-left whitespace-nowrap">
                    <%= new Date(item.createdAt).toLocaleDateString() %>
                  </td>
                  <td class="py-3 px-6 text-center whitespace-nowrap">
                    <div class="flex item-center justify-center">
                      <a href="/admin/about/edit/<%= item.id %>"
                        class="text-blue-600 hover:text-blue-800 mr-4">
                        <i class="fas fa-edit"></i>
                      </a>
                      <button class="text-red-600 hover:text-red-800"
                        onclick="document.getElementById('deleteModal<%= item.id %>').classList.remove('hidden')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>

                <!-- Delete Modal -->
                <div id="deleteModal<%= item.id %>"
                  class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
                  <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
                    <div class="mt-3 text-center">
                      <h3 class="text-lg leading-6 font-medium text-gray-900">確認刪除</h3>
                      <div class="mt-2 px-7 py-3">
                        <p class="text-sm text-gray-500">
                          您確定要刪除項目「<%= item.title_en %>」嗎？
                        </p>
                      </div>
                      <div class="flex justify-center mt-4 px-4 py-3">
                        <button type="button"
                          class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded mr-2"
                          onclick="document.getElementById('deleteModal<%= item.id %>').classList.add('hidden')">
                          取消
                        </button>
                        <form action="/admin/about/<%= item.id %>/delete" method="POST">
                          <button type="submit"
                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                            刪除
                          </button>
                        </form>
                      </div>
                    </div>
                  </div>
                </div>
                <% }); %>
            </tbody>
          </table>
        </div>
        <% } else { %>
          <div class="text-center py-8">
            <p class="text-gray-500 mb-4">尚未找到任何關於我們頁面項目。</p>
            <a href="/admin/about/create" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
              <i class="fas fa-plus mr-2"></i> 新增第一個項目
            </a>
          </div>
          <% } %>
    </div>
  </div>

  <%- contentFor('script') %>
    <script>
      $(document).ready(function () {
        $('#dataTable').DataTable({
          "order": [[0, "asc"]]
        });
      });
    </script>