<%- contentFor('body') %>

  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">編輯關於頁面項目</h1>
      <a href="/admin/about" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
        <i class="fas fa-arrow-left mr-2"></i> 返回列表
      </a>
    </div>

    <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
      <form action="/admin/about/<%= item.id %>" method="POST" enctype="multipart/form-data" id="aboutForm">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-gray-700 text-sm font-bold mb-2" for="title_tw">
              標題（中文） <span class="text-red-500">*</span>
            </label>
            <input
              class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="title_tw" name="title_tw" type="text" value="<%= item.title_tw %>" required>
          </div>
          <div>
            <label class="block text-gray-700 text-sm font-bold mb-2" for="title_en">
              標題（英文） <span class="text-red-500">*</span>
            </label>
            <input
              class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
              id="title_en" name="title_en" type="text" value="<%= item.title_en %>" required>
          </div>
        </div>

        <div class="mb-4">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="type">
            內容類型 <span class="text-red-500">*</span>
          </label>
          <select
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="type" name="type" required>
            <option value="">選擇類型</option>
            <option value="plain_text" <%=item.type==='plain_text' ? 'selected' : '' %>>編輯器</option>
            <option value="image" <%=item.type==='image' ? 'selected' : '' %>>圖片 / Image</option>
            <!-- <option value="bullet_points" <%=item.type==='bullet_points' ? 'selected' : '' %>>項目符號</option> -->
          </select>
          <p class="text-gray-600 text-xs italic mt-1">
            編輯器：簡單文字內容<br>
            圖片：上傳圖片，可選擇添加說明<br>
            <!-- 項目符號：項目列表（每行一個） -->
          </p>
        </div>

        <div class="mb-4" id="imageUploadGroup">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="image">
            圖片
          </label>

          <!-- Chinese Images Section -->
          <div class="mb-6">
            <h3 class="text-xl font-semibold mb-4 border-b pb-2">中文版圖片</h3>

            <!-- Desktop Image -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="imageDesktop">
                桌面版圖片
              </label>
              <div class="border-2 border-gray-300 border-dashed rounded-lg p-6 bg-gray-50">
                <div class="flex flex-col items-center justify-center mb-4">
                  <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 20 16">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                  </svg>
                  <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">點擊選擇中文桌面版圖片</span></p>
                  <p class="text-xs text-gray-500 mb-4">PNG, JPG, GIF（最大 5MB）</p>
                </div>
                <input
                  class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none"
                  id="imageDesktop" name="imageDesktop" type="file" accept="image/*">
              </div>
              <p class="text-gray-600 text-xs italic mt-1">建議尺寸：1920x1080像素。最大文件大小：5MB。</p>

                              <% if (item.imagePathDesktop) { %>
                <div class="mt-4">
                  <div class="max-w-sm rounded overflow-hidden shadow-lg">
                    <img src="<%= item.imagePathDesktop %>" alt="當前中文桌面版圖片" class="w-full">
                    <div class="px-6 py-4 bg-gray-100">
                      <div class="font-bold text-xl mb-2">當前中文桌面版圖片</div>
                      <p class="text-gray-600 text-sm">上傳新圖片將替換當前圖片。</p>
                    </div>
                  </div>
                  <input type="hidden" id="existingImageDesktop" name="existingImageDesktop"
                    value="<%= item.imagePathDesktop %>">
                </div>
                <% } %>
                
            </div>

            <!-- Tablet Image -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="imageTablet">
                平板版圖片
              </label>
              <div class="border-2 border-gray-300 border-dashed rounded-lg p-6 bg-gray-50">
                <div class="flex flex-col items-center justify-center mb-4">
                  <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 20 16">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                  </svg>
                  <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">點擊選擇中文平板版圖片</span></p>
                  <p class="text-xs text-gray-500 mb-4">PNG, JPG, GIF（最大 5MB）</p>
                </div>
                <input
                  class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none"
                  id="imageTablet" name="imageTablet" type="file" accept="image/*">
              </div>
              <p class="text-gray-600 text-xs italic mt-1">建議尺寸：768x1024像素。最大文件大小：5MB。</p>

              <% if (item.imagePathTablet) { %>
                <div class="mt-4">
                  <div class="max-w-sm rounded overflow-hidden shadow-lg">
                    <img src="<%= item.imagePathTablet %>" alt="當前中文平板版圖片" class="w-full">
                    <div class="px-6 py-4 bg-gray-100">
                      <div class="font-bold text-xl mb-2">當前中文平板版圖片</div>
                      <p class="text-gray-600 text-sm">上傳新圖片將替換當前圖片。</p>
                    </div>
                  </div>
                  <input type="hidden" id="existingImageTablet" name="existingImageTablet"
                    value="<%= item.imagePathTablet %>">
                </div>
                <% } %>
            </div>

            <!-- Mobile Image -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="imageMobile">
                手機版圖片
              </label>
              <div class="border-2 border-gray-300 border-dashed rounded-lg p-6 bg-gray-50">
                <div class="flex flex-col items-center justify-center mb-4">
                  <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 20 16">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                  </svg>
                  <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">點擊選擇中文手機版圖片</span></p>
                  <p class="text-xs text-gray-500 mb-4">PNG, JPG, GIF（最大 5MB）</p>
                </div>
                <input
                  class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none"
                  id="imageMobile" name="imageMobile" type="file" accept="image/*">
              </div>
              <p class="text-gray-600 text-xs italic mt-1">建議尺寸：375x667像素。最大文件大小：5MB。</p>

              <% if (item.imagePathMobile) { %>
                <div class="mt-4">
                  <div class="max-w-sm rounded overflow-hidden shadow-lg">
                    <img src="<%= item.imagePathMobile %>" alt="當前中文手機版圖片" class="w-full">
                    <div class="px-6 py-4 bg-gray-100">
                      <div class="font-bold text-xl mb-2">當前中文手機版圖片</div>
                      <p class="text-gray-600 text-sm">上傳新圖片將替換當前圖片。</p>
                    </div>
                  </div>
                  <input type="hidden" id="existingImageMobile" name="existingImageMobile"
                    value="<%= item.imagePathMobile %>">
                </div>
                <% } %>
            </div>

            <!-- Chinese Image Alt Text -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="alt_tw">
                中文圖片替代文字 (Alt Text)
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="alt_tw" name="alt_tw" type="text" value="<%= item.alt_tw || '' %>" placeholder="輸入圖片替代文字，提高無障礙性">
              <p class="text-gray-600 text-xs italic mt-1">為視障人士描述圖片內容，提高無障礙性。適用於所有中文版圖片。</p>
            </div>
          </div>

          <!-- English Images Section -->
          <div class="mb-4">
            <h3 class="text-xl font-semibold mb-4 border-b pb-2">英文版圖片</h3>

            <!-- Desktop Image English -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="imageDesktopEn">
                英文桌面版圖片
              </label>
              <div class="border-2 border-gray-300 border-dashed rounded-lg p-6 bg-gray-50">
                <div class="flex flex-col items-center justify-center mb-4">
                  <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 20 16">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                  </svg>
                  <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">點擊選擇英文桌面版圖片</span></p>
                  <p class="text-xs text-gray-500 mb-4">PNG, JPG, GIF（最大 5MB）</p>
                </div>
                <input
                  class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none"
                  id="imageDesktopEn" name="imageDesktopEn" type="file" accept="image/*">
              </div>
              <p class="text-gray-600 text-xs italic mt-1">建議尺寸：1920x1080像素。最大文件大小：5MB。</p>

                              <% if (item.imagePathDesktopEn) { %>
                <div class="mt-4">
                  <div class="max-w-sm rounded overflow-hidden shadow-lg">
                    <img src="<%= item.imagePathDesktopEn %>" alt="當前英文桌面版圖片" class="w-full">
                    <div class="px-6 py-4 bg-gray-100">
                      <div class="font-bold text-xl mb-2">當前英文桌面版圖片</div>
                      <p class="text-gray-600 text-sm">上傳新圖片將替換當前圖片。</p>
                    </div>
                  </div>
                  <input type="hidden" id="existingImageDesktopEn" name="existingImageDesktopEn"
                    value="<%= item.imagePathDesktopEn %>">
                </div>
                <% } %>
                
            </div>

            <!-- Tablet Image English -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="imageTabletEn">
                英文平板版圖片
              </label>
              <div class="border-2 border-gray-300 border-dashed rounded-lg p-6 bg-gray-50">
                <div class="flex flex-col items-center justify-center mb-4">
                  <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 20 16">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                  </svg>
                  <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">點擊選擇英文平板版圖片</span></p>
                  <p class="text-xs text-gray-500 mb-4">PNG, JPG, GIF（最大 5MB）</p>
                </div>
                <input
                  class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none"
                  id="imageTabletEn" name="imageTabletEn" type="file" accept="image/*">
              </div>
              <p class="text-gray-600 text-xs italic mt-1">建議尺寸：768x1024像素。最大文件大小：5MB。</p>

              <% if (item.imagePathTabletEn) { %>
                <div class="mt-4">
                  <div class="max-w-sm rounded overflow-hidden shadow-lg">
                    <img src="<%= item.imagePathTabletEn %>" alt="當前英文平板版圖片" class="w-full">
                    <div class="px-6 py-4 bg-gray-100">
                      <div class="font-bold text-xl mb-2">當前英文平板版圖片</div>
                      <p class="text-gray-600 text-sm">上傳新圖片將替換當前圖片。</p>
                    </div>
                  </div>
                  <input type="hidden" id="existingImageTabletEn" name="existingImageTabletEn"
                    value="<%= item.imagePathTabletEn %>">
                </div>
                <% } %>
            </div>

            <!-- Mobile Image English -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="imageMobileEn">
                英文手機版圖片
              </label>
              <div class="border-2 border-gray-300 border-dashed rounded-lg p-6 bg-gray-50">
                <div class="flex flex-col items-center justify-center mb-4">
                  <svg class="w-8 h-8 mb-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 20 16">
                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2" />
                  </svg>
                  <p class="mb-2 text-sm text-gray-500"><span class="font-semibold">點擊選擇英文手機版圖片</span></p>
                  <p class="text-xs text-gray-500 mb-4">PNG, JPG, GIF（最大 5MB）</p>
                </div>
                <input
                  class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 focus:outline-none"
                  id="imageMobileEn" name="imageMobileEn" type="file" accept="image/*">
              </div>
              <p class="text-gray-600 text-xs italic mt-1">建議尺寸：375x667像素。最大文件大小：5MB。</p>

              <% if (item.imagePathMobileEn) { %>
                <div class="mt-4">
                  <div class="max-w-sm rounded overflow-hidden shadow-lg">
                    <img src="<%= item.imagePathMobileEn %>" alt="當前英文手機版圖片" class="w-full">
                    <div class="px-6 py-4 bg-gray-100">
                      <div class="font-bold text-xl mb-2">當前英文手機版圖片</div>
                      <p class="text-gray-600 text-sm">上傳新圖片將替換當前圖片。</p>
                    </div>
                  </div>
                  <input type="hidden" id="existingImageMobileEn" name="existingImageMobileEn"
                    value="<%= item.imagePathMobileEn %>">
                </div>
                <% } %>
            </div>

            <!-- English Image Alt Text -->
            <div class="mb-4">
              <label class="block text-gray-700 text-sm font-bold mb-2" for="alt_en">
                英文圖片替代文字 (Alt Text)
              </label>
              <input
                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                id="alt_en" name="alt_en" type="text" value="<%= item.alt_en || '' %>" placeholder="Enter image alternative text for accessibility">
              <p class="text-gray-600 text-xs italic mt-1">Describe image content for visually impaired users to improve accessibility. Applies to all English version images.</p>
            </div>
          </div>

          <!-- Legacy image support -->
          <% if (item.imagePath && (!item.imagePathDesktop && !item.imagePathTablet && !item.imagePathMobile)) { %>
            <div class="mt-4">
              <div class="max-w-sm rounded overflow-hidden shadow-lg">
                <img src="<%= item.imagePath %>" alt="當前圖片" class="w-full">
                <div class="px-6 py-4 bg-gray-100">
                  <div class="font-bold text-xl mb-2">當前圖片（舊版）</div>
                  <p class="text-gray-600 text-sm">這是舊版圖片。上傳新的桌面版、平板版或手機版圖片將替換此圖片。</p>
                </div>
              </div>
              <input type="hidden" id="existingImage" name="existingImage" value="<%= item.imagePath %>">
            </div>
            <% } %>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label class="block text-gray-700 text-sm font-bold mb-2" for="editor_tw">
              內容（中文） <span class="text-red-500">*</span>
            </label>
            <div id="editor_tw" class="h-64 mt-1"></div>
            <input type="hidden" name="content_tw" id="content_tw" value="<%= item.content_tw %>">
            <p class="text-gray-600 text-xs italic mt-1">對於項目符號，使用工具列中的項目符號按鈕。</p>
          </div>
          <div>
            <label class="block text-gray-700 text-sm font-bold mb-2" for="editor_en">
              內容（英文） <span class="text-red-500">*</span>
            </label>
            <div id="editor_en" class="h-64 mt-1"></div>
            <input type="hidden" name="content_en" id="content_en" value="<%= item.content_en %>">
            <p class="text-gray-600 text-xs italic mt-1">對於項目符號，使用工具列中的項目符號按鈕。</p>
          </div>
        </div>

        <div class="mb-6">
          <label class="block text-gray-700 text-sm font-bold mb-2" for="order">
            顯示順序
          </label>
          <input
            class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
            id="order" name="order" type="number" value="<%= item.order %>" min="0">
          <p class="text-gray-600 text-xs italic mt-1">項目按升序顯示（0, 1, 2, ...）。</p>
        </div>

        <div class="flex items-center justify-end">
          <button
            class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            type="submit">
            更新項目
          </button>
        </div>
      </form>
    </div>
  </div>

  <script>
    (function () {
      
          // Clear any existing content
          $('#editor_en').html('');
          $('#editor_tw').html('');

          // Initialize English editor with simple configuration
          var quillEn = new Quill('#editor_en', {
            theme: 'snow',
            modules: {
              toolbar: [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'align': [] }],
                [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                ['link', 'image', 'video'],
                ['clean'],
                ['code-block']
              ]
            },
            placeholder: 'Enter the content in English...'
          });

          // Initialize Traditional Chinese editor with simple configuration
          var quillTw = new Quill('#editor_tw', {
            theme: 'snow',
            modules: {
              toolbar: [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'align': [] }],
                [{ 'list': 'ordered' }, { 'list': 'bullet' }],
                ['link', 'image', 'video'],
                ['clean'],
                ['code-block']
              ]
            },
            placeholder: '請輸入中文內容...'
          });

          // Set initial content based on item type
          if ('<%= item.type %>' === 'bullet_points') {
            try {
              // For bullet points, we need to parse the content if it's stored as a string
              let contentEn, contentTw;

              // Get the content from hidden inputs that were populated by the server
              const contentEnRaw = $('#content_en').val();
              const contentTwRaw = $('#content_tw').val();

              try {
                contentEn = JSON.parse(contentEnRaw);
                contentTw = JSON.parse(contentTwRaw);

                // Set the contents to the editors
                quillEn.setContents(contentEn);
                quillTw.setContents(contentTw);
              } catch (parseError) {
                console.error('Error parsing bullet points:', parseError);
                // If parsing fails, set empty content
                quillEn.setText('');
                quillTw.setText('');
              }
            } catch (e) {
              console.error('Error setting bullet points:', e);
              quillEn.setText('');
              quillTw.setText('');
            }
          } else {
            // For plain text, set the HTML content directly
            try {
              // Get content from hidden inputs
              const htmlEn = $('#content_en').val();
              const htmlTw = $('#content_tw').val();

              // Set the HTML content
              quillEn.root.innerHTML = htmlEn || '';
              quillTw.root.innerHTML = htmlTw || '';
            } catch (e) {
              console.error('Error setting HTML content:', e);
              quillEn.setText('');
              quillTw.setText('');
            }
          }

          // Show/hide image upload based on type
          function toggleContentFields() {
            const type = $('#type').val();

            if (type === 'image') {
              $('#imageUploadGroup').show();
              // Hide content editors for image type
              $('#editor_en').closest('.grid').hide();
              // Remove required indicator from content fields
              $('label[for="editor_en"] .text-red-500').hide();
              $('label[for="editor_tw"] .text-red-500').hide();
              // Hide help text about bullet points
              $('.text-gray-600.text-xs.italic.mt-1').hide();
            } else {
              $('#imageUploadGroup').hide();
              // Show content editors for non-image types
              $('#editor_en').closest('.grid').show();
              // Show required indicator for content fields
              $('label[for="editor_en"] .text-red-500').show();
              $('label[for="editor_tw"] .text-red-500').show();
              // Show help text about bullet points
              $('.text-gray-600.text-xs.italic.mt-1').show();
            }
          }

          // Initial toggle
          toggleContentFields();

          // Toggle on type change
          $('#type').change(function () {
            toggleContentFields();
          });

          // Function to truncate content if needed
          function truncateContent(content, maxLength) {
            // Use environment variable MAX_UPLOAD_SIZE or default to 100MB
            const MAX_LONGTEXT_LENGTH = <%= parseInt(process.env.MAX_UPLOAD_SIZE) || 100 * 1024 * 1024 %>;

            // If maxLength is not provided, use the maximum LongText length
            const limit = maxLength || MAX_LONGTEXT_LENGTH;

            if (content && content.length > limit) {
              console.warn(`Content truncated from ${content.length} to ${limit} characters`);
              return content.substring(0, limit);
            }
            return content;
          }

          // Form validation before submit
          $('#aboutForm').on('submit', function (e) {
            // If type is image and there's no existing image and no new image selected
            if ($('#type').val() === 'image') {
              const hasExistingChineseImages = $('#existingImage').length > 0 ||
                $('#existingImageDesktop').length > 0 ||
                $('#existingImageTablet').length > 0 ||
                $('#existingImageMobile').length > 0;
              
              const hasExistingEnglishImages = $('#existingImageDesktopEn').length > 0 ||
                $('#existingImageTabletEn').length > 0 ||
                $('#existingImageMobileEn').length > 0;

              const hasNewChineseImages = $('#imageDesktop').val() ||
                $('#imageTablet').val() ||
                $('#imageMobile').val();
                
              const hasNewEnglishImages = $('#imageDesktopEn').val() ||
                $('#imageTabletEn').val() ||
                $('#imageMobileEn').val();

              if (!hasExistingChineseImages && !hasExistingEnglishImages && !hasNewChineseImages && !hasNewEnglishImages) {
                e.preventDefault();
                alert('請至少選擇一個圖片文件（中文或英文版本的桌面版、平板版或手機版）。');
                return false;
              }
            }

            // Update hidden inputs with Quill content
            if ($('#type').val() === 'plain_text') {
              // For plain text, store the HTML content
              const htmlEn = quillEn.root.innerHTML;
              const htmlTw = quillTw.root.innerHTML;

              // Using LongText field now, so we can store much more content
              $('#content_en').val(truncateContent(htmlEn));
              $('#content_tw').val(truncateContent(htmlTw));
            } else if ($('#type').val() === 'bullet_points') {
              // For bullet points, store the Quill Delta as JSON
              const deltaEn = JSON.stringify(quillEn.getContents());
              const deltaTw = JSON.stringify(quillTw.getContents());

              // Using LongText field now, so we can store much more content
              $('#content_en').val(truncateContent(deltaEn));
              $('#content_tw').val(truncateContent(deltaTw));
            } else if ($('#type').val() === 'image') {
              // For image type, set empty content if not provided
              if (!$('#content_en').val()) {
                $('#content_en').val(' ');
              }
              if (!$('#content_tw').val()) {
                $('#content_tw').val(' ');
              }
            }

            return true;
          });

          // Add image handler for English editor
          const imageHandlerEn = () => {
            const input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.click();

            input.onchange = () => {
              const file = input.files[0];
              if (file) {
                const reader = new FileReader();
                reader.onload = () => {
                  const base64 = reader.result;
                  const range = quillEn.getSelection(true);
                  const altText = prompt("Enter alt text for the image (English):");
                  if (altText) {
                    const img = document.createElement('img');
                    img.src = base64;
                    img.alt = altText;
                    quillEn.clipboard.dangerouslyPasteHTML(range.index, img.outerHTML);
                  } else {
                    quillEn.insertEmbed(range.index, 'image', base64);
                  }
                };
                reader.readAsDataURL(file);
              }
            };
          };

          // Attach image handler to English editor toolbar
          quillEn.getModule('toolbar').addHandler('image', imageHandlerEn);

          // Add image handler for Traditional Chinese editor
          const imageHandlerTw = () => {
            const input = document.createElement('input');
            input.setAttribute('type', 'file');
            input.setAttribute('accept', 'image/*');
            input.click();

            input.onchange = () => {
              const file = input.files[0];
              if (file) {
                const reader = new FileReader();
                reader.onload = () => {
                  const base64 = reader.result;
                  const range = quillTw.getSelection(true);
                  const altText = prompt("輸入圖片替代文字 (中文):");
                  if (altText) {
                    const img = document.createElement('img');
                    img.src = base64;
                    img.alt = altText;
                    quillTw.clipboard.dangerouslyPasteHTML(range.index, img.outerHTML);
                  } else {
                    quillTw.insertEmbed(range.index, 'image', base64);
                  }
                };
                reader.readAsDataURL(file);
              }
            };
          };

          // Attach image handler to Traditional Chinese editor toolbar
          quillTw.getModule('toolbar').addHandler('image', imageHandlerTw);
        
    })();

    // Add a function to check content length before submission
    function checkContentLength() {
      const MAX_CONTENT_LENGTH = <%= parseInt(process.env.MAX_UPLOAD_SIZE) || 100 * 1024 * 1024 %>; // Use MAX_UPLOAD_SIZE from env or default to 100MB
      const contentEn = document.getElementById('content_en').value;
      const contentTw = document.getElementById('content_tw').value;

      let warningMessage = '';

      if (contentEn && contentEn.length > MAX_CONTENT_LENGTH) {
        warningMessage += `English content is too large (${(contentEn.length / (1024 * 1024)).toFixed(2)}MB). It will be truncated to ${(MAX_CONTENT_LENGTH / (1024 * 1024)).toFixed(0)}MB.\n`;
      }

      if (contentTw && contentTw.length > MAX_CONTENT_LENGTH) {
        warningMessage += `Traditional Chinese content is too large (${(contentTw.length / (1024 * 1024)).toFixed(2)}MB). It will be truncated to ${(MAX_CONTENT_LENGTH / (1024 * 1024)).toFixed(0)}MB.\n`;
      }

      if (warningMessage) {
        return confirm(warningMessage + 'Do you want to continue?');
      }

      return true;
    }

    // Update the form to use the check function
    document.getElementById('aboutForm').onsubmit = function () {
      return checkContentLength();
    };
  </script>

  <style>
    /* Custom styles for Quill editors */
    #editor_en,
    #editor_tw {
      height: 300px;
      margin-bottom: 30px;
    }

    /* Fix toolbar positioning */
    .ql-toolbar.ql-snow {
      border: 1px solid #ccc;
      box-sizing: border-box;
      font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
      padding: 8px;
    }

    .ql-container.ql-snow {
      border: 1px solid #ccc;
      border-top: 0px;
    }

    /* Ensure editor has proper height */
    .ql-editor {
      min-height: 200px;
    }
  </style>