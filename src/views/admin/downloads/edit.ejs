<%- contentFor('body') %>

    <div class="container px-6 mx-auto grid">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">編輯下載</h1>
            <a href="/admin/downloads" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-arrow-left mr-2"></i> 返回列表
            </a>
        </div>

        <div class="px-4 py-3 mb-8 bg-white rounded-lg shadow-md">
            <div class="mb-4 pb-2 border-b border-gray-200">
                <div class="flex items-center">
                    <i class="fas fa-edit mr-2 text-gray-600"></i>
                    <h3 class="text-lg font-medium text-gray-700">編輯下載</h3>
                </div>
            </div>
            <form action="/admin/downloads/<%= download.id %>" method="POST" enctype="multipart/form-data">
                <!-- Language Switcher -->
                <div class="mb-4">
                    <div class="flex border-b border-gray-200">
                        <button type="button"
                            class="py-2 px-4 text-center border-b-2 border-blue-500 text-blue-500 font-medium text-sm leading-5 focus:outline-none language-tab active"
                            data-language="tw">
                            中文
                        </button>
                        <button type="button"
                            class="py-2 px-4 text-center border-b-2 border-transparent text-gray-500 font-medium text-sm leading-5 hover:text-gray-700 hover:border-gray-300 focus:outline-none language-tab"
                            data-language="en">
                            英文
                        </button>
                    </div>
                </div>
                
                <!-- Traditional Chinese Content -->
                <div class="mb-4 language-content tw">
                    <label for="title_tw" class="block text-gray-700 text-sm font-bold mb-2">
                        標題 (中文) <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="title_tw" name="title_tw" value="<%= download.title_tw %>" required maxlength="100">
                    <p class="mt-1 text-xs text-gray-500">請輸入下載檔案的中文標題（建議 100 字內）</p>
                </div>

                <div class="mb-4 language-content tw">
                    <label for="description_tw" class="block text-gray-700 text-sm font-bold mb-2">描述
                        (中文)</label>
                    <textarea
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="description_tw" name="description_tw"
                        rows="3" maxlength="500"><%= download.description_tw || '' %></textarea>
                    <p class="mt-1 text-xs text-gray-500">請提供檔案的中文簡短描述（建議 500 字內）</p>
                </div>

                <div class="mb-4 language-content tw">
                    <label for="keywords_tw" class="block text-gray-700 text-sm font-bold mb-2">關鍵字
                        (中文)</label>
                    <input type="text"
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="keywords_tw" name="keywords_tw" value="<%= download.keywords_tw || '' %>">
                    <p class="mt-1 text-xs text-gray-500">請輸入以逗號分隔的中文關鍵字，以幫助搜尋（選填）</p>
                </div>

                <!-- English Content -->
                <div class="mb-4 language-content en hidden">
                    <label for="title_en" class="block text-gray-700 text-sm font-bold mb-2">
                        標題 (英文) <span class="text-red-500">*</span>
                    </label>
                    <input type="text"
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="title_en" name="title_en" value="<%= download.title_en %>" required maxlength="100">
                    <p class="mt-1 text-xs text-gray-500">請輸入下載檔案的英文標題（建議 100 字內）</p>
                </div>

                <div class="mb-4 language-content en hidden">
                    <label for="description_en" class="block text-gray-700 text-sm font-bold mb-2">描述
                        (英文)</label>
                    <textarea
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="description_en" name="description_en"
                        rows="3" maxlength="500"><%= download.description_en || '' %></textarea>
                    <p class="mt-1 text-xs text-gray-500">請提供檔案的英文簡短描述（建議 500 字內）</p>
                </div>

                <div class="mb-4 language-content en hidden">
                    <label for="keywords_en" class="block text-gray-700 text-sm font-bold mb-2">關鍵字
                        (英文)</label>
                    <input type="text"
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="keywords_en" name="keywords_en" value="<%= download.keywords_en || '' %>">
                    <p class="mt-1 text-xs text-gray-500">請輸入以逗號分隔的英文關鍵字，以幫助搜尋（選填）</p>
                </div>

                <!-- Group Name Fields -->
                <div class="mb-4 language-content tw">
                    <label for="group_name_tw" class="block text-gray-700 text-sm font-bold mb-2">群組名稱
                        (中文)</label>
                    <input type="text"
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="group_name_tw" name="group_name_tw" value="<%= download.group_name_tw || '' %>">
                    <p class="mt-1 text-xs text-gray-500">請輸入下載檔案的中文群組名稱（選填）</p>
                    <p class="mt-1 text-xs text-gray-500">若要開啟分組功能，請同時填寫中文群組名稱與英文群組名稱</p>
                </div>

                <div class="mb-4 language-content en hidden">
                    <label for="group_name_en" class="block text-gray-700 text-sm font-bold mb-2">群組名稱
                        (英文)</label>
                    <input type="text"
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="group_name_en" name="group_name_en" value="<%= download.group_name_en || '' %>">
                    <p class="mt-1 text-xs text-gray-500">請輸入下載檔案的英文群組名稱（選填）</p>
                    <p class="mt-1 text-xs text-gray-500">若要開啟分組功能，請同時填寫中文群組名稱與英文群組名稱</p>
                </div>

                <!-- Common Fields -->
                <div class="mb-4">
                    <label for="categoryId" class="block text-gray-700 text-sm font-bold mb-2">分類</label>
                    <select
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="categoryId" name="categoryId" onchange="loadGroups()">
                        <option value="">選擇分類</option>
                        <% categories.forEach(category=> { %>
                            <option value="<%= category.id %>" <%=download.categoryId===category.id ? 'selected' : '' %>>
                                <%= category.name_tw %> / <%= category.name_en %>
                            </option>
                            <% }); %>
                    </select>
                    <p class="mt-1 text-xs text-gray-500">選擇一個分類以幫助組織下載（選填）</p>
                </div>

                <div class="mb-4">
                    <label for="groupId" class="block text-gray-700 text-sm font-bold mb-2">分組</label>
                    <select
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="groupId" name="groupId">
                        <option value="">選擇分組</option>
                        <% groups.forEach(group=> { %>
                            <option value="<%= group.id %>" <%=download.groupId===group.id ? 'selected' : '' %>>
                                <%= group.name_tw %> / <%= group.name_en %>
                            </option>
                            <% }); %>
                    </select>
                    <p class="mt-1 text-xs text-gray-500">請先選擇分類，然後選擇分組（選填）</p>
                </div>

                <div class="mb-4">
                    <label for="status" class="block text-gray-700 text-sm font-bold mb-2">狀態</label>
                    <select
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="status" name="status">
                        <option value="draft" <%=download.status==='draft' ? 'selected' : '' %>>草稿</option>
                        <option value="published" <%=download.status==='published' ? 'selected' : '' %>>已發布
                        </option>
                    </select>
                    <p class="mt-1 text-xs text-gray-500">只有已發布的下載內容會顯示在前台</p>
                </div>

                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">目前檔案</label>
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                        <div class="flex items-center">
                            <% let icon='fa-file' ; if (download.mimeType.includes('pdf')) { icon='fa-file-pdf'
                                ; } else if (download.mimeType.includes('word')) { icon='fa-file-word' ; } else
                                if (download.mimeType.includes('excel') ||
                                download.mimeType.includes('spreadsheet')) { icon='fa-file-excel' ; } else if
                                (download.mimeType.includes('image')) { icon='fa-file-image' ; } else if
                                (download.mimeType.includes('zip') || download.mimeType.includes('rar')) {
                                icon='fa-file-archive' ; } %>
                                <i class="fas <%= icon %> fa-2x mr-3 text-gray-600"></i>
                                <div>
                                    <div class="font-medium">
                                        <%= download.originalName %>
                                    </div>
                                    <div class="text-xs text-gray-500">
                                        <%= download.mimeType %> | <%= (download.size / 1024).toFixed(2) %>
                                                KB
                                    </div>
                                </div>
                                <a href="/admin/downloads/<%= download.id %>/download" target="_blank"
                                    class="ml-auto px-3 py-1 text-xs font-medium text-blue-600 bg-blue-100 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                    <i class="fas fa-download mr-1"></i> 下載
                                </a>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <label for="file" class="block text-gray-700 text-sm font-bold mb-2">替換檔案</label>
                    <div class="mt-1 px-6 pt-5 pb-6 border-2 border-gray-300 rounded-lg">
                        <div class="space-y-1 text-center">
                            <i class="fas fa-file-upload text-4xl text-gray-400 mb-3"></i>
                            <div class="text-sm text-gray-600">
                                <label for="file"
                                    class="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                    <span>選擇檔案</span>
                                    <input type="file" id="file" name="file" class="hidden">
                                </label>
                            </div>
                            <p class="text-xs text-gray-500">
                                留空以保留目前檔案。上傳新檔案將替換現有檔案。<br>
                                支援所有檔案格式，最大 <%= Math.round(process.env.MAX_DOWNLOAD_SIZE / (1024*1024)) %>MB
                            </p>
                            <div id="fileNameContainer" class="hidden mt-4 p-2 bg-blue-50 border border-blue-200 rounded">
                                <p id="fileNameDisplay" class="text-sm text-blue-700 font-medium"></p>
                            </div>
                        </div>
                    </div>
                    <p class="mt-2 text-xs text-gray-500">
                        支援所有檔案格式<br>
                        最大檔案大小：<%= Math.round(process.env.MAX_DOWNLOAD_SIZE / (1024*1024)) %>MB
                    </p>
                </div>

                <div class="flex justify-end mt-6">
                    <a href="/admin/downloads"
                        class="px-4 py-2 mr-2 text-sm font-medium leading-5 text-gray-700 transition-colors duration-150 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 focus:outline-none focus:shadow-outline-gray">
                        取消
                    </a>
                    <button type="submit"
                        class="px-4 py-2 text-sm font-medium leading-5 text-white transition-colors duration-150 bg-blue-600 border border-transparent rounded-lg active:bg-blue-600 hover:bg-blue-700 focus:outline-none focus:shadow-outline-blue">
                        更新下載
                    </button>
                </div>
            </form>
        </div>
    </div>

<script>
    // Make environment variables available to client-side JavaScript
    const ENV = {
        MAX_DOWNLOAD_SIZE: parseInt('<%= process.env.MAX_DOWNLOAD_SIZE %>'),
        ALLOWED_DOWNLOAD_TYPES: '<%= process.env.ALLOWED_DOWNLOAD_TYPES %>'.split(',')
    };
    
    // Simple function to display selected file name
    function updateFileName(input) {
        const fileNameContainer = document.getElementById('fileNameContainer');
        const fileNameDisplay = document.getElementById('fileNameDisplay');
        
        if (input.files && input.files.length > 0) {
            const fileName = input.files[0].name;
            fileNameDisplay.textContent = '已選擇: ' + fileName;
            fileNameContainer.classList.remove('hidden');
        } else {
            fileNameDisplay.textContent = '';
            fileNameContainer.classList.add('hidden');
        }
    }
    
    // Function to load groups based on selected category
    async function loadGroups() {
        const categoryId = document.getElementById('categoryId').value;
        const groupSelect = document.getElementById('groupId');
        const currentGroupId = '<%= download.groupId || "" %>';
        
        // Clear existing options (except current one)
        groupSelect.innerHTML = '<option value="">選擇分組</option>';
        
        if (!categoryId) {
            return;
        }
        
        try {
            const response = await fetch(`/admin/api/groups/category/${categoryId}`);
            const groups = await response.json();
            
            // Populate with groups
            groups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.id;
                option.textContent = `${group.name_tw} / ${group.name_en}`;
                if (group.id == currentGroupId) {
                    option.selected = true;
                }
                groupSelect.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading groups:', error);
        }
    }

    document.addEventListener('DOMContentLoaded', function () {
        // Set up file input change listener
        const fileInput = document.getElementById('file');
        
        if (fileInput) {
            fileInput.addEventListener('change', function() {
                updateFileName(this);
            });
        }
        
        // Language tab switching
        const languageTabs = document.querySelectorAll('.language-tab');
        const languageContents = document.querySelectorAll('.language-content');
        
        languageTabs.forEach(tab => {
            tab.addEventListener('click', () => {
                // Remove active class from all tabs
                languageTabs.forEach(t => {
                    t.classList.remove('active');
                    t.classList.remove('border-blue-500');
                    t.classList.remove('text-blue-500');
                    t.classList.add('border-transparent');
                    t.classList.add('text-gray-500');
                });
                
                // Add active class to clicked tab
                tab.classList.add('active');
                tab.classList.add('border-blue-500');
                tab.classList.add('text-blue-500');
                tab.classList.remove('border-transparent');
                tab.classList.remove('text-gray-500');
                
                // Hide all content
                languageContents.forEach(content => {
                    content.classList.add('hidden');
                });
                
                // Show relevant content
                const language = tab.getAttribute('data-language');
                document.querySelectorAll(`.language-content.${language}`).forEach(content => {
                    content.classList.remove('hidden');
                });
            });
        });
        
        // Form validation
        const form = document.querySelector('form');
        form.addEventListener('submit', function (event) {
            const titleTwInput = document.getElementById('title_tw');
            const titleEnInput = document.getElementById('title_en');

            if (!titleTwInput.value.trim() || !titleEnInput.value.trim()) {
                alert('請輸入中文和英文的下載標題');
                event.preventDefault();
                return;
            }

            const fileInput = document.getElementById('file');
            if (fileInput.files && fileInput.files.length > 0) {
                const file = fileInput.files[0];
                // Check by extension as well for more robust detection
                const fileName = file.name.toLowerCase();
                
                // Check filename length - database field likely has a limit
                if (fileName.length > 100) {  // Adjust this value based on your database schema
                    alert('檔案名稱太長。請選擇名稱短於100個字符的檔案。');
                    event.preventDefault();
                    return;
                }

                const maxSize = ENV.MAX_DOWNLOAD_SIZE; // Get file size from environment variable
                const maxSizeMB = Math.round(maxSize / (1024*1024));
                if (file.size > maxSize) {
                    alert(`檔案太大。最大大小為 ${maxSizeMB}MB。`);
                    event.preventDefault();
                    return;
                }
            }
        });
    });
</script>