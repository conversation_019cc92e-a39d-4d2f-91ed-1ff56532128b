<%- contentFor('body') %>

    <div class="container px-6 mx-auto grid">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">下載管理</h1>
                  <div class="space-x-4">
        <a href="/admin/downloads/categories"
          class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-bold py-2 px-4 rounded mr-2">
          <i class="fas fa-cog mr-2"></i> 管理分類
        </a>
        <a href="/admin/downloads/groups"
          class="bg-white hover:bg-gray-100 text-green-500 border border-green-500 font-bold py-2 px-4 rounded mr-2">
          <i class="fas fa-layer-group mr-2"></i> 管理分組
        </a>
        <a href="/admin/downloads/create"
          class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
          <i class="fas fa-plus mr-2"></i> 新增下載
        </a>
      </div>
        </div>

        <!-- Filters -->
        <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
            <form action="/admin/downloads" method="GET"
                class="flex flex-wrap items-end gap-4 mb-6">
                <div class="flex-1 min-w-[200px]">
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">搜尋</label>
                    <input type="text" id="search" name="search" value="<%= filters.search %>"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="搜尋下載項目...">
                </div>
                <div class="flex-1 min-w-[200px]">
                    <label for="category" class="block text-sm font-medium text-gray-700 mb-1">分類</label>
                    <select id="category" name="category"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">所有分類</option>
                        <% categories.forEach(category=> { %>
                            <option value="<%= category.id %>" <%=filters.category==category.id ? 'selected' : '' %>>
                                <%= category.name_tw %> / <%= category.name_en %>
                            </option>
                            <% }); %>
                    </select>
                </div>
                <div class="flex-1 min-w-[200px]">
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">狀態</label>
                    <select id="status" name="status"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">所有狀態</option>
                        <option value="draft" <%=filters.status==='draft' ? 'selected' : '' %>>草稿</option>
                        <option value="published" <%=filters.status==='published' ? 'selected' : '' %>>已發佈</option>
                    </select>
                </div>
                <button type="submit"
                    class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    套用篩選
                </button>
            </form>
        </div>

        <!-- Downloads List -->
        <div class="overflow-x-auto rounded-lg shadow">
            <table class="w-full whitespace-no-wrap">
                <thead>
                    <tr class="text-xs font-semibold tracking-wide text-left text-gray-500 uppercase bg-gray-50 border-b">
                        <th class="px-4 py-3">標題</th>
                        <th class="px-4 py-3">分類</th>
                        <th class="px-4 py-3">狀態</th>
                        <th class="px-4 py-3">作者</th>
                        <th class="px-4 py-3">日期</th>
                        <th class="px-4 py-3">操作</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y">
                    <% downloads.forEach(download=> { %>
                        <tr class="text-gray-700 dark:text-gray-400">
                            <td class="px-4 py-3">
                                <div class="text-sm font-medium text-gray-900">
                                    <%= download.title_tw %>
                                </div>
                                <div class="text-sm text-gray-500">
                                    <%= download.title_en %>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-sm text-gray-900">
                                    <%= download.category ? download.category.name_tw : '-' %>
                                </div>
                                <% if (download.category) { %>
                                    <div class="text-sm text-gray-500">
                                        <%= download.category.name_en %>
                                    </div>
                                    <% } %>
                            </td>
                            <td class="px-4 py-3">
                                <span
                                    class="px-2 py-1 text-xs font-medium rounded-full <%= download.status === 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' %>">
                                    <%= download.status==='published' ? '已發佈' : '草稿' %>
                                </span>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-sm text-gray-900">
                                    <%= download.author.username %>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <div class="text-sm text-gray-900">
                                    <%= new Date(download.createdAt).toLocaleDateString() %>
                                </div>
                            </td>
                            <td class="px-4 py-3">
                                <div class="flex items-center space-x-4 text-sm">
                                    <a href="/admin/downloads/edit/<%= download.id %>" 
                                       class="flex items-center justify-between px-2 py-2 text-sm font-medium leading-5 text-blue-600 rounded-lg hover:bg-blue-50 focus:outline-none focus:shadow-outline-gray">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="/admin/downloads/<%= download.id %>/delete" method="POST"
                                        class="inline">
                                        <button type="submit" 
                                                class="flex items-center justify-between px-2 py-2 text-sm font-medium leading-5 text-red-600 rounded-lg hover:bg-red-50 focus:outline-none focus:shadow-outline-gray"
                                                onclick="return confirm('您確定要刪除此下載項目嗎？')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <% }); %>
                            <% if (downloads.length===0) { %>
                                <tr>
                                    <td colspan="6" class="px-4 py-3 text-center text-sm text-gray-500">
                                        尚未找到任何下載項目
                                    </td>
                                </tr>
                                <% } %>
                </tbody>
            </table>
        </div>
    </div>