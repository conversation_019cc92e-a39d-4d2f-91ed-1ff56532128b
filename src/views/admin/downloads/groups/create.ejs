<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold">建立下載分組</h1>
            <div class="space-x-2">
                <a href="/admin/downloads"
                    class="bg-white hover:bg-gray-100 text-blue-500 border border-blue-500 font-semibold py-2 px-4 rounded">
                    <i class="fas fa-home mr-2"></i> 下載頁面
                </a>
                <a href="/admin/downloads/groups"
                    class="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded">
                    <i class="fas fa-arrow-left mr-2"></i> 返回列表
                </a>
            </div>
        </div>

        <%- include('../../../partials/messages') %>

            <!-- Group Form -->
            <div class="bg-white shadow rounded-lg p-6">
                <form action="/admin/downloads/groups" method="POST">
                    <!-- Category Selection -->
                    <div class="mb-6 pb-4 border-b border-gray-200">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-folder mr-2 text-gray-600"></i>
                            <h3 class="text-lg font-medium text-gray-700">分類選擇</h3>
                        </div>

                        <div class="mb-4">
                            <label for="categoryId" class="block text-sm font-medium text-gray-700 mb-2">
                                所屬分類 <span class="text-red-500">*</span>
                            </label>
                            <select id="categoryId" name="categoryId" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500">
                                <option value="">選擇分類</option>
                                <% categories.forEach(function(category) { %>
                                    <option value="<%= category.id %>">
                                        <%= category.name_tw %> / <%= category.name_en %>
                                    </option>
                                    <% }); %>
                            </select>
                            <p class="mt-1 text-xs text-gray-500">請選擇此分組所屬的分類</p>
                        </div>
                    </div>

                    <!-- Traditional Chinese Content -->
                    <div class="mb-6 pb-4 border-b border-gray-200">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-language mr-2 text-gray-600"></i>
                            <h3 class="text-lg font-medium text-gray-700">中文內容</h3>
                        </div>

                        <!-- Name (Traditional Chinese) -->
                        <div class="mb-4">
                            <label for="name_tw" class="block text-sm font-medium text-gray-700 mb-2">
                                分組名稱 (中文) <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                id="name_tw" name="name_tw" required placeholder="例如：運作機制" />
                            <p class="mt-1 text-xs text-gray-500">請輸入此分組的中文名稱</p>
                        </div>

                        <!-- Description (Traditional Chinese) -->
                        <div class="mb-4">
                            <label for="description_tw" class="block text-sm font-medium text-gray-700 mb-2">
                                描述 (中文)
                            </label>
                            <textarea
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                id="description_tw" name="description_tw" rows="3" placeholder="此分組的簡短描述"></textarea>
                            <p class="mt-1 text-xs text-gray-500">選填：提供此分組的簡短描述</p>
                        </div>
                    </div>

                    <!-- English Content -->
                    <div class="mb-6 pb-4 border-b border-gray-200">
                        <div class="flex items-center mb-4">
                            <i class="fas fa-language mr-2 text-gray-600"></i>
                            <h3 class="text-lg font-medium text-gray-700">英文內容</h3>
                        </div>

                        <!-- Name (English) -->
                        <div class="mb-4">
                            <label for="name_en" class="block text-sm font-medium text-gray-700 mb-2">
                                分組名稱 (英文) <span class="text-red-500">*</span>
                            </label>
                            <input type="text"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                id="name_en" name="name_en" required placeholder="例如：Operation Mechanism" />
                            <p class="mt-1 text-xs text-gray-500">輸入此分組的英文名稱</p>
                        </div>

                        <!-- Description (English) -->
                        <div class="mb-4">
                            <label for="description_en" class="block text-sm font-medium text-gray-700 mb-2">
                                描述 (英文)
                            </label>
                            <textarea
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                id="description_en" name="description_en" rows="3"
                                placeholder="此分組的簡短描述"></textarea>
                            <p class="mt-1 text-xs text-gray-500">選填：提供此分組的簡短描述</p>
                        </div>
                    </div>

                    <!-- Common Fields -->
                    <div>
                        <!-- Order -->
                        <div class="mb-6">
                            <label for="order" class="block text-sm font-medium text-gray-700 mb-2">
                                顯示順序
                            </label>
                            <input type="number"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                                id="order" name="order" min="0" value="0" />
                            <p class="mt-1 text-xs text-gray-500">數字越小，顯示越靠前</p>
                        </div>

                        <!-- Submit Button -->
                        <div class="flex justify-end">
                            <a href="/admin/downloads/groups"
                                class="bg-gray-500 hover:bg-gray-600 text-white font-semibold py-2 px-4 rounded mr-2">
                                取消
                            </a>
                            <button type="submit"
                                class="bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded">
                                建立分組
                            </button>
                        </div>
                    </div>
                </form>
            </div>
    </div> 