<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">編輯橫幅</h1>
            <a href="/admin/banners" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-arrow-left mr-2"></i> 返回橫幅列表
            </a>
        </div>

        <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
            <div class="mb-6">
                <h2 class="text-lg font-semibold mb-2">目前主要媒體</h2>
                <div class="border rounded p-4 flex justify-center">
                    <% if (banner.mediaType==='image' ) { %>
                        <img src="<%= banner.mediaPath %>" alt="<%= banner.title_en %>" class="max-h-64 object-contain">
                        <% } else { %>
                            <video class="max-h-64" controls>
                                <source src="<%= banner.mediaPath %>" type="video/mp4">
                                您的瀏覽器不支援影片標籤。
                            </video>
                            <% } %>
                </div>
            </div>

            <% if (banner.mediaPathDesktop) { %>
                <div class="mb-6">
                    <h2 class="text-lg font-semibold mb-2">目前桌面版圖片</h2>
                    <div class="border rounded p-4 flex justify-center">
                        <img src="<%= banner.mediaPathDesktop %>" alt="<%= banner.title_en %> - 桌面版"
                            class="max-h-64 object-contain">
                    </div>
                </div>
                <% } %>

                    <% if (banner.mediaPathTablet) { %>
                        <div class="mb-6">
                            <h2 class="text-lg font-semibold mb-2">目前平板版圖片</h2>
                            <div class="border rounded p-4 flex justify-center">
                                <img src="<%= banner.mediaPathTablet %>" alt="<%= banner.title_en %> - 平板版"
                                    class="max-h-64 object-contain">
                            </div>
                        </div>
                        <% } %>

                            <% if (banner.mediaPathMobile) { %>
                                <div class="mb-6">
                                    <h2 class="text-lg font-semibold mb-2">目前手機版圖片</h2>
                                    <div class="border rounded p-4 flex justify-center">
                                        <img src="<%= banner.mediaPathMobile %>" alt="<%= banner.title_en %> - 手機版"
                                            class="max-h-64 object-contain">
                                    </div>
                                </div>
                                <% } %>

                                    <form action="/admin/banners/<%= banner.id %>" method="POST"
                                        enctype="multipart/form-data">
                                        <!-- Language Tabs -->
                                        <%- include('../../partials/admin-language-switcher', { title: '內容語言' ,
                                            activeTab: 'tw' }) %>

                                            <!-- Traditional Chinese Content -->
                                            <div class="language-content language-content-tw">
                                                <div class="mb-4">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2"
                                                        for="title_tw">
                                                        標題 (中文) <span class="text-red-500">*</span>
                                                    </label>
                                                    <input
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="title_tw" name="title_tw" type="text"
                                                        value="<%= banner.title_tw %>" placeholder="請輸入橫幅標題" required>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2"
                                                        for="description_tw">
                                                        描述 (中文)
                                                    </label>
                                                    <textarea
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="description_tw" name="description_tw" rows="3"
                                                        placeholder="請輸入橫幅描述"><%= banner.description_tw || '' %></textarea>
                                                </div>
                                            </div>

                                            <!-- English Content -->
                                            <div class="language-content language-content-en hidden">
                                                <div class="mb-4">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2"
                                                        for="title_en">
                                                        Title (English) <span class="text-red-500">*</span>
                                                    </label>
                                                    <input
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="title_en" name="title_en" type="text"
                                                        value="<%= banner.title_en %>"
                                                        placeholder="Enter banner title in English" required>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2"
                                                        for="description_en">
                                                        Description (English)
                                                    </label>
                                                    <textarea
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="description_en" name="description_en" rows="3"
                                                        placeholder="Enter banner description in English"><%= banner.description_en || '' %></textarea>
                                                </div>
                                            </div>

                                            <!-- Common Fields -->
                                            <div class="border-t pt-4 mt-4">
                                                <div class="mb-4">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="url">
                                                        網址 (選填)
                                                    </label>
                                                    <input
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="url" name="url" type="url" value="<%= banner.url || '' %>"
                                                        placeholder="https://example.com">
                                                    <p class="text-gray-600 text-xs italic mt-1">用戶點擊橫幅時的重定向連結</p>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2"
                                                        for="media">
                                                        更換主要媒體 (圖片或影片)
                                                    </label>
                                                    <input
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="media" name="media" type="file" accept="image/*,video/mp4">
                                                    <p class="text-gray-600 text-xs italic mt-1">上傳新的圖片或MP4影片 (最大20MB)。
                                                        留空以保留目前媒體。</p>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2"
                                                        for="mediaDesktop">
                                                        更換桌面版圖片 (選填)
                                                    </label>
                                                    <input
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="mediaDesktop" name="mediaDesktop" type="file"
                                                        accept="image/*">
                                                    <p class="text-gray-600 text-xs italic mt-1">上傳適合桌面顯示的圖片 (建議寬度:
                                                        1920px)</p>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2"
                                                        for="mediaTablet">
                                                        更換平板版圖片 (選填)
                                                    </label>
                                                    <input
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="mediaTablet" name="mediaTablet" type="file"
                                                        accept="image/*">
                                                    <p class="text-gray-600 text-xs italic mt-1">上傳適合平板顯示的圖片 (建議寬度:
                                                        1024px)</p>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2"
                                                        for="mediaMobile">
                                                        更換手機版圖片 (選填)
                                                    </label>
                                                    <input
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="mediaMobile" name="mediaMobile" type="file"
                                                        accept="image/*">
                                                    <p class="text-gray-600 text-xs italic mt-1">上傳適合手機顯示的圖片 (建議寬度:
                                                        640px)</p>
                                                </div>

                                                <div class="mb-4">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2" for="order">
                                                        顯示順序
                                                    </label>
                                                    <input
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="order" name="order" type="number" min="0" value="<%= banner.order %>">
                                                    <p class="text-gray-600 text-xs italic mt-1">數字越小的橫幅將優先顯示，預設為0</p>
                                                </div>

                                                <div class="mb-6">
                                                    <label class="block text-gray-700 text-sm font-bold mb-2"
                                                        for="status">
                                                        狀態
                                                    </label>
                                                    <select
                                                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                        id="status" name="isActive">
                                                        <option value="true" <%=banner.isActive ? 'selected' : '' %>>啟用
                                                        </option>
                                                        <option value="false" <%=!banner.isActive ? 'selected' : '' %>
                                                            >停用</option>
                                                    </select>
                                                    <p class="text-gray-600 text-xs italic mt-1">啟用的橫幅將顯示在首頁</p>
                                                </div>

                                                <div class="flex items-center justify-between">
                                                    <button
                                                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                                                        type="submit">
                                                        更新橫幅
                                                    </button>
                                                </div>
                                            </div>
                                    </form>

                                    <form action="/admin/banners/<%= banner.id %>" method="POST" class="inline mt-4"
                                        onsubmit="return confirm('您確定要刪除此橫幅嗎？');">
                                        <input type="hidden" name="_method" value="DELETE">
                                        <button type="submit"
                                            class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                            刪除橫幅
                                        </button>
                                    </form>
        </div>
    </div>