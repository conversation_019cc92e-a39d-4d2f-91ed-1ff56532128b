<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold text-neutral-800">首頁橫幅</h1>
            <a href="/admin/banners/create"
                class="bg-blue-500 hover:bg-blue-600 text-white font-medium py-2 px-4 rounded-md shadow-sm flex items-center">
                <i class="fas fa-plus mr-2"></i> 新增首頁橫幅
            </a>
        </div>

        <% if (banners && banners.length> 0) { %>
            <div class="bg-white shadow-md rounded-lg overflow-x-auto">
                <table class="min-w-full divide-y divide-neutral-200">
                    <thead class="bg-neutral-50">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                                媒體
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                                標題
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                                網址
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                                顯示順序
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                                狀態
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                                建立者
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-right text-xs font-medium text-neutral-500 uppercase tracking-wider whitespace-nowrap">
                                操作
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-neutral-200">
                        <% banners.forEach(banner=> { %>
                            <tr class="hover:bg-neutral-50 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex-shrink-0 overflow-hidden rounded-md">
                                        <div class="mb-2">
                                            <span class="text-xs font-semibold">主要媒體:</span>
                                            <% if (banner.mediaType==='image' ) { %>
                                                <img src="<%= banner.mediaPath %>" alt="<%= banner.title_en %>"
                                                    class="h-20 w-32 object-cover">
                                                <% } else { %>
                                                    <video class="h-20 w-32 object-cover" controls>
                                                        <source src="<%= banner.mediaPath %>" type="video/mp4">
                                                        您的瀏覽器不支援影片標籤。
                                                    </video>
                                                    <% } %>
                                        </div>

                                        <% if (banner.mediaPathDesktop) { %>
                                            <div class="mb-2">
                                                <span class="text-xs font-semibold">桌面版:</span>
                                                <img src="<%= banner.mediaPathDesktop %>"
                                                    alt="<%= banner.title_en %> - 桌面版" class="h-16 w-28 object-cover">
                                            </div>
                                            <% } %>

                                                <% if (banner.mediaPathTablet) { %>
                                                    <div class="mb-2">
                                                        <span class="text-xs font-semibold">平板版:</span>
                                                        <img src="<%= banner.mediaPathTablet %>"
                                                            alt="<%= banner.title_en %> - 平板版"
                                                            class="h-16 w-28 object-cover">
                                                    </div>
                                                    <% } %>

                                                        <% if (banner.mediaPathMobile) { %>
                                                            <div>
                                                                <span class="text-xs font-semibold">手機版:</span>
                                                                <img src="<%= banner.mediaPathMobile %>"
                                                                    alt="<%= banner.title_en %> - 手機版"
                                                                    class="h-16 w-28 object-cover">
                                                            </div>
                                                            <% } %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-neutral-900">
                                        <div class="mb-1">
                                            <span class="text-xs text-neutral-500">中:</span>
                                            <%= banner.title_tw %>
                                        </div>
                                        <div>
                                            <span class="text-xs text-neutral-500">EN:</span>
                                            <%= banner.title_en %>
                                        </div>
                                    </div>
                                    <% if (banner.description_en || banner.description_tw) { %>
                                        <div class="text-sm text-neutral-500 truncate max-w-xs mt-1">
                                            <% if (banner.description_tw) { %>
                                                <div class="text-xs">
                                                    <span class="text-neutral-400">中:</span>
                                                    <%= banner.description_tw.length> 30 ?
                                                        banner.description_tw.substring(0, 30) + '...' :
                                                        banner.description_tw %>
                                                </div>
                                            <% } %>
                                            <% if (banner.description_en) { %>
                                                <div class="text-xs">
                                                    <span class="text-neutral-400">EN:</span>
                                                    <%= banner.description_en.length> 30 ?
                                                        banner.description_en.substring(0, 30) + '...' :
                                                        banner.description_en %>
                                                </div>
                                            <% } %>
                                        </div>
                                    <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                                    <% if (banner.url) { %>
                                        <a href="<%= banner.url %>" target="_blank"
                                            class="text-blue-600 hover:text-blue-800">
                                            <%= banner.url.length> 30 ? banner.url.substring(0, 30) + '...' : banner.url
                                                %>
                                        </a>
                                        <% } else { %>
                                            <span class="text-neutral-400">無網址</span>
                                            <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                                    <span class="font-medium"><%= banner.order %></span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <button
                                        class="toggle-status px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full <%= banner.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' %>"
                                        data-id="<%= banner.id %>" data-active="<%= banner.isActive %>">
                                        <%= banner.isActive ? '啟用中' : '已停用' %>
                                    </button>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-neutral-500">
                                    <%= banner.createdBy.username %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="/admin/banners/edit/<%= banner.id %>"
                                        class="text-blue-600 hover:text-blue-800 mr-4">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <form action="/admin/banners/<%= banner.id %>?_method=DELETE" method="POST"
                                        class="inline" onsubmit="return confirm('您確定要刪除此橫幅嗎？');">
                                        <button type="submit" class="text-red-600 hover:text-red-800">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                            <% }); %>
                    </tbody>
                </table>
            </div>
            <% } else { %>
                <div class="bg-white shadow-md rounded-lg p-8 text-center text-neutral-500">
                    <div class="mb-4">
                        <i class="fas fa-image text-4xl text-neutral-300"></i>
                    </div>
                    <p class="mb-4">尚未找到任何首頁橫幅。</p>
                    <a href="/admin/banners/create" class="text-blue-600 hover:text-blue-800 font-medium">
                        立即建立一個 <i class="fas fa-arrow-right ml-1"></i>
                    </a>
                </div>
                <% } %>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const toggleButtons = document.querySelectorAll('.toggle-status');

            toggleButtons.forEach(button => {
                button.addEventListener('click', async function () {
                    const id = this.dataset.id;
                    const currentStatus = this.dataset.active === 'true';

                    try {
                        const response = await fetch(`/admin/banners/toggle/${id}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        const data = await response.json();

                        if (data.success) {
                            // Update button appearance
                            this.classList.remove(currentStatus ? 'bg-green-100' : 'bg-red-100');
                            this.classList.remove(currentStatus ? 'text-green-800' : 'text-red-800');
                            this.classList.add(!currentStatus ? 'bg-green-100' : 'bg-red-100');
                            this.classList.add(!currentStatus ? 'text-green-800' : 'text-red-800');

                            // Update text
                            this.textContent = !currentStatus ? '啟用中' : '已停用';

                            // Update data attribute
                            this.dataset.active = !currentStatus;

                            // Show success message
                            alert(data.message);
                        } else {
                            alert('錯誤: ' + data.message);
                        }
                    } catch (error) {
                        console.error('錯誤:', error);
                        alert('更新橫幅狀態時發生錯誤');
                    }
                });
            });
        });
    </script>