<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">網頁橫幅列表</h1>
            <a href="/admin/pageImages/create"
                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-plus mr-2"></i> 新增網頁橫幅
            </a>
        </div>

        <div class="bg-white shadow-md rounded my-6 overflow-x-auto">
            <% if (pageImages && pageImages.length> 0) { %>
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                預覽</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                頁面</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                檔案名稱</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                狀態</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                建立者</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                建立日期</th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider whitespace-nowrap">
                                操作</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <% pageImages.forEach(function(pageImage) { %>
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex flex-col space-y-2">
                                        <div>
                                            <span class="text-xs font-semibold">桌面版:</span>
                                            <img src="<%= pageImage.pathDesktop || pageImage.path %>"
                                                alt="<%= pageImage.targetPage %> - 桌面版"
                                                class="h-16 w-auto object-cover">
                                        </div>
                                        <div>
                                            <span class="text-xs font-semibold">平板版:</span>
                                            <img src="<%= pageImage.pathTablet || pageImage.path %>"
                                                alt="<%= pageImage.targetPage %> - 平板版"
                                                class="h-16 w-auto object-cover">
                                        </div>
                                        <div>
                                            <span class="text-xs font-semibold">手機版:</span>
                                            <img src="<%= pageImage.pathMobile || pageImage.path %>"
                                                alt="<%= pageImage.targetPage %> - 手機版"
                                                class="h-16 w-auto object-cover">
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <%= pageImage.targetPage %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <%= pageImage.originalName %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <% if (pageImage.isActive) { %>
                                        <span
                                            class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                            啟用
                                        </span>
                                        <% } else { %>
                                            <span
                                                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                                                停用
                                            </span>
                                            <% } %>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <%= pageImage.createdBy.username %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <%= new Date(pageImage.createdAt).toLocaleDateString() %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <a href="/admin/pageImages/toggle/<%= pageImage.id %>"
                                        class="text-indigo-600 hover:text-indigo-900 mr-3">
                                        <%= pageImage.isActive ? '停用' : '啟用' %>
                                    </a>
                                    <a href="/admin/pageImages/edit/<%= pageImage.id %>"
                                        class="text-yellow-600 hover:text-yellow-900 mr-3">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="/admin/pageImages/delete/<%= pageImage.id %>"
                                        class="text-red-600 hover:text-red-900"
                                        onclick="return confirm('確定要刪除此網頁橫幅嗎？');">
                                        <i class="fas fa-trash-alt"></i>
                                    </a>
                                </td>
                            </tr>
                            <% }); %>
                    </tbody>
                </table>
                <% } else { %>
                    <div class="p-6 text-center text-gray-500">
                        <p>目前沒有網頁橫幅。</p>
                        <a href="/admin/pageImages/create"
                            class="mt-4 inline-block bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            新增第一個網頁橫幅
                        </a>
                    </div>
                    <% } %>
        </div>
    </div>