<%- contentFor('body') %>

    <div class="container mx-auto px-4 py-8">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-3xl font-bold">編輯網頁橫幅</h1>
            <a href="/admin/pageImages" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                <i class="fas fa-arrow-left mr-2"></i> 返回列表
            </a>
        </div>

        <div class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
            <form action="/admin/pageImages/edit/<%= pageImage.id %>" method="POST" enctype="multipart/form-data">
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="targetPage">
                        目標頁面 <span class="text-red-500">*</span>
                    </label>
                    <select
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="targetPage" name="targetPage" required>
                        <% pages.forEach(function(page) { %>
                            <option value="<%= page.name %>" <%=pageImage.targetPage===page.name ? 'selected' : '' %>>
                                <%= page.chineseName %>
                            </option>
                            <% }); %>
                    </select>
                    <p class="text-gray-600 text-xs italic mt-1">選擇要顯示此橫幅的頁面</p>
                </div>

                <!-- Desktop Image -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">
                        當前桌面版圖片
                    </label>
                    <div class="border p-2 rounded">
                        <img src="<%= pageImage.pathDesktop || pageImage.path %>"
                            alt="<%= pageImage.targetPage %> - 桌面版" class="max-h-48 w-auto">
                        <p class="text-sm text-gray-600 mt-1">
                            <%= pageImage.originalNameDesktop || pageImage.originalName %>
                        </p>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="imageDesktop">
                        更換桌面版圖片 (選填)
                    </label>
                    <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="imageDesktop" name="imageDesktop" type="file" accept="image/*">
                    <p class="text-gray-600 text-xs italic mt-1">上傳適合桌面顯示的圖片 (建議寬度: 1920px)</p>
                </div>

                <!-- Tablet Image -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">
                        當前平板版圖片
                    </label>
                    <div class="border p-2 rounded">
                        <img src="<%= pageImage.pathTablet || pageImage.path %>" alt="<%= pageImage.targetPage %> - 平板版"
                            class="max-h-48 w-auto">
                        <p class="text-sm text-gray-600 mt-1">
                            <%= pageImage.originalNameTablet || pageImage.originalName %>
                        </p>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="imageTablet">
                        更換平板版圖片 (選填)
                    </label>
                    <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="imageTablet" name="imageTablet" type="file" accept="image/*">
                    <p class="text-gray-600 text-xs italic mt-1">上傳適合平板顯示的圖片 (建議寬度: 1024px)</p>
                </div>

                <!-- Mobile Image -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">
                        當前手機版圖片
                    </label>
                    <div class="border p-2 rounded">
                        <img src="<%= pageImage.pathMobile || pageImage.path %>" alt="<%= pageImage.targetPage %> - 手機版"
                            class="max-h-48 w-auto">
                        <p class="text-sm text-gray-600 mt-1">
                            <%= pageImage.originalNameMobile || pageImage.originalName %>
                        </p>
                    </div>
                </div>

                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2" for="imageMobile">
                        更換手機版圖片 (選填)
                    </label>
                    <input
                        class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                        id="imageMobile" name="imageMobile" type="file" accept="image/*">
                    <p class="text-gray-600 text-xs italic mt-1">上傳適合手機顯示的圖片 (建議寬度: 640px)</p>
                </div>

                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2">
                        狀態
                    </label>
                    <div class="flex items-center">
                        <input type="radio" id="active" name="isActive" value="true" class="mr-2" <%=pageImage.isActive
                            ? 'checked' : '' %>>
                        <label for="active" class="mr-4">啟用</label>

                        <input type="radio" id="inactive" name="isActive" value="false" class="mr-2"
                            <%=!pageImage.isActive ? 'checked' : '' %>>
                        <label for="inactive">停用</label>
                    </div>
                    <p class="text-gray-600 text-xs italic mt-1">設定為啟用時，橫幅將顯示在所選頁面</p>
                </div>

                <div class="flex items-center justify-end">
                    <button
                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                        type="submit">
                        更新網頁橫幅
                    </button>
                </div>
            </form>
        </div>
    </div>