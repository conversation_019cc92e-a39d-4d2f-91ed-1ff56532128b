<%- contentFor('body') %>

<div class="bg-white shadow rounded-lg p-6">
  <div class="flex items-center gap-4 mb-6">
    <% if (typeof settings !== 'undefined' && settings && settings.logo_desktop_path) { %>
      <img src="/<%= settings.logo_desktop_path %>" alt="<%= settings.logo_alt_tw || '網站標誌' %>" class="h-12">
    <% } %>
    <h1 class="text-2xl font-bold">網站設定</h1>
  </div>

  <form action="/admin/site-settings" method="POST" enctype="multipart/form-data">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <!-- Site Name (Traditional Chinese) -->
      <div>
        <label for="site_name_tw" class="block text-sm font-medium text-gray-700 mb-1">網站名稱 (中文)</label>
        <input type="text" id="site_name_tw" name="site_name_tw" 
          value="<%= settings ? settings.site_name_tw : '' %>" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" 
          required>
      </div>
      
      <!-- Site Name (English) -->
      <div>
        <label for="site_name_en" class="block text-sm font-medium text-gray-700 mb-1">網站名稱 (英文)</label>
        <input type="text" id="site_name_en" name="site_name_en" 
          value="<%= settings ? settings.site_name_en : '' %>" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" 
          required>
      </div>

      <!-- Site URL -->
      <div>
        <label for="site_url" class="block text-sm font-medium text-gray-700 mb-1">網站網址</label>
        <input type="url" id="site_url" name="site_url" 
          value="<%= settings ? settings.site_url : '' %>" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" 
          required>
      </div>
    </div>

    <!-- Logo Alt Text -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
      <!-- Logo Alt Text (English) -->
      <div>
        <label for="logo_alt_en" class="block text-sm font-medium text-gray-700 mb-1">標誌替代文字 (英文)</label>
        <input type="text" id="logo_alt_en" name="logo_alt_en" 
          value="<%= settings ? settings.logo_alt_en : '' %>" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
      </div>

      <!-- Logo Alt Text (Traditional Chinese) -->
      <div>
        <label for="logo_alt_tw" class="block text-sm font-medium text-gray-700 mb-1">標誌替代文字 (中文)</label>
        <input type="text" id="logo_alt_tw" name="logo_alt_tw" 
          value="<%= settings ? settings.logo_alt_tw : '' %>" 
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
      </div>
    </div>

    <!-- Logo Uploads -->
    <div class="mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-3">網站標誌</h2>
      <p class="text-sm text-gray-500 mb-4">上傳不同螢幕尺寸的標誌版本。為獲得最佳效果，請使用具有透明背景的 PNG 或 SVG 格式。</p>
      
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Logo Desktop -->
        <div class="border rounded-lg p-4">
          <h3 class="font-medium mb-2">桌面標誌</h3>
          <div class="mb-3">
            <% if (settings && settings.logo_desktop_path) { %>
              <img src="/<%= settings.logo_desktop_path %>" alt="目前桌面標誌" class="mb-2 max-h-20">
              <p class="text-xs text-gray-500 mb-2">目前標誌: <%= settings.logo_desktop_path.split('/').pop() %></p>
            <% } %>
            <label class="block text-sm font-medium text-gray-700 mb-1">上傳新的桌面標誌</label>
            <input type="file" name="logo_desktop" accept="image/*" class="w-full text-sm">
          </div>
          <p class="text-xs text-gray-500">建議尺寸: 200×60像素</p>
        </div>

        <!-- Logo Tablet -->
        <div class="border rounded-lg p-4">
          <h3 class="font-medium mb-2">平板標誌</h3>
          <div class="mb-3">
            <% if (settings && settings.logo_tablet_path) { %>
              <img src="/<%= settings.logo_tablet_path %>" alt="目前平板標誌" class="mb-2 max-h-16">
              <p class="text-xs text-gray-500 mb-2">目前標誌: <%= settings.logo_tablet_path.split('/').pop() %></p>
            <% } %>
            <label class="block text-sm font-medium text-gray-700 mb-1">上傳新的平板標誌</label>
            <input type="file" name="logo_tablet" accept="image/*" class="w-full text-sm">
          </div>
          <p class="text-xs text-gray-500">建議尺寸: 160×48像素</p>
        </div>

        <!-- Logo Mobile -->
        <div class="border rounded-lg p-4">
          <h3 class="font-medium mb-2">行動裝置標誌</h3>
          <div class="mb-3">
            <% if (settings && settings.logo_mobile_path) { %>
              <img src="/<%= settings.logo_mobile_path %>" alt="目前行動裝置標誌" class="mb-2 max-h-12">
              <p class="text-xs text-gray-500 mb-2">目前標誌: <%= settings.logo_mobile_path.split('/').pop() %></p>
            <% } %>
            <label class="block text-sm font-medium text-gray-700 mb-1">上傳新的行動裝置標誌</label>
            <input type="file" name="logo_mobile" accept="image/*" class="w-full text-sm">
          </div>
          <p class="text-xs text-gray-500">建議尺寸: 120×36像素</p>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="flex justify-end">
      <button type="submit" class="px-4 py-2 bg-indigo-600 text-white font-medium rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
        儲存設定
      </button>
    </div>
  </form>
</div> 
