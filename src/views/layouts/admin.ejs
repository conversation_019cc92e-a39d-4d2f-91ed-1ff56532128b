<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>
        大南方新矽谷推動辦公室後台
    </title>

    <!-- Tailwind CSS -->
    <link href="/css/<EMAIL>" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="/css/<EMAIL>" rel="stylesheet">

    <!-- Include Quill.js 2.0.3 for rendering -->
    <link href="/css/<EMAIL>" rel="stylesheet">
    <script src="/js/<EMAIL>"></script>
    
    <!-- Jquery -->
    <script src="/js/jquery-3.6.0.min.js"></script>
    
    <style>
        .dropdown-menu {
            display: none;
            z-index: 50;
        }

        .dropdown-menu.show {
            display: block;
        }

        .dropdown-menu:not(.hidden) {
            display: block;
        }

        .sidebar {
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            overflow-y: auto;
            z-index: 40;
            background-color: #ffffff;
            box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }

        .main-content {
            margin-left: 250px;
            min-height: 100vh;
            background-color: #f5f5f5;
        }

        .dropdown-toggle {
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            color: #333;
            background-color: #ffffff;
            margin-bottom: 2px;
            border-radius: 4px;
        }

        .dropdown-toggle i.fa-chevron-down {
            font-size: 12px;
            color: #666;
        }

        .dropdown-toggle.active {
            background-color: #e5f4f7;
        }

        .dropdown-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.2s ease-out;
            background-color: #ffffff;
        }

        .dropdown-content.show {
            max-height: 500px;
            transition: max-height 0.3s ease-in;
        }

        .dropdown-content a {
            padding: 8px 15px 8px 35px;
            color: #666;
            display: block;
            text-decoration: none;
            font-size: 14px;
        }

        .dropdown-content a:hover {
            background-color: #f0f9fb;
            color: #333;
        }

        .sidebar-header {
            padding: 20px;
            background-color: #ffffff;
            border-bottom: 1px solid #e5e5e5;
        }

        .sidebar-section {
            margin: 10px 0;
        }

        .dropdown-toggle {
            font-size: 13px;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .sidebar {
                width: 0;
                transform: translateX(-100%);
                transition: transform 0.3s ease-in-out;
            }

            .sidebar.open {
                width: 250px;
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }
        }
    </style>

    <!-- Additional Styles -->
    <%- style %>
</head>

<body class="bg-neutral-100">
    <!-- Left Sidebar Navigation -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <% 
            // Check for logo in either settings or siteSettings
            const logo = (typeof settings !== 'undefined' && settings && settings.logo_desktop_path) ? 
                        settings.logo_desktop_path : 
                        (typeof siteSettings !== 'undefined' && siteSettings && siteSettings.logo_desktop_path) ? 
                        siteSettings.logo_desktop_path : null;
                        
            const logoAlt = (typeof settings !== 'undefined' && settings && settings.logo_alt_tw) ? 
                        settings.logo_alt_tw : 
                        (typeof siteSettings !== 'undefined' && siteSettings && siteSettings.logo_alt_tw) ? 
                        siteSettings.logo_alt_tw : '大南方新矽谷';
            %>
            
            <% if (logo) { %>
                <a href="/admin/dashboard" class="block">
                    <img src="/<%= logo %>" alt="<%= logoAlt %>" class="h-10">
                </a>
            <% } else { %>
                <a href="/admin/dashboard" class="text-gray-800 font-bold text-xl block">大南方新矽谷</a>
            <% } %>
        </div>

        <% if (typeof user !=='undefined' && user) { %>
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center mb-4">
                    <span class="text-gray-600 mr-2">
                        歡迎 <%= user.contactName_zh ? user.contactName_zh : user.username %>
                    </span>
                    <a href="/admin/logout" class="text-gray-500 hover:text-gray-700 ml-auto">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>

            <div class="py-2 px-3">
                <ul class="space-y-1">
                    <li>
                        <a href="/" class="block px-4 py-2 text-gray-600 hover:bg-gray-100 hover:text-gray-800 rounded">
                            <i class="fas fa-arrow-right mr-2"></i> 前往首頁
                        </a>
                    </li>

                    <% if (user.role !== 'visitor') { %>
                    <li>
                        <a href="/admin/dashboard" class="block px-4 py-2 text-gray-600 hover:bg-gray-100 hover:text-gray-800 rounded">儀表板</a>
                    </li>
                    <% } %>

                    <% if (user.role === 'super_admin' || user.role === 'admin') { %>
                    <!-- Section 1: 編輯首頁 -->
                    <li class="sidebar-section">
                        <div class="dropdown-toggle" data-target="dropdown-homepage">
                            編輯首頁
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <ul class="dropdown-content" id="dropdown-homepage">
                            <li>
                                <a href="/admin/banners">
                                    <i class="fas fa-image mr-2"></i> 首頁橫幅
                                </a>
                            </li>
                            <li>
                                <a href="/admin/frontpage">
                                    <i class="fas fa-list mr-2"></i> 首頁項目
                                </a>
                            </li>
                            <li>
                                <a href="/admin/links">
                                    <i class="fas fa-link mr-2"></i> 相關連結
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Section 2: 編輯頁面 - For admin/super_admin, show all options -->
                    <li class="sidebar-section">
                        <div class="dropdown-toggle" data-target="dropdown-pages">
                            編輯頁面
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <ul class="dropdown-content" id="dropdown-pages">
                            <li>
                                <a href="/admin/about">
                                    <i class="fas fa-info-circle mr-2"></i> 關於我們
                                </a>
                            </li>
                            <li>
                                <a href="/admin/promotions/items">
                                    <i class="fas fa-bullhorn mr-2"></i> 推動方案
                                </a>
                            </li>
                            <li>
                                <a href="/admin/news/items">
                                    <i class="fas fa-newspaper mr-2"></i> 最新消息
                                </a>
                            </li>
                            <li>
                                <a href="/admin/downloads">
                                    <i class="fas fa-download mr-2"></i> 下載專區
                                </a>
                            </li>
                            <li>
                                <a href="/admin/faq/items">
                                    <i class="fas fa-question-circle mr-2"></i> 常見問題
                                </a>
                            </li>
                            <li>
                                <a href="/admin/contact">
                                    <i class="fas fa-envelope mr-2"></i> 聯絡我們
                                </a>
                            </li>
                            <li>
                                <a href="/admin/pages/edit/website-security-policy">
                                    <i class="fas fa-file-alt mr-2"></i> 網站安全政策
                                </a>
                            </li>
                            <li>
                                <a href="/admin/pages/edit/privacy-policy">
                                    <i class="fas fa-file-alt mr-2"></i> 隱私權政策
                                </a>
                            </li>
                            <li>
                                <a href="/admin/pages/edit/government-open-data-declaration">
                                    <i class="fas fa-file-alt mr-2"></i> 政府網站資料開放宣告
                                </a>
                            </li>
                        </ul>
                    </li>
                    <% } 
                    
                    if (user.role === 'editor') { %>
                    <!-- Section 2: 編輯頁面 - For editors, only show permitted pages -->
                    <li class="sidebar-section">
                        <div class="dropdown-toggle" data-target="dropdown-pages">
                            編輯頁面
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <ul class="dropdown-content" id="dropdown-pages">
                            <li>
                                <a href="/admin/news/items">
                                    <i class="fas fa-newspaper mr-2"></i> 最新消息
                                </a>
                            </li>
                            <li>
                                <a href="/admin/downloads">
                                    <i class="fas fa-download mr-2"></i> 下載專區
                                </a>
                            </li>
                            <li>
                                <a href="/admin/faq/items">
                                    <i class="fas fa-question-circle mr-2"></i> 常見問題
                                </a>
                            </li>
                        </ul>
                    </li>
                    <li class="sidebar-section">
                        <div class="dropdown-toggle" data-target="dropdown-users">
                            使用者管理
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <ul class="dropdown-content" id="dropdown-users">
                                <li>
                                    <a href="/admin/users/edit/<%= user.id %>">
                                        <i class="fas fa-user-edit mr-2"></i> 編輯個人資料
                                    </a>
                                </li>
                        </ul>
                    </li>
                    <% } %>

                    <% if (user.role === 'super_admin' || user.role === 'admin' || user.role === 'guest' || user.role === 'editor' || user.role === 'visitor') { %>
                    <!-- Section 6: 訪廠資訊 -->
                    <li class="sidebar-section">
                        <div class="dropdown-toggle" data-target="dropdown-visit">
                            訪廠資訊
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <ul class="dropdown-content" id="dropdown-visit">
                            <li>
                                <a href="/admin/visits">
                                    <i class="fas fa-building mr-2"></i> 訪廠資訊
                                </a>
                            </li>
                        </ul>
                    </li>
                    <% } %>

                    <% if (user.role === 'super_admin' || user.role === 'admin') { %>
                    <!-- Section 3: 臺灣智慧系統 -->
                    <li class="sidebar-section">
                        <div class="dropdown-toggle" data-target="dropdown-faq">
                            臺灣智慧系統
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <ul class="dropdown-content" id="dropdown-faq">
                            <li>
                                <a href="/admin/platforms">
                                    <i class="fas fa-folder mr-2"></i> 整合製造平台
                                </a>
                            </li>
                            <li>
                                <a href="/admin/platforms/categories">
                                    <i class="fas fa-tags mr-2"></i> 平台分類管理
                                </a>
                            </li>
                            <li>
                                <a href="/admin/platforms/attachment-categories">
                                    <i class="fas fa-folder-open mr-2"></i> 附件分類管理
                                </a>
                            </li>
                            <li>
                                <a href="/admin/platforms/attachment-groups">
                                    <i class="fas fa-layer-group mr-2"></i> 附件分組管理
                                </a>
                            </li>
                            <li>
                                <a href="/admin/partners">
                                    <i class="fas fa-list-alt mr-2"></i> 合作夥伴
                                </a>
                            </li>
                        </ul>
                    </li>

                    <!-- Section 4: 編輯橫幅 -->
                    <li class="sidebar-section">
                        <div class="dropdown-toggle" data-target="dropdown-banners">
                            編輯橫幅
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <ul class="dropdown-content" id="dropdown-banners">
                            <li>
                                <a href="/admin/pageImages">
                                    <i class="fas fa-paper-plane mr-2"></i> 編輯橫幅
                                </a>
                            </li>
                        </ul>
                    </li>
                    <% } %>

                    <!-- Section 5: 使用者管理 -->
                    <% if (user.role === 'super_admin' || user.role === 'admin') { %>
                    <li class="sidebar-section">
                        <div class="dropdown-toggle" data-target="dropdown-users">
                            使用者管理
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <ul class="dropdown-content" id="dropdown-users">
                            <li>
                                <a href="/admin/users">
                                    <i class="fas fa-users mr-2"></i> 權限設定
                                </a>
                            </li>
                            <li>
                                <a href="/admin/projects">
                                    <i class="fas fa-tasks mr-2"></i> 計畫管理
                                </a>
                            </li>
                        </ul>
                    </li>
                    <% } %>

                    <% if (user.role === 'super_admin' || user.role === 'admin' || user.role === 'guest') { %>
                    <!-- Section 7: 系統設定 -->
                    <% if (user.role && user.role==='super_admin' || user.role === 'admin' ) { %>
                    <li class="sidebar-section">
                        <div class="dropdown-toggle" data-target="dropdown-system">
                            系統設定
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <ul class="dropdown-content" id="dropdown-system">
                            <li>
                                <a href="/admin/site-settings">
                                    <i class="fas fa-cog mr-2"></i> 網站設定
                                </a>
                            </li>
                        </ul>
                    </li>
                    <% } %>

                    <% if (user.role === 'guest') { %>
                    <!-- Section for 訪客: Personal Info -->
                    <li class="sidebar-section">
                        <div class="dropdown-toggle" data-target="dropdown-personal">
                            個人資料
                            <i class="fas fa-chevron-down"></i>
                        </div>
                        <ul class="dropdown-content" id="dropdown-personal">
                            <li>
                                <a href="/admin/users/edit/<%= user.id %>">
                                    <i class="fas fa-user-edit mr-2"></i> 編輯個人資料
                                </a>
                            </li>
                        </ul>
                    </li>
                    <% } %>
                    <% } %>
                </ul>
            </div>
        <% } else { %>
            <div class="p-4">
                <a href="/admin/login" class="block px-4 py-2 text-gray-600 hover:bg-gray-100 hover:text-gray-800 rounded">
                    <i class="fas fa-sign-in-alt mr-2"></i> 登入
                </a>
            </div>
        <% } %>
    </nav>

    <!-- Mobile menu button -->
    <div class="md:hidden fixed top-0 left-0 z-50 p-4">
        <button id="mobile-menu-button" class="text-white bg-neutral-800 p-2 rounded-md">
            <i class="fas fa-bars"></i>
        </button>
    </div>

    <!-- Main Content -->
    <main class="main-content p-4">
        <% if (typeof success_msg !=='undefined' && success_msg.length> 0) { %>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                <p>
                    <%= success_msg %>
                </p>
            </div>
        <% } %>
        <% if (typeof error_msg !=='undefined' && error_msg.length> 0) { %>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                <p>
                    <%= error_msg %>
                </p>
            </div>
        <% } %>

        <!-- Inject the template content here -->
        <%- body %>
    </main>

    <!-- Base Scripts -->
    <script>
        // Toggle mobile menu
        document.addEventListener('DOMContentLoaded', function () {
            // Mobile menu toggle
            const mobileMenuButton = document.getElementById('mobile-menu-button');
            const sidebar = document.querySelector('.sidebar');

            if (mobileMenuButton && sidebar) {
                mobileMenuButton.addEventListener('click', function () {
                    sidebar.classList.toggle('open');
                });
            }

            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function (event) {
                if (window.innerWidth <= 768 &&
                    !sidebar.contains(event.target) &&
                    event.target !== mobileMenuButton) {
                    sidebar.classList.remove('open');
                }
            });

            // Dropdown toggle functionality
            const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
            
            // Function to save dropdown states to localStorage
            function saveDropdownState(targetId, isOpen) {
                try {
                    // Get existing state
                    let sidebarState = JSON.parse(localStorage.getItem('adminSidebarState')) || {};
                    // Update state for this dropdown
                    sidebarState[targetId] = isOpen;
                    // Save back to localStorage
                    localStorage.setItem('adminSidebarState', JSON.stringify(sidebarState));
                } catch (error) {
                    console.error('Error saving sidebar state:', error);
                }
            }
            
            // Function to load dropdown states from localStorage
            function loadDropdownStates() {
                try {
                    const sidebarState = JSON.parse(localStorage.getItem('adminSidebarState')) || {};
                    
                    // Apply saved states to dropdowns
                    dropdownToggles.forEach(toggle => {
                        const targetId = toggle.getAttribute('data-target');
                        const targetContent = document.getElementById(targetId);
                        
                        if (targetContent && sidebarState[targetId]) {
                            // If this dropdown was open, restore its state
                            toggle.classList.add('active');
                            targetContent.classList.add('show');
                        }
                    });
                } catch (error) {
                    console.error('Error loading sidebar state:', error);
                }
            }
            
            // Mark current section as active based on URL
            function markCurrentSection() {
                const currentPath = window.location.pathname;
                
                // Get all menu links
                const menuLinks = document.querySelectorAll('.dropdown-content a');
                let foundActive = false;
                
                // Look through all links to find matching URL
                menuLinks.forEach(link => {
                    const linkHref = link.getAttribute('href');
                    if (currentPath.includes(linkHref) && linkHref !== '/') {
                        // Found a matching link
                        foundActive = true;
                        
                        // Get the parent dropdown content and its toggle
                        const dropdownContent = link.closest('.dropdown-content');
                        if (dropdownContent) {
                            const dropdownId = dropdownContent.getAttribute('id');
                            const toggle = document.querySelector(`.dropdown-toggle[data-target="${dropdownId}"]`);
                            
                            if (toggle && dropdownContent) {
                                toggle.classList.add('active');
                                dropdownContent.classList.add('show');
                                
                                saveDropdownState(dropdownId, true);
                            }
                            
                            link.classList.add('text-blue-600', 'font-medium');
                        }
                    }
                });
                
                return foundActive;
            }
            
            // Apply click handlers to dropdowns
            dropdownToggles.forEach(toggle => {
                toggle.addEventListener('click', function () {
                    const targetId = this.getAttribute('data-target');
                    const targetContent = document.getElementById(targetId);

                    // Toggle active class on the toggle button
                    this.classList.toggle('active');

                    // Toggle show class on the dropdown content
                    if (targetContent) {
                        targetContent.classList.toggle('show');
                        // Save the new state
                        saveDropdownState(targetId, targetContent.classList.contains('show'));
                    }
                });
            });
            
            const foundActive = markCurrentSection();
            
            if (!foundActive) {
                loadDropdownStates();
            }
        });
    </script>

    <!-- Additional Scripts -->
    <%- script %>
</body>

</html>