<!DOCTYPE html>
<html lang="<%= currentLanguage === 'tw' ? 'zh-TW' : 'en' %>" class="<%= currentLanguage === 'tw' ? 'tw' : 'en' %>">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>
    <%= typeof title !=='undefined' ? title + ' - ' : '' %>
        <%= siteSettings && currentLanguage==='en' ? siteSettings.site_name_en : siteSettings && currentLanguage==='tw'
            ? siteSettings.site_name_tw : '大南方新矽谷推動辦公室' %>
</title>

    <!-- splidejs -->
    <script src="/js/<EMAIL>"></script>
    <link href="/css/<EMAIL>" rel="stylesheet">
    <!-- <script src="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/js/splide.min.js"></script> -->
    <!-- <link href="https://cdn.jsdelivr.net/npm/@splidejs/splide@4.1.4/dist/css/splide.min.css" rel="stylesheet"> -->

    <!-- Google Fonts -->
    <link href="/css/font-google.css" rel="stylesheet">
    <!-- <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;500;600;700&display=swap" rel="stylesheet"> -->

    <!-- Tailwind CSS -->
    <link href="/css/<EMAIL>" rel="stylesheet">
    <!-- <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet"> -->

    <!-- Include Quill.js 2.0.3 for rendering -->
    <link href="/css/<EMAIL>" rel="stylesheet">
    <script src="/js/<EMAIL>"></script>
    <!-- <link href="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.snow.css" rel="stylesheet"> -->
    <!-- <script src="https://cdn.jsdelivr.net/npm/quill@2.0.3/dist/quill.min.js"></script> -->    
    
    <!-- CSS -->
    <link rel="stylesheet" href="/css/main.css">

    <%- defineContent('head') %>

        <!-- Custom styles -->
        <style>
            .skip-link {
                top: -8rem;
            }
            .skip-link:focus {
                top: 0;
                box-shadow: 0 0 0 2px #000, 0 0 0 4px #fff;
            }
            /* Remove platform embed custom styling to use default rendering */
            .platform-embed-container {
                /* Reset any custom styling */
                margin: 0;
                border: none;
                border-radius: 0;
                box-shadow: none;
                background-color: transparent;
            }
        
            .platform-embed-header {
                background-color: transparent;
                padding: 0;
                border-bottom: none;
            }
        
            .platform-embed-body {
                padding: 0;
                background-color: transparent;
            }
        
            /* Ensure images are responsive */
            .promotion-content img {
                max-width: 100%;
                height: auto;
            }
        
            /* Preserve Quill.js styling */
            .promotion-content .ql-editor {
                padding: 0;
            }
        
            /* Override any Quill container styles that might interfere */
            .promotion-content .ql-container {
                border: none !important;
            }
        </style>
</head>

<body class="bg-white">
    <!-- 無障礙跳轉到主要內容 -->
    <a href="#main-content" class="skip-link absolute bg-white p-10 z-50">
        <%= currentLanguage==='en' ? 'Skip to main content' : '跳到主要內容' %>
    </a>
    <!-- Header -->
    <header class="fixed top-0 left-0 w-full bg-white shadow-sm z-40">
        <nav class="px-4 py-3">
            <div class="flex items-center justify-between">
                <!-- New logo -->
                <% if (siteSettings) { %>
                    <a href="/<%= currentLanguage %>" rel="noopener noreferrer">
                        <picture>
                            <% if (siteSettings.logo_mobile_path) { %>
                                <source media="(max-width: 640px)" srcset="/<%= siteSettings.logo_mobile_path %>">
                                <% } %>
                                    <% if (siteSettings.logo_tablet_path) { %>
                                        <source media="(max-width: 1024px)" srcset="/<%= siteSettings.logo_tablet_path %>">
                                        <% } %>
                                            <% if (siteSettings.logo_desktop_path) { %>
                                                <source media="(min-width: 1025px)" srcset="/<%= siteSettings.logo_desktop_path %>">
                                                <% } %>
                                                    <img src="/<%= siteSettings.logo_desktop_path || 'images/logo-placeholder.png' %>"
                                                        alt="<%= currentLanguage === 'en' ? (siteSettings.logo_alt_en || siteSettings.site_name_en) : (siteSettings.logo_alt_tw || siteSettings.site_name_tw) %>"
                                                        class="max-h-12">
                        </picture>
                    </a>
                <% } else { %>
                    <a href="/<%= currentLanguage %>" class="flex items-center" rel="noopener noreferrer">
                        <img src="/images/desktop/logo-STSVPO_v2.png" alt="<%= currentLanguage==='en' ? 'Southern Taiwan Silicon Valley Promotion Office (STSVPO) Header Logo' : '大南方新矽谷推動辦公室頁首標誌' %>" class="h-8 lg:h-8">
                    </a>
                <% } %>
                <div class="flex">
                    <!-- Combined Navigation Links -->
                    <div id="nav-menu"
                        class="hidden absolute top-full left-0 w-full bg-white lg:static lg:flex lg:w-auto lg:items-center lg:space-x-8">
                        <div class="p-0 space-y-1 lg:flex lg:space-x-8 lg:space-y-0 lg:p-0">
                            <!-- <a href="/<%= currentLanguage %>" class="headerLink" rel="noopener noreferrer">
                                <%= currentLanguage==='en' ? 'Home' : '首頁' %>
                            </a> -->
                            <!-- 首頁下拉選單 -->
                            <div class="relative dropdown">
                                <button type="button" href="/<%= currentLanguage %>/" class="headerLink flex items-center" id="homeDropdownButton">
                                    <%= currentLanguage==='en' ? 'Home' : '首頁' %>
                                </button>
                                <!-- 下拉選單內容 -->
                                <div class="lg:absolute lg:left-0 lg:mt-2 lg:w-48 hidden dropdown-menu lg:z-50 lg:shadow-lg lg:rounded-md lg:bg-white static">
                                    <div class="lg:py-1 px-4 lg:px-0">
                                        <a href="/<%= currentLanguage %>/#about" class="headerLink dropdown-item block py-2 px-4 text-sm hover:bg-gray-100 lg:hover:bg-gray-100">
                                        <%= currentLanguage==='en' ? 'About' : '關於大南方' %>
                                        </a>
                                        <a href="/<%= currentLanguage %>/#promotion" class="headerLink dropdown-item block py-2 px-4 text-sm hover:bg-gray-100 lg:hover:bg-gray-100">
                                        <%= currentLanguage==='en' ? 'Promotion' : '推動方案' %>
                                        </a>
                                        <a href="/<%= currentLanguage %>/#news" class="headerLink dropdown-item block py-2 px-4 text-sm hover:bg-gray-100 lg:hover:bg-gray-100">
                                        <%= currentLanguage==='en' ? 'News' : '最新消息' %>
                                        </a>
                                        <a href="/<%= currentLanguage %>/#partners" class="headerLink dropdown-item block py-2 px-4 text-sm hover:bg-gray-100 lg:hover:bg-gray-100">
                                            <%= currentLanguage==='en' ? 'Partners' : '合作伙伴' %>
                                        </a>
                                        <a href="/<%= currentLanguage %>/#links" class="headerLink dropdown-item block py-2 px-4 text-sm hover:bg-gray-100 lg:hover:bg-gray-100">
                                            <%= currentLanguage==='en' ? 'Links' : '相關連結' %>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <a href="/<%= currentLanguage %>/about" class="headerLink" rel="noopener noreferrer">
                                <%= currentLanguage==='en' ? 'About Us' : '關於我們' %>
                            </a>
                            <a href="/<%= currentLanguage %>/promotions" class="headerLink" rel="noopener noreferrer">
                                <%= currentLanguage==='en' ? 'Promotions' : '推動方案' %>
                            </a>
                            <a href="/<%= currentLanguage %>/news" class="headerLink" rel="noopener noreferrer">
                                <%= currentLanguage==='en' ? 'News' : '最新消息' %>
                            </a>
                            <a href="/<%= currentLanguage %>/downloads" class="headerLink" rel="noopener noreferrer">
                                <%= currentLanguage==='en' ? 'Downloads' : '下載專區' %>
                            </a>
                            <a href="/<%= currentLanguage %>/faq" class="headerLink" rel="noopener noreferrer">
                                <%= currentLanguage==='en' ? 'FAQ' : '常見問題' %>
                            </a>
                            <a href="/<%= currentLanguage %>/contact" class="headerLink" rel="noopener noreferrer">
                                <%= currentLanguage==='en' ? 'Contact' : '聯絡我們' %>
                            </a>
                        </div>
                    </div>

                    <!-- Language Switcher -->
                    <%- include('../partials/language-switcher') %>

                    <!-- Mobile Menu Button -->
                    <button class="lg:hidden w-10 h-10 flex items-center justify-center"
                        onclick="document.getElementById('nav-menu').classList.toggle('hidden')" type="button">
                        <span class="hidden"><%= currentLanguage==='en' ? 'Menu' : '選單' %></span>
                        <svg class="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            alt="<%= currentLanguage==='en' ? 'Menu' : '選單' %>">
                            <path d="M4 6h16M4 12h16M4 18h16" stroke-linecap="round" />
                        </svg>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main id="main-content" class="">
        <!-- Flash Messages -->
        <% if (typeof success_msg !=='undefined' && success_msg.length> 0) { %>
            <div class="bg-green-100 border-l-4 border-green-500 text-green-700 p-4 mb-4" role="alert">
                <%= success_msg %>
            </div>
        <% } %>
        <% if (typeof error_msg !=='undefined' && error_msg.length> 0) { %>
            <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4" role="alert">
                <%= error_msg %>
            </div>
        <% } %>

        <!-- Page Image -->
        <% if (typeof pageImage !=='undefined' && pageImage) { %>
            <section class="relative">
                <picture class="block w-full h-48 sm:h-64">
                    <% if (pageImage.pathDesktop) { %>
                        <source media="(min-width: 1024px)" srcset="<%= pageImage.pathDesktop %>">
                    <% } %>
                    <% if (pageImage.pathTablet) { %>
                        <source media="(min-width: 640px)" srcset="<%= pageImage.pathTablet %>">
                    <% } %>
                    <% if (pageImage.pathMobile) { %>
                        <img class="w-full h-full object-cover" src="<%= pageImage.pathMobile %>" alt="">
                    <% } %>
                </picture>
                <div class="absolute inset-0 flex flex-col justify-center px-6 sm:px-16 lg:px-32">
                    <h1 class="h2 text-white">
                        <%= title %>
                    </h1>
                    <p class="text-white">/ <%= currentLanguage==='en' ? 'Home' : '首頁' %> - <%= title %> /</p>
                </div>
            </section>
        <% } %>

        <!-- Page Content -->
        <%- body %>
    </main>

        <!-- Footer -->
        <footer class="bg-gray-100 px-8 py-6 rounded-t-3xl">
            <div class="relative">
                <!-- Organization Info -->
                <div class="">
                    <img src="/images/desktop/logo-STSVPO_v2.png" alt="<%= currentLanguage==='en' ? 'Southern Taiwan Silicon Valley Promotion Office (STSVPO) Footer Logo' : '大南方新矽谷推動辦公室頁尾標誌' %>" class="h-10 mb-3">
                    <p class="">
                        <%= currentLanguage==='en' ? 'Southern Taiwan Silicon Valley Promotion Office (STSVPO)'
                            : '「大南方新矽谷」推動辦公室 (STSVPO)' %>
                    </p>
                    <p class="">
                        <%= currentLanguage==='en'
                            ? 'Address: 5F, Room D503-1, No. 6, Section 1, Guiren 13th Road, Guiren District, Tainan City 711, Taiwan(資安暨智慧科技研發大樓)'
                            : '地址：711臺南市歸仁區歸仁十三路一段6號5樓D503-1室(資安暨智慧科技研發大樓)' %>
                    </p>
                    <p class="">
                        <%= currentLanguage==='en' ? 'Tel: +886-6-303-2027' : '電話：(06) 303-2027' %>
                        &nbsp;
                        <%= currentLanguage==='en' ? 'Fax: +886-6-303-2227' : '傳真：(06) 303-2227' %>
                        &nbsp;
                        <%= currentLanguage==='en' ? 'Service Email: <EMAIL>' : '服務信箱：<EMAIL>' %>
                    </p>
                    <p class="">
                    </p>
                    <p class="">
                        <%= `${currentLanguage==='en' ? 'Page Views' : '瀏覽人次'}： ${todayVisitCount || 0}  /  ${totalVisitCount || 0}` %>
                    </p>
                    <p class="">
                        |<a href="/<%= currentLanguage %>/page/sitemap" class="underline" rel="noopener noreferrer">
                            <%= currentLanguage==='en' ? 'Sitemap' : '網站導覽' %>
                        </a>　
                        |<a href="/<%= currentLanguage %>/page/website-security-policy" class="underline" rel="noopener noreferrer">
                            <%= currentLanguage==='en' ? 'Website Security Policy' : '網站安全政策' %>
                        </a>　
                        |<a href="/<%= currentLanguage %>/page/privacy-policy" class="underline" rel="noopener noreferrer">
                            <%= currentLanguage==='en' ? 'Privacy Policy' : '隱私權政策' %>
                        </a>　
                        |<a href="/<%= currentLanguage %>/page/government-open-data-declaration" class="underline" rel="noopener noreferrer">
                            <%= currentLanguage==='en' ? 'Government Open Data Declaration' : '政府網站資料開放宣告' %>
                        </a>　
                        |<span>
                            <%= currentLanguage==='en'
                            ? 'Recommended Browsers: Chrome 120 / Edge 120 / Firefox 120 / Safari 17 or above, Optimal viewing resolution: 1,280 × 800 or higher.'
                            : '建議使用 Chrome 120 / Edge 120 / Firefox 120 / Safari 17 以上版本瀏覽，最佳瀏覽解析度為 1,280 × 800 以上' %>
                        </span>
                    </p>
                </div>

                <!-- Browser Support Info -->
                <!-- <p class="text-right">
                </p> -->

                <!-- Divider -->
                <div class="border-t border-gray-300 my-1"></div>

                <!-- Accessibility Tag -->
                <div class="sm:absolute max-w-max top-0 right-0">
                    <a href="https://accessibility.moda.gov.tw/Applications/Detail?category=20250429154610" title="無障礙網站">
                        <img src="/images/網站無障礙標章+AA等級圖示.png" border="0" width="127" height="40" alt="通過AA無障礙網頁檢測" />
                    </a>
                </div>

                <!-- Copyright Info -->
                <div class="flex flex-col sm:flex-row justify-between">
                    <span> 2025 All Rights Reserved.</span>
                    <span>
                        <%= currentLanguage==='en'
                            ? 'This platform is commissioned and maintained by the Southern Taiwan Silicon Valley Promotion Office. All rights reserved.'
                            : '本平台由「大南方新矽谷」推動辦公室委辦維護管理 版權所有' %>
                    </span>
                </div>
            </div>
        </footer>


    <!-- JavaScript -->
    <script>
        // 等待DOM完全加載
        document.addEventListener('DOMContentLoaded', function () {
            
            // 獲取當前頁面的路徑名稱
            const currentPath = window.location.pathname;
            // 從路徑中提取頁面名稱 (例如從 '/en/about' 提取 'about')
            const pathParts = currentPath.split('/').filter(Boolean);
            const pageName = pathParts.pop() || '';
            // 獲取所有導航連結
            const headerLinks = document.querySelectorAll('.headerLink');
            // 檢查是否在首頁 (路徑為 '/' 或只有語言代碼)
            const isHomePage = pageName === '' || pathParts.length === 0;
            // 遍歷所有導航連結，設置 active 狀態
            headerLinks.forEach(link => {
                const href = link.getAttribute('href');
                if(href==null) return;
                const hrefParts = href.split('/').filter(Boolean);
                const hrefPageName = hrefParts.pop() || '';
                if (isHomePage) {
                    // 首頁情況: 只激活 href 為 '/' 或 '/語言' 的連結
                    if (hrefPageName === '' || hrefParts.length === 0) {
                        link.classList.add('active');
                    }
                } else {
                    // 其他頁面: 當連結包含當前頁面名稱
                    if (pageName === hrefPageName) {
                        link.classList.add('active');
                    }
                }
            });

            ///////////////////////////////////////////////////////////

            // 首頁下拉選單功能
            const homeDropdownButton = document.getElementById('homeDropdownButton');
            if (homeDropdownButton) {
                homeDropdownButton.addEventListener('click', function(event) {
                    // 阻止事件冒泡到文檔
                    event.stopPropagation();
                    console.log("click homeDropdownButton")
                    // 獲取當前點擊的下拉按鈕的父元素
                    const dropdown = this.closest('.dropdown');
                    const dropdownMenu = dropdown.querySelector('.dropdown-menu');
                    const dropdownArrow = dropdown.querySelector('.dropdown-arrow');
                    
                    // 切換顯示/隱藏
                    dropdownMenu.classList.toggle('hidden');
                    dropdownArrow.classList.toggle('rotate');
                    
                    // 點擊其他地方時關閉所有下拉選單
                    document.addEventListener('click', function closeDropdowns(e) {
                    if (!dropdown.contains(e.target)) {
                        dropdownMenu.classList.add('hidden');
                        dropdownArrow.classList.remove('rotate');
                        document.removeEventListener('click', closeDropdowns);
                    }
                    });
                });

                // 為每個子選單項目添加點擊事件，點擊後關閉下拉選單
                const dropdownItems = document.querySelectorAll('.dropdown-item');
                if (dropdownItems && dropdownItems.length > 0) {
                    dropdownItems.forEach(item => {
                        item.addEventListener('click', function() {
                            const dropdown = this.closest('.dropdown');
                            const dropdownMenu = dropdown.querySelector('.dropdown-menu');

                            // 隱藏子選單與漢堡選單
                            // dropdownMenu.classList.add('hidden');
                            document.getElementById('nav-menu').classList.add('hidden')
                        });
                    });
                }
            }

            ///////////////////////////////////////////////////////////

            // 滾動偏移量設定（可調整）
            const scrollOffset = 120;
            // 獲取所有 subtitle 元素
            const subtitles = document.querySelectorAll('.subtitle');
            // 為每個 subtitle 添加點擊事件監聽器
            subtitles.forEach(subtitle => {
                subtitle.addEventListener('click', function (e) {
                    // 阻止默認的錨點行為
                    // e.preventDefault();

                    // 移除所有 subtitle 的活動樣式
                    subtitles.forEach(s => {
                        // 移除活動樣式
                        s.classList.remove('active');
                    });

                    // 為被點擊的元素添加活動樣式
                    this.classList.add('active');

                    const targetId = this.getAttribute('href').substring(1);

                    // 如果需要，可以滾動到目標區域
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - scrollOffset;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });
                    }
                });
            });

            ///////////////////////////////////////////////////////////
            
        });
    </script>
    <%- defineContent('script') %>
</body>

</html>