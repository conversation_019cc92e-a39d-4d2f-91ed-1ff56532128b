<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - Admin Panel</title>
    <!-- Tailwind CSS -->
    <link href="/css/<EMAIL>" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Captcha Styles -->
    <style>
        #randomfield { 
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            
            width: 200px;
            color: black;
            border-color: black;
            text-align: center;
            font-size: 32px;
            font-weight: bold;
            background-color: #f0f0f0;
            position: relative;
            overflow: hidden;
            padding: 8px 12px;
            background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGQAAABkCAYAAABw4pVUAAAABmJLR0QA/wD/AP+gvaeTAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAB3RJTUUH3QYOBzgp9dBvfgAAAB1pVFh0Q29tbWVudAAAAAAAQ3JlYXRlZCB3aXRoIEdJTVBkLmUHAAAEXklEQVR42u3dS28URxTG8f+p7p6xB7CNwDYBnMQkJEQkK1sRIkKyIBISQhG7SFnkA+QrZJ3PEMguuyySBVIUFCWRQIqyIBtiGWwZG+Ljl8C8PDPTXcUik7ke2/N+VLq6NNNdZ56uM4AxY8aMMWOMMWOMMWOMMWOMMWOMMWOMMWOMMWbJkZnYycIbb6RxMuEBa92fvb67k0NB3/vn/n4V59Hpixe7/yUQWYijhDhmEQ5XYXaVQ1urtbiyPJQ1jm3Uqh9XRXJ3hfQR1EIcTXBsCPpWUwJryyMVSL1I/D3J9EyI7ULDLALIRBylQD8BtldhbkZYHx8LjFTl0YlGUXR9nzKUMwWOHSQtIcHLiaMeiL87EIKZHSELiKMWiL85UgS5GaI7E/YTpMcxLOjOuoK+x1gSINM4hoReK9HcS6drhVQcdUUXYWNsjE7XmoCb/3aHR7Vc69s4SkE3NpUc1Lq/O1wgPY5BCXuqMvZA6vXBU9RHITj9D8BkwsFqzB4QxLBwdLpeiA04xkXbhLQtkEFxDEp5kskkT1+LolP7vDM5kDEY7dL44mASVpTK46FQeKyUfh0Psu5Ekn0fOJA+jlER1tWhbYnGUz0HUk6CsXqMvZjJZNc7kwOJ26Vq21sbp1RwIAyJYzTgYAqb3TfCo69AuI9jcLCJJCT2U4A4jmaJ4wVFowW9nQ2FA4yXOdpHQp3A2Aso9mF75RHNEttlqLBMYVWhbzKg1D5J0rN1Rx7I3DhWwMYC7/XpJT2ZIIxXSNaqPvI4puJYK6wSfdI4yDdQkFkc6yVtJjjqgj6twh/lDzwyQHocQ2A7gQeVlIFH9x9XbpdKHGvA3kT0qQR902/SX/9c+pJo7lDiWCFss87XpL5TyO3ywJE4VgkfCPqoQuc7B5LH0Va5KJbWO5A8jmHy/1Xnm2M4kDmO9SU5r2sgcxwv4T25bCAdSDwkfz1aHg7kAY7VIO/lXV52vVD6R1LnQg6kf/PYVY0MiAPp/+jyct+ZAzS7DeTxjSIbhzz7qlxCvOE3wubIgTzc0TFnDiT/o0sOxIHkP7o4EAfi40sOxLMsB2JnDsSzLAfio8SBOBDbHBPEMqwvfXBj0lcrspE9bkhfcfgIcSA2yhyIjzIH4kAciANxIA7EgTgQB+JAHIgDcSAOxIE4EAfiQG5LDuRHmUfEbwMnl+TQUvkpyaF/nZLDz7x1iWkYjpE4Ak5VoT0YUgbZHkUo0lNNKEwHdqbp5fvpqcWK40DSz1dIpJ9hS9+BAPy0a9fTDiW9ky9J+nJ+Y9vYKHEoeXNMb5e7EHiPh2uSfnIc+Y4tF9Nb9ZCfPzfWc18vBG78Dlu6fMnZcRgzZsyYMWPGjBkzZsyYMWPGjBkzZsyYMWPGjBkzZu7E/AOXxK0vniCTnwAAAABJRU5ErkJggg==');
            background-repeat: repeat;
        }
        
        .captcha-char {
            display: inline-block;
            margin: 0 2px;
            transform-origin: center;
            font-size: 38px;
        }
        
        .captcha-italic {
            font-style: italic;
        }
        
        .captcha-bold {
            font-weight: 900;
        }
        
        .captcha-underline {
            text-decoration: underline;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center">
        <!-- Flash Messages -->
        <% if (typeof success_msg !== 'undefined' && success_msg.length > 0) { %>
            <div class="fixed top-4 right-4 bg-green-100 border-l-4 border-green-500 text-green-700 p-4" role="alert">
                <%= success_msg %>
            </div>
        <% } %>
        <% if (typeof error_msg !== 'undefined' && error_msg.length > 0) { %>
            <div class="fixed top-4 right-4 bg-red-100 border-l-4 border-red-500 text-red-700 p-4" role="alert">
                <%= error_msg %>
            </div>
        <% } %>

        <!-- Page Content -->
        <%- body %>
    </div>
    
    <!-- Captcha Scripts -->
    <script>
        function ChangeCaptcha() {
            var chars = "0123456789abcdefghiklmnopqrstuvwxyz"; 
            var string_length = 6;
            var captchaString = '';
            
            // Generate the captcha string
            for (var i=0; i<string_length; i++) {
                var rnum = Math.floor(Math.random() * chars.length);
                captchaString += chars.substring(rnum,rnum+1);
            }
            
            // Store the value in the input field for validation
            document.getElementById('randomfield').value = captchaString;
            
            // Now let's create a fancy display with random styling
            var captchaDisplay = document.getElementById('captchaDisplay');
            if (captchaDisplay) {
                captchaDisplay.innerHTML = '';
                
                for (var i = 0; i < captchaString.length; i++) {
                    var span = document.createElement('span');
                    span.className = 'captcha-char';
                    span.textContent = captchaString[i];
                    
                    // Random rotation
                    var rotation = Math.floor(Math.random() * 40) - 20; // -20 to 20 degrees
                    span.style.transform = 'rotate(' + rotation + 'deg)';
                    
                    // Random styling
                    if (Math.random() > 0.5) span.classList.add('captcha-italic');
                    if (Math.random() > 0.5) span.classList.add('captcha-bold');
                    if (Math.random() > 0.5) span.classList.add('captcha-underline');
                    
                    // Random color
                    var colors = ['#000', '#333', '#555', '#111', '#222', '#444'];
                    span.style.color = colors[Math.floor(Math.random() * colors.length)];
                    
                    captchaDisplay.appendChild(span);
                }
            }
        }
        
        function validateCaptcha() {
            if(document.getElementById('CaptchaEnter').value != document.getElementById('randomfield').value) {
                alert('驗證碼錯誤，請重試');
                return false;
            }
            return true;
        }
        
        // Initialize captcha when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            if (document.getElementById('randomfield')) {
                ChangeCaptcha();
            }
        });
    </script>
    <%- typeof scripts !== 'undefined' ? scripts : '' %>
</body>
</html>
