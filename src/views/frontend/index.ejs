<%- contentFor('body') %>
<!-- 新版 - 首頁橫幅, 桌面, 平板, 手機 -->
<!-- Slide Section -->
<section class="splide">
    <% if (banners && banners.length> 0) { %>
        <div class="splide__track">
            <ul class="splide__list">
                <% banners.forEach((banner, index)=> { %>
                    <!-- Slide 1 -->
                    <li class="splide__slide">
                        <div class="slide flex relative">
                            <div class="w-full">
                                <% if (banner.mediaType==='image' ) { %>
                                    <picture class="block w-full h-full">
                                        <% if (banner.mediaPathDesktop) { %>
                                            <source media="(min-width: 1024px)" srcset="<%= banner.mediaPathDesktop %>">
                                        <% } %>
                                        <% if (banner.mediaPathTablet) { %>
                                            <source media="(min-width: 640px)" srcset="<%= banner.mediaPathTablet %>">
                                        <% } %>
                                        <% if (banner.mediaPathMobile) { %>
                                            <source srcset="<%= banner.mediaPathMobile %>">
                                        <% } %>
                                        <img src="<%= banner.mediaPath %>" alt="<%= banner.title %>" class="w-full h-full object-cover">
                                    </picture>
                                <% } else if (banner.mediaType==='video' ) { %>
                                    <video id="video-<%= index %>" autoplay muted loop playsinline controls
                                        class="w-full h-full object-cover" aria-label="介紹影片">
                                        <source src="<%= banner.mediaPath %>" type="video/mp4">
                                        Your browser does not support the video tag.
                                    </video>
                                <% } %>
                            </div>
                            <!-- 隱形線包含文字內容，垂直置中 -->
                            <div class="absolute bottom-52 lg:inset-y-0 left-8 sm:left-16 flex items-center">
                                <div class="relative">
                                    <!-- h1 貼齊隱形線上緣 -->
                                    <h2 class="h1 absolute bottom-0 mb-4 w-full text-white"><%= banner.title %></h2>
                                    <!-- 隱形線 -->
                                    <div class="banner-title"></div>
                                    <!-- p 貼齊隱形線下緣 -->
                                    <% if (banner.description) { %>
                                        <h3 class="h3 absolute top-0 mt-0 w-full text-white"><%= banner.description %></h3>
                                    <% } %>
                                   
                                </div>
                            </div>
                            <% if (banner.url) { %>
                                <a href="<%= banner.url %>" target="_blank" title="<%= currentLanguage==='en' ? 'New window' : '另開新視窗' %>" rel="noopener noreferrer" class="absolute top-0 block w-full h-full text-transparent" title="<%= banner.title %>">
                                    <%= banner.title %>
                                </a>
                            <% } %>
                        </div>
                    </li>
                <% }); %>
            </ul>
        </div>
    <% } %>
    <div class="absolute bottom-0 w-full h-full bg-white" id="splideLoader"></div>
    <div class="absolute bottom-0 w-full flex flex-col sm:flex-row justify-center">
        <a href="https://stb.stpi.narl.org.tw/page/stationed" target="_blank" title="<%= currentLanguage==='en' ? 'New window' : '另開新視窗' %>" class="inline-block max-w-xl px-3 py-2 mx-10 sm:px-12 sm:py-3 sm:mx-8 sm:w-1/2 mb-2 sm:mb-0 banner-button banner-button-yellow flex items-center justify-center">
            <span class="tracking-wider">
                <%= currentLanguage==='en' ? 'Full Service for Tenants' : '全區進駐服務' %>
            </span>
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" class="arrow-icon">
                <polygon points="11.293 4.707 17.586 11 4 11 4 13 17.586 13 11.293 19.293 12.707 20.707 21.414 12 12.707 3.293 11.293 4.707"/>
            </svg>
        </a>
        <a href="/<%= currentLanguage %>/promotions" target="_blank" title="<%= currentLanguage==='en' ? 'New window' : '另開新視窗' %>" class="inline-block max-w-xl px-3 py-2 mx-10 sm:px-12 sm:py-3 sm:mx-8 sm:w-1/2 banner-button banner-button-lightgreen flex items-center justify-center no-underline">
            <span class="tracking-wider">
                <%= currentLanguage==='en' ? 'Industry Promotion & Cooperation' : '產業推廣合作' %>
            </span>
            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" class="arrow-icon">
                <polygon points="11.293 4.707 17.586 11 4 11 4 13 17.586 13 11.293 19.293 12.707 20.707 21.414 12 12.707 3.293 11.293 4.707"/>
            </svg>
        </a>
    </div>
</section>

<!-- Promotion About Section -->
<% if (plainTextItems && plainTextItems.length> 0) { %>
    <% plainTextItems.forEach(function(item) { %>
        <section id="about" class="bg-promotion bg-center bg-cover py-12 md:py-24 px-8 md:px-16 lg:px-24">
            <div class="flex flex-col lg:flex-row items-center mb-16">
                <div class="w-full lg:w-1/2 mb-0">
                    <!-- <h2 class="mb-4 text-center lg:text-left"><%= currentLanguage==='en' ? item.title_en : item.title_tw %></h2> -->
                    <h1 class="h2 mb-4 text-center lg:text-left">
                        <%= currentLanguage==='en' ? 'About Southern Taiwan Silicon Valley' : 
                        '關於大南方新矽谷' %>
                        <br>
                        <%= currentLanguage==='en' ? 'Promotion Plan' : 
                        '推動方案' %>
                    </h1>
                    <div class="gradient-line w-24 h-2 mx-auto lg:mx-0 mb-12"></div>
                </div>
                <div class="w-full lg:w-1/2 flex justify-center">
                    <video autoplay muted loop playsinline controls class="w-full h-full object-cover img-shadow"
                        poster="/images/desktop/contentImg.jpg" aria-label="推動方案影片">
                        <source src="/images/desktop/about_great_south.mp4" type="video/mp4">
                    </video>
                    <!-- <img class="img-shadow" src="/images/desktop/contentImg.jpg" alt=""> -->
                </div>
            </div>
            <div class="w-full">
                <p class=""><%- currentLanguage==='en' ? item.content_en : item.content_tw %></p>
            </div>
        </section>
    <% }); %>
<% } %>

<!-- Promotion Link Section -->
<% if (pictureItems && pictureItems.length> 0) { %>
    <% pictureItems.forEach(function(item) { %>
        <section id="promotion" class="bg-plan bg-center bg-cover py-16 px-6 md:px-16">
            <h3 class="text-center mb-10"><%= currentLanguage==='en' ? item.title_en : item.title_tw %></h3>
            <% if (item.images && item.images.length> 0) { %>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-x-14 gap-y-24 sm:gap-10">
                    <% item.images.forEach(function(image, index) { %>
                        <% if (image.url) { %>
                            <a href="<%= image.url %>" target="_blank" title="<%= currentLanguage==='en' ? 'New window' : '另開新視窗' %>" rel="noopener noreferrer" class="flex justify-center">
                                <img src="<%= image.path %>" alt="<%= image.alt || (currentLanguage === 'en' ? item.title_en : item.title_tw) %>" class="w-full rounded-2xl cursor-pointer transition-all duration-300 transform hover:-translate-y-1 promotion-link">
                            </a>
                        <% } else { %>
                            <div class="flex justify-center">
                                <img src="<%= image.path %>" alt="<%= image.alt || (currentLanguage === 'en' ? item.title_en : item.title_tw) %>" class="w-full rounded-2xl transition-all duration-300 transform hover:-translate-y-1 promotion-link">
                            </div>
                        <% } %>
                    <% }); %>
                </div>
            <% } else { %>
                <div class="text-center text-gray-500">
                    <!-- Debug message to see if images are being retrieved -->
                    <p>No images found for this language (<%= currentLanguage %>)</p>
                </div>
            <% } %>
        </section>
    <% }); %>
<% } %>

<!-- Latest News Section  首頁讀取最新消息 6筆-->
<% if (latestNews && latestNews.length> 0) { %>
    <section id="news" class="bg-news bg-cover py-16 px-6 md:px-16 sm:bg-none">
        <div class="flex flex-col sm:flex-row gap-8">
            <!-- Left: Image (hidden on mobile) -->
            <div class="hidden sm:block sm:w-1/2 flex items-center justify-center relative overflow-hidden">
                <div class="flex items-center justify-center h-full">
                    <!-- 背景圖片 -->
                    <img src="/images/desktop/newslgbg.png" alt="火箭背景" class="w-full h-auto rounded-lg">
                    <!-- 新圖片帶動畫效果 - 從左下移動到右上 -->
                    <img src="/images/desktop/newsrocket.png" alt="火箭" 
                    class="absolute w-1/3 h-auto transform origin-center-left animate-move-diagonal opacity-90">
                </div>
            </div>
            <!-- Right: News List -->
            <div class="w-full sm:w-1/2">
                <h3 class="mb-2 text-center sm:text-left">
                    <%= currentLanguage==='en' ? 'Latest News' : '最新消息' %>
                </h3>
                <div class="yellow-line w-24 h-2 mx-auto sm:mx-0 mb-8"></div>
                <div class="max-h-96 overflow-y-auto pr-2">
                    <% latestNews.forEach(function(item) { %>
                        <!-- News Item -->
                        <div class="relative border-b border-gray-300 py-2 mb-4">
                            <% if (item.category) { %>
                                <span class="<%= item.category.name_en.includes('ndustry') ? 'bg-custom-lightblue' : 'bg-custom-lightgreen' %> absolute top-1 right-0 text-black px-2 py-0.5 rounded text-xs">
                                    <%= item.category.name %>
                                </span>
                            <% } %>
                            <p class="mb-2">
                                <%= new
                                    Date(item.publishedDate).toLocaleDateString(currentLanguage==='tw'
                                    ? 'zh-TW' : 'en-US' ) %>
                            </p>
                            <a href="<%= item.url %>" target="_blank" title="<%= currentLanguage==='en' ? 'New window' : '另開新視窗' %>" rel="noopener noreferrer" class="no-underline">
                                <p class=""><%= item.title %></p>
                            </a>
                        </div>
                    <% }); %>
                    <a href="/<%= currentLanguage %>/news" class="underline">
                        <%= currentLanguage==='en' ? 'More' : '更多' %>
                    </a>
                </div>
            </div>
        </div>
    </section>
<% } %>

<!-- Partners Section -->
<section id="partners" class="bg-partner bg-no-repeat bg-cover bg-bottom sm:bg-cover sm:bg-center px-6 py-12 sm:px-16 sm:py-20 lg:px-56 lg:py-28">
    <!-- Partner Data Container - Moved before the scripts -->
    <div id="partnersDataContainer" style="display:none;" data-partners='<%- JSON.stringify(partnersByCategory || {}).replace(/'/g, "&#39;") %>' data-language="<%= currentLanguage %>"></div>
    
    <div class="flex flex-col sm:flex-row">
        <div class="hidden sm:block sm:w-3/12 mr-6"></div>
        <!-- 標題 -->
        <div class="w-full sm:w-9/12">
            <img class="max-w-xl mx-auto mb-4 sm:mb-8 w-full h-auto" src="/images/desktop/partnertitle.png" alt="臺灣智慧系統整合製造平台合作伙伴">
            <!-- <h3 class="mb-4 sm:mb-8 text-center">
                <span class="text-white"><%= currentLanguage==='en' ? 'Taiwan Smart System Integration and Manufacturing Platform' : '臺灣智慧系統整合製造平台' %></span>
                <br class="lg:hidden"><span class="text-custom-yellow"><%= currentLanguage==='en' ? 'Partners' : '合作伙伴' %></span>
            </h3> -->
        </div>
    </div>
    <div class="flex flex-col sm:flex-row">
        <!-- 手機版下拉選單 -->
        <div class="block w-full sm:hidden mb-6 p-2 relative bg-white rounded-xl border">
            <label for="categorySelect" class="hidden"><%= currentLanguage==='en' ? "Industry menu" : "產業選單" %></label>
            <select id="categorySelect" name="categorySelect" class="w-full text-gray-600 border-0 focus:ring-0 py-2 pl-2 pr-10 appearance-none">
                <% 
                // Get all categories that have suppliers or buyers
                const activeCategories = Object.keys(partnersByCategory)
                    .filter(key => partnersByCategory[key].suppliers.length > 0 || partnersByCategory[key].buyers.length > 0)
                    .map(key => partnersByCategory[key].category);
                
                // Sort by order instead of name
                activeCategories.sort((a, b) => {
                    // If order exists, sort by order
                    if (a.order !== undefined && b.order !== undefined) {
                        return a.order - b.order;
                    }
                    // Fall back to name if order is not available
                    const nameA = currentLanguage === 'en' ? a.name_en : a.name_tw;
                    const nameB = currentLanguage === 'en' ? b.name_en : b.name_tw;
                    return nameA.localeCompare(nameB);
                });
                
                // Set first one as default if exists
                const firstCategoryId = activeCategories.length > 0 ? activeCategories[0].id : null;
                %>
                
                <% activeCategories.forEach((category, index) => { 
                    const catName = currentLanguage === 'en' ? category.name_en : category.name_tw;
                %>
                    <option value="<%= category.id %>" <%= index === 0 ? 'selected' : '' %>>
                        <%= catName %>
                    </option>
                <% }); %>
            </select>
            <!-- 自定義下拉箭頭 -->
            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4 text-gray-500">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
            </div>
        </div>

        <!-- 左側選單 (Desktop) -->
        <div class="hidden sm:block sm:w-3/12 mr-6">
            <ul class="">
                <% activeCategories.forEach((category, index) => { 
                    const catName = currentLanguage === 'en' ? category.name_en : category.name_tw;
                %>
                    <li class="">
                        <button class="categoryTab block px-3 py-2 mb-2 w-full text-center bg-gray-100 <%= index === 0 ? 'border-l-4 border-custom-yellow' : 'text-gray-500' %>" 
                                type="button" data-category="<%= category.id %>">
                            <%= catName %>
                        </button>
                    </li>
                <% }); %>
            </ul>
        </div>

        <!-- 主內容區 -->
        <div class="w-full sm:w-9/12">
            <!-- 頂部標籤頁 -->
            <div class="bg-white border-2 rounded-xl border-gray-100 p-4 mb-6 min-h-full">
                <div class="flex justify-center">
                    <button class="companyTab px-4 py-2 border-b-2 border-custom-yellow text-custom-blue font-medium">
                        <%= currentLanguage==='en' ? 'Suppliers' : '供應商' %>
                    </button>
                    <button class="companyTab px-4 py-2 border-b-2 border-gray-100 text-gray-500">
                        <%= currentLanguage==='en' ? 'Buyers' : '需求商' %></button>
                </div>

                <% activeCategories.forEach((category, index) => { %>
                    <!-- <%= category.name_en %> 類別 -->
                    <div class="categoryContent <%= index === 0 ? '' : 'hidden' %>" data-category="<%= category.id %>">
                        <!-- 供應商列表 -->
                        <div class="supplierList py-6 grid grid-cols-1 sm:grid-cols-2 gap-x-2 gap-y-4">
                            <!-- 供應商卡片容器 (動態加載) -->
                        </div>
                        
                        <!-- 需求商列表 -->
                        <div class="demanderList py-6 hidden grid-cols-1 sm:grid-cols-2 gap-x-2 gap-y-4">
                            <!-- 需求商卡片容器 (動態加載) -->
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    </div>
</section>

<!-- 相關連結 Related Links Section -->
<% if (links && links.length> 0) { %>
    <section id="links" class="py-12 px-5 lg:px-8">
        <h3 class="text-center mb-2"><%= currentLanguage==='en' ? 'Related Links' : '相關連結' %></h3>
        <div class="yellow-line w-24 h-2 mx-auto mb-16 text-center"></div>
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-2 lg:gap-6">
            <% links.forEach(function(link) { %>
                <!-- Link Item -->
                <a href="<%= link.url %>" class="block no-underline" target="_blank" title="<%= currentLanguage==='en' ? 'New window' : '另開新視窗' %>" rel="noopener noreferrer">
                    <div class="bg-gray-100 h-28 lg:h-36 rounded-xl flex items-center justify-center p-4 sm:p-6">
                        <% if (link.image) { %>
                            <div class="w-full flex items-center justify-center">
                                <img src="<%= link.image %>" alt="<%= currentLanguage==='en' ? link.title_en : link.title_tw %>" 
                                class="h-full w-full object-cover">
                            </div>
                        <% } %>
                    </div>
                </a>
            <% }); %>
        </div>
    </section>
<% } %>

<%- contentFor('scripts') %>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var splide = new Splide( '.splide' ,{
            type    : 'loop',
            autoplay: 'true',
        });
        splide.mount();
            
        // 獲取覆蓋層元素
        const splideLoader = document.getElementById('splideLoader');
        // 使用 requestAnimationFrame 實現平滑的淡出效果
        let opacity = 1;
        const fadeOut = function() {
            opacity -= 0.01; // 每次減少0.01，實現緩慢淡出
            if (opacity <= 0) {
                opacity = 0;
                splideLoader.style.opacity = '0';
                // 當完全透明時移除元素
                setTimeout(function() {
                splideLoader.style.display = 'none';
                }, 100);
                return;
            }
            splideLoader.style.opacity = opacity.toString();
            requestAnimationFrame(fadeOut);
        };
        // 延遲一段時間後開始淡出動畫
        setTimeout(function() {
            requestAnimationFrame(fadeOut);
        }, 500); // 可以調整這個延遲時間

        /////////////////////////////////////////////////////

        // Note: The category and company tab functionality is now handled in the dedicated
        // Partners Section JavaScript block below to avoid duplicate event listeners
        
        /////////////////////////////////////////////////////

    });
</script>

<!-- Partners Section JavaScript and Data Population Script -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Check if the partners data container exists
        const partnersDataContainer = document.getElementById('partnersDataContainer');
        if (!partnersDataContainer) {
            console.warn('Partners data container not found');
            return;
        }

        // Partners data from server
        let partnersData = {};
        try {
            const partnersDataStr = partnersDataContainer.getAttribute('data-partners');
            if (!partnersDataStr) {
                console.warn('No partners data found');
                partnersData = {};
            } else {
                partnersData = JSON.parse(partnersDataStr);
            }
        } catch (error) {
            console.error('Error parsing partners data:', error);
            console.error('Error at position:', error.message.match(/position (\d+)/)?.[1] || 'unknown');
            partnersData = {};
        }
        const currentLanguage = partnersDataContainer.getAttribute('data-language') || 'en';
        
        // Debug: Log the partners data
        console.log('Partners data loaded successfully');
        console.log('Current language:', currentLanguage);
        
        // ====== UI Elements ======
        // 行業別選單和標籤
        const categorySelect = document.getElementById('categorySelect');
        const categoryTabs = document.querySelectorAll('.categoryTab');
        const categoryContents = document.querySelectorAll('.categoryContent');
        const companyTabs = document.querySelectorAll('.companyTab');
        
        // 手機版下拉選單切換行業
        if (categorySelect) {
            categorySelect.addEventListener('change', function() {
                const selectedCategory = String(this.value);
                
                // 隱藏所有內容
                categoryContents.forEach(content => {
                    content.classList.add('hidden');
                });
                
                // 顯示選中的內容
                const targetContent = document.querySelector(`.categoryContent[data-category="${selectedCategory}"]`);
                if (targetContent) {
                    targetContent.classList.remove('hidden');
                }
                
                // 更新 Desktop 選單的選中狀態
                categoryTabs.forEach(tab => {
                    const tabCategory = tab.getAttribute('data-category');
                    if (tabCategory === selectedCategory) {
                        tab.classList.add('border-custom-yellow', 'border-l-4');
                        tab.classList.remove('text-gray-500');
                    } else {
                        tab.classList.add('text-gray-500');
                        tab.classList.remove('border-custom-yellow', 'border-l-4');
                    }
                });
            });
        }
        
        // Desktop 版標籤切換行業
        if (categoryTabs && categoryTabs.length > 0) {
            categoryTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const selectedCategory = String(this.getAttribute('data-category') || '');
                    
                    // 隱藏所有內容
                    categoryContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    
                    // 顯示選中的內容
                    const targetContent = document.querySelector(`.categoryContent[data-category="${selectedCategory}"]`);
                    if (targetContent) {
                        targetContent.classList.remove('hidden');
                    }
                    
                    // 更新標籤樣式
                    categoryTabs.forEach(tab => {
                        if (tab === this) {
                            tab.classList.add('border-custom-yellow', 'border-l-4');
                            tab.classList.remove('text-gray-500');
                        } else {
                            tab.classList.add('text-gray-500');
                            tab.classList.remove('border-custom-yellow', 'border-l-4');
                        }
                    });
                    
                    // 同步手機版下拉選單
                    if (categorySelect) {
                        categorySelect.value = selectedCategory;
                    }
                });
            });
        }
        
        // 供應商/需求商標籤切換
        if (companyTabs && companyTabs.length >= 2) {
            // 初始化狀態 - 確保第一個標籤被選中
            companyTabs[0].classList.add('border-custom-yellow', 'text-custom-blue');
            companyTabs[0].classList.remove('border-gray-100', 'text-gray-500');
            companyTabs[1].classList.add('border-gray-100', 'text-gray-500');
            companyTabs[1].classList.remove('border-custom-yellow', 'text-custom-blue');
            
            // 供應商標籤點擊事件
            companyTabs[0].addEventListener('click', function() {
                // 更新標籤樣式
                this.classList.add('border-custom-yellow', 'text-custom-blue');
                this.classList.remove('border-gray-100', 'text-gray-500');
                companyTabs[1].classList.add('border-gray-100', 'text-gray-500');
                companyTabs[1].classList.remove('border-custom-yellow', 'text-custom-blue');
                
                // 顯示所有供應商列表，隱藏所有需求商列表
                document.querySelectorAll('.supplierList').forEach(list => {
                    list.classList.remove('hidden');
                    list.classList.add('grid');
                });
                document.querySelectorAll('.demanderList').forEach(list => {
                    list.classList.add('hidden');
                    list.classList.remove('grid');
                });
            });
            
            // 需求商標籤點擊事件
            companyTabs[1].addEventListener('click', function() {
                // 更新標籤樣式
                this.classList.add('border-custom-yellow', 'text-custom-blue');
                this.classList.remove('border-gray-100', 'text-gray-500');
                companyTabs[0].classList.add('border-gray-100', 'text-gray-500');
                companyTabs[0].classList.remove('border-custom-yellow', 'text-custom-blue');
                
                // 隱藏所有供應商列表，顯示所有需求商列表
                document.querySelectorAll('.supplierList').forEach(list => {
                    list.classList.add('hidden');
                    list.classList.remove('grid');
                });
                document.querySelectorAll('.demanderList').forEach(list => {
                    list.classList.remove('hidden');
                    list.classList.add('grid');
                });
            });
        }
        
        // ===== Data Population =====
        // Helper to create a company card element
        function createCompanyCard(companyName) {
            const card = document.createElement('div');
            card.className = 'h-full flex items-center justify-center';
            card.innerHTML = `
                <p class="text-center">${companyName}</p>
            `;
            return card;
        }

        // Populate each category content area with suppliers and buyers
        Object.keys(partnersData).forEach(categoryIdRaw => {
            const categoryId = String(categoryIdRaw); // Ensure categoryId is a string
            const categoryData = partnersData[categoryIdRaw];
            if (!categoryData || !categoryData.category) return;
            
            // Debug: Log each category being processed
            console.log(`Processing category ID: ${categoryId}`, categoryData);
            
            // Find the content containers for this category
            const categoryContent = document.querySelector(`.categoryContent[data-category="${categoryId}"]`);
            if (!categoryContent) {
                console.warn(`No container found for category ID: ${categoryId}`);
                return;
            }
            
            // Get supplier and buyer lists
            const supplierList = categoryContent.querySelector('.supplierList');
            const buyerList = categoryContent.querySelector('.demanderList');
            
            // Populate supplier list
            if (supplierList) {
                supplierList.innerHTML = '';
                if (categoryData.suppliers && categoryData.suppliers.length > 0) {
                    console.log(`Found ${categoryData.suppliers.length} suppliers for category ${categoryId}`);
                    categoryData.suppliers.forEach(company => {
                        if (company && company.trim()) {
                            supplierList.appendChild(createCompanyCard(company));
                        }
                    });
                } else {
                    // If no suppliers, show a message
                    console.log(`No suppliers for category ${categoryId}`);
                    const message = document.createElement('div');
                    message.className = 'col-span-2 p-4 text-center text-gray-500';
                    message.textContent = currentLanguage === "en" ? "None" : "無";
                    supplierList.appendChild(message);
                }
            }
            
            // Populate buyer list
            if (buyerList) {
                buyerList.innerHTML = '';
                if (categoryData.buyers && categoryData.buyers.length > 0) {
                    console.log(`Found ${categoryData.buyers.length} buyers for category ${categoryId}`);
                    categoryData.buyers.forEach(company => {
                        if (company && company.trim()) {
                            buyerList.appendChild(createCompanyCard(company));
                        }
                    });
                } else {
                    // If no buyers, show a message
                    console.log(`No buyers for category ${categoryId}`);
                    const message = document.createElement('div');
                    message.className = 'col-span-2 p-4 text-center text-gray-500';
                    message.textContent = currentLanguage === "en" ? "None" : "無";
                    buyerList.appendChild(message);
                }
            }
        });
    });
</script>