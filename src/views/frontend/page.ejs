<%- contentFor('body') %>
    <section class="px-6 py-8 sm:px-16 sm:pb-16 lg:px-32 lg:pb-16">
        <h2 class="h4 text-3xl font-bold mb-6">
            <%= page.title %>
        </h2>

        <div class="bg-white py-6 mb-8">
            <!-- Page Content -->
            <div class="prose max-w-none quill-content">
                <div class="ql-container ql-snow" style="border: none;">
                    <div class="ql-editor quill-content-wrapper" <% if ((currentLanguage==='en' &&
                        page._content_en_is_delta) || (currentLanguage==='tw' && page._content_tw_is_delta)) { %>
                        data-is-delta="true" <% } %> id="quill-content-<%= page.id %>-<%= currentLanguage %>">
                                    <%- page.content %>
                    </div>
                </div>
            </div>
        </div>

        <% if (page.attachments && page.attachments.length> 0) { %>
            <!-- Attachments Section -->
            <div class="mt-8">
                <h2 class="text-2xl font-semibold mb-4">
                    <%= currentLanguage==='en' ? 'Attachments' : '附件' %>
                </h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <% page.attachments.forEach(attachment=> { %>
                        <div class="border rounded-lg p-4 flex items-center">
                            <% if (attachment.mimeType.startsWith('image/')) { %>
                                <div class="w-16 h-16 mr-4 flex-shrink-0">
                                    <img src="<%= attachment.path %>" alt="<%= attachment.originalName %>"
                                        class="w-full h-full object-cover rounded">
                                </div>
                                <% } else if (attachment.mimeType.startsWith('video/')) { %>
                                    <div
                                        class="w-16 h-16 bg-gray-200 flex items-center justify-center mr-4 flex-shrink-0 rounded">
                                        <svg class="w-8 h-8 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
                                            <path
                                                d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zm12.553 1.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z">
                                            </path>
                                        </svg>
                                    </div>
                                    <% } else if (attachment.mimeType==='application/pdf' ) { %>
                                        <div
                                            class="w-16 h-16 bg-gray-200 flex items-center justify-center mr-4 flex-shrink-0 rounded">
                                            <svg class="w-8 h-8 text-gray-500" fill="currentColor"
                                                viewBox="0 0 20 20">
                                                <path fill-rule="evenodd"
                                                    d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z"
                                                    clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                        <% } else if (attachment.mimeType.includes('word') ||
                                            attachment.mimeType.includes('document')) { %>
                                            <div
                                                class="w-16 h-16 bg-gray-200 flex items-center justify-center mr-4 flex-shrink-0 rounded">
                                                <svg class="w-8 h-8 text-gray-500" fill="currentColor"
                                                    viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                        d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
                                                        clip-rule="evenodd"></path>
                                                </svg>
                                            </div>
                                            <% } else if (attachment.mimeType.includes('excel') ||
                                                attachment.mimeType.includes('spreadsheet')) { %>
                                                <div
                                                    class="w-16 h-16 bg-gray-200 flex items-center justify-center mr-4 flex-shrink-0 rounded">
                                                    <svg class="w-8 h-8 text-gray-500" fill="currentColor"
                                                        viewBox="0 0 20 20">
                                                        <path fill-rule="evenodd"
                                                            d="M5 4a3 3 0 00-3 3v6a3 3 0 003 3h10a3 3 0 003-3V7a3 3 0 00-3-3H5zm-1 9v-1h5v2H5a1 1 0 01-1-1zm7 1h4a1 1 0 001-1v-1h-5v2zm0-4h5V8h-5v2zM9 8H4v2h5V8z"
                                                            clip-rule="evenodd"></path>
                                                    </svg>
                                                </div>
                                                <% } else { %>
                                                    <div
                                                        class="w-16 h-16 bg-gray-200 flex items-center justify-center mr-4 flex-shrink-0 rounded">
                                                        <svg class="w-8 h-8 text-gray-500" fill="currentColor"
                                                            viewBox="0 0 20 20">
                                                            <path fill-rule="evenodd"
                                                                d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4z"
                                                                clip-rule="evenodd"></path>
                                                        </svg>
                                                    </div>
                                                    <% } %>
                                                        <div class="flex-1 min-w-0">
                                                            <p class="text-sm font-medium text-gray-900 truncate">
                                                                <%= attachment.originalName %>
                                                            </p>
                                                            <p class="text-xs text-gray-500">
                                                                <%= (attachment.size / 1024).toFixed(2) %> KB
                                                            </p>
                                                            <a href="<%= attachment.path %>" target="_blank"
                                                                class="inline-block mt-2 text-sm text-blue-600 hover:underline">
                                                                <% if (attachment.mimeType.startsWith('image/')) {
                                                                    %>
                                                                    <%= currentLanguage==='en' ? 'View Image'
                                                                        : '查看圖片' %>
                                                                        <% } else if
                                                                            (attachment.mimeType.startsWith('video/'))
                                                                            { %>
                                                                            <%= currentLanguage==='en'
                                                                                ? 'Watch Video' : '觀看影片' %>
                                                                                <% } else { %>
                                                                                    <%= currentLanguage==='en'
                                                                                        ? 'Download File' : '下載檔案'
                                                                                        %>
                                                                                        <% } %>
                                                            </a>
                                                        </div>
                        </div>
                        <% }); %>
                </div>
            </div>
        <% } %>

        <!-- <div class="mt-8 text-sm text-gray-500">
            <p>
                <%= currentLanguage==='en' ? 'Published' : '發布日期' %>: <%= new Date(page.publishedAt ||
                        page.createdAt).toLocaleDateString() %>
            </p>
            <p>
                <%= currentLanguage==='en' ? 'Last updated' : '最後更新' %>: <%= new
                        Date(page.updatedAt).toLocaleDateString() %>
            </p>
        </div> -->
    </section>

        <% if (page.metaTitle) { %>
            <title>
                <%= page.metaTitle %>
            </title>
            <% } %>
                <% if (page.metaDescription) { %>
                    <meta name="description" content="<%= page.metaDescription %>">
                    <% } %>
                        <% if (page.metaKeywords) { %>
                            <meta name="keywords" content="<%= page.metaKeywords %>">
                            <% } %>
