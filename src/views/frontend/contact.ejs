<%- contentFor('body') %>

    <!-- User information -->
    <section id="userInfo" class="px-6 pt-16 pb-8 sm:px-16 sm:pb-16 lg:px-32 lg:pb-32">
        <form action="/<%= currentLanguage %>/contact" method="POST">
            <h2 class="h4 text-left mb-12"><%= currentLanguage==='en' ? 'Contact information' : '聯絡資料' %></h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <label for="name" class=""><%= currentLanguage==='en' ? 'Name' : '姓名' %><span class="text-red-700">*</span></label>
                    <input class="w-full px-6 py-2 mt-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-cyan-500"
                        id="name" name="name" type="text" placeholder="<%= currentLanguage==='en' ? 'Name' : '姓名' %>"
                        value="<%= typeof formData !== 'undefined' ? formData.name || '' : '' %>" required>
                </div>
                
                <div>
                    <label for="company" class=""><%= currentLanguage==='en' ? 'Company' : '公司名稱' %><span class="text-red-700">*</span></label>
                    <input class="w-full px-6 py-2 mt-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-cyan-500"
                        id="company" name="company" type="text" placeholder="<%= currentLanguage==='en' ? 'Company' : '公司名稱' %>" 
                        value="<%= typeof formData !== 'undefined' ? formData.company || '' : '' %>" required>
                </div>

                <div>
                    <label for="email" class=""><%= currentLanguage==='en' ? 'Email' : '電子信箱' %><span class="text-red-700">*</span></label>
                    <input class="w-full px-6 py-2 mt-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-cyan-500"
                        id="email" name="email" type="email" placeholder="<%= currentLanguage==='en' ? 'Email' : '電子信箱' %>" 
                        value="<%= typeof formData !== 'undefined' ? formData.email || '' : '' %>" required>
                </div>
                
                <div>
                    <label for="phone" class=""><%= currentLanguage==='en' ? 'Phone' : '聯絡電話' %><span class="text-red-700">*</span></label>
                    <input class="w-full px-6 py-2 mt-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-cyan-500" 
                        id="phone" name="phone" type="tel" placeholder="<%= currentLanguage==='en' ? 'Phone' : '聯絡電話' %>" 
                        value="<%= typeof formData !== 'undefined' ? formData.phone || '' : '' %>" required>
                </div>
            </div>

            <h2 class="h4 text-left my-12"><%= currentLanguage==='en' ? 'Inquiry' : '需求' %></h2>
            
            <label for="categoryId" class=""><%= currentLanguage==='en' ? 'Inquiry Categories' : '需求分類' %><span class="text-red-700">*</span></label>
            <div class="p-2 mt-4 mb-4 relative rounded-xl border">
                <select class="w-full  text-gray-600 border-0 focus:ring-0 py-2 pl-2 pr-10 appearance-none"
                    id="categoryId" name="categoryId" required>
                    <option value="">
                        <%= currentLanguage==='en' ? 'Select Category' : '選擇分類' %>
                      </option>
                      <% categories.forEach(function(category) { %>
                        <option value="<%= category.id %>" <%=typeof formData !=='undefined' &&
                          formData.categoryId==category.id ? 'selected' : '' %>>
                          <%= currentLanguage==='en' ? category.name_en : category.name_tw %>
                        </option>
                        <% }); %>
                </select>
                <!-- 自定義下拉箭頭 -->
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4 text-gray-500">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div>
            </div>
            
            <label for="message" class=""><%= currentLanguage==='en' ? 'Inquiry Description' : '需求說明' %><span class="text-red-700">*</span></label>
            <textarea name="message" id="message" rows="6" placeholder="<%= currentLanguage==='en' ? 'Requirements description' : '需求說明' %>" class="w-full px-3 py-2 mt-4 border border-gray-300 rounded-md focus:ring-1 focus:ring-cyan-500 resize-y"></textarea>

            <h2 class="h4 ext-left my-12">
                <%= currentLanguage==='en' ? 'Industrial Technology Research Institute Personal Data Collection, Processing, and Use Notification and Consent Form' : '財團法人工業技術研究院 個人資料蒐集、處理及利用之告知暨同意書' %>
            </h2>
            <div id="termsContainer" class="p-8 sm:p-16 h-80 border border-gray-300 rounded-3xl overflow-auto">
                <% if (agreement) { %>
                    <div class="prose max-w-none quill-content">
                        <div class="ql-container ql-snow" style="border: none;">
                            <div class="ql-editor quill-content-wrapper">
                                <%- currentLanguage==='en' ? agreement.content_en : agreement.content_tw %>
                            </div>
                        </div>
                    </div>
                <% } else { %>
                <p>
                    <%= currentLanguage==='en' ? 'Notification Statement' : '告知事項' %>
                    <br>
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    'The Industrial Technology Research Institute (hereinafter referred to as "the Institute") will collect, process, and use your personal data (hereinafter referred to as "personal data") to execute the Shalun Artificial Intelligence Industrial Zone Preliminary Planning Project. We hereby notify you of the following details: ' : 
                    '財團法人工業技術研究院（下稱本院）為了執行沙崙人工智慧產業專區先期規劃計畫，將蒐集、處理及利用您的個人資料（下稱個資），謹先告知下列事項：' %>
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    '1. Purpose of Collection Your personal data is collected for the following purposes: "006 Industrial Administration" "008 Assistance for SMEs and Other Industries" "078 Project, Control Evaluation, and Research Management" "110 Industry-Academic Collaboration" "130 Conference Management" "157 Surveys, Statistics, and Research Analysis" "182 Other Consulting and Advisory Services"' : 
                    '一、蒐集目的：「006工業行政」、「008中小企業及其他產業之輔導」、「078計畫、管制考核與其他研考管理」、「110產學合作」、「130 會議管理」、「157調查、統計與研究分析」及「182 其他諮詢與顧問服務」等相關事宜。' %>
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    '2. Categories of Personal Data Collected The following categories of personal data may be collected: C001: Identifiable Personal Information C003: Identifiable Government Data C038: Occupation C052: Professional Qualifications or Technical Certifications C053: Membership in Professional Associations C061: Current Employment Status C067: Membership in Trade Unions and Employee Associations ' : 
                    '二、個資類別：「C001識別個人者」、「C003政府資料中之辨識者」、「C038職業」、「C052資格或技術」、「C053職業團體會員資格」、「C061現行之受雇情形」及「C067工會及員工之會員資格」等。' %>
                    
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    '3. Data Retention Period Your data will be retained until the purpose of collection is fulfilled.' : 
                    '三、利用期間：至蒐集目的消失為止。' %>
                    
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    '4. Data Usage Location Your data will be used within the Republic of China (Taiwan) and at the Institute\'s locations and offices.' : 
                    '四、利用地區：中華民國地區及本院駐點及辦事處所在地區。' %>
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    '5. Data Users Your data may be used by the Institute and other public and private entities with business relations with the Institute.' : 
                    '五、利用者：本院及其他與本院有業務往來之公務及非公務機關。' %>
                    
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    '6. Methods of Data Usage Your data may be used through the internet, email, written communication, fax, or other legally permitted means, provided it does not contradict the stated purpose of collection.' : 
                    '六、利用方式：在不違反蒐集目的的前提下，以網際網路、電子郵件、書面、傳真及其他合法方式利用之。' %>
                    
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    '7. Your Rights You have the right to request the following in writing:' : 
                    '七、您得以書面主張下列權利：' %>
                    
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    'Access or review your personal data.' : 
                    '（一）查詢或請求閱覽。' %>
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    'Obtain a copy of your personal data.' : 
                    '（二）請求製給複製本。' %>
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    'Supplement or correct your personal data.' : 
                    '（三）請求補充或更正。' %>
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    'Request suspension of collection, processing, or use of your data.' : 
                    '（四）請求停止蒐集、處理或利用。' %>
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    'Request deletion of your personal data.' : 
                    '（五）請求刪除。' %>
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    'If you wish to exercise any of the above rights, please contact the responsible personnel at: Phone: 06-0000000 Email: <EMAIL> The Institute will respond to your request in accordance with applicable laws.' : 
                    '若有上述需求，請與承辦人員(電話：06-0000000；E-mail：<EMAIL>)聯繫，本院將依法進行回覆。' %>
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    '8. Consent Requirement If you choose not to sign this notification and consent form, the Institute will not be able to provide you with related services within the specific scope of its operations.' : 
                    '八、您若不簽署本告知暨同意書，本院將無法提供您特定目的範圍內之相關服務。' %>
                    
                    <br>
                    <br>
                    <%= currentLanguage==='en' ? 
                    '9. Confidentiality and Data Protection The Institute will safeguard and protect your personal data in accordance with relevant government regulations.' : 
                    '九、對本院所持有您的個資，本院會按照政府相關法規保密並予以妥善保管。' %>
                    
                    <br>
                    <br>
                </p>
                <% } %>
            </div>
            <div class="flex items-start my-8">
                <div class="flex items-center h-5">
                    <input id="agreeTerms" name="agreeTerms" type="checkbox" class="h-4 w-4 text-cyan-600 border-gray-300 rounded focus:ring-cyan-500" required>
                </div>
                <div class="ml-3">
                    <label for="agreeTerms" class="">
                        <%= currentLanguage==='en' ? 
                        'I have read and understood the notification regarding personal data consent and agree to allow the Southern Taiwan Silicon Valley Promotion Office to collect, process, and use my personal data. This consent may be expressed electronically.' : 
                        '本人已閱讀並瞭解個資同意書之 告知事項，並同意大南方推動辦公室，蒐集、處理及利用本人的個資，本項同意得以電子文件方式表達。' %>
                        <span class="text-red-600">
                            <%= currentLanguage==='en' ? 
                            'Please read the above consent form before checking' : 
                            '請閱讀完上方同意書再勾選' %>
                        </span>
                    </label>
                </div>
            </div>

            <!-- Captcha Section -->
            <div class="my-8">
                <label for="captchaEnter" class="block mb-4 font-medium">
                    <%= currentLanguage==='en' ? 'Please enter the verification code *' : '請輸入驗證碼 *' %>
                </label>
                <div class="flex flex-row items-center gap-4 w-full captcha-wrapper">
                    <div class="flex w-auto items-center gap-2 captcha-container">
                        <!-- Captcha display container -->
                        <div id="captchaDisplay" class="h-14 w-24 flex items-center justify-center border border-gray-300 rounded-md bg-repeat shrink-0"></div>
                        
                        <!-- Refresh button -->
                        <button type="button" onclick="ChangeCaptcha()" class="p-2 bg-gray-200 rounded-md hover:bg-gray-300 shrink-0" title="<%= currentLanguage==='en' ? 'Refresh the verification code' : '重置驗證碼' %>" aria-hidden="true" role="img" aria-label="<%= currentLanguage==='en' ? 'Refresh' : '重置' %>">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"aria-labelledby="filterIconTitle">
                                <title id="filterIconTitle"><%= currentLanguage === 'en' ? 'Refresh' : '重置' %></title>
                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                    
                    <!-- Input field -->
                    <input id="captchaEnter" name="captchaEnter" type="text" maxlength="6" required
                        class="flex-1 p-3 min-w-0 border border-gray-300 rounded-md focus:ring-1 focus:ring-cyan-500 captcha-input"
                        placeholder="<%= currentLanguage==='en' ? 'Enter the verification code' : '請輸入驗證碼' %>">
                        
                    <!-- Hidden input for validation -->
                    <label for="captchaValue" class="hidden">randomfield</label>
                    <input type="hidden" id="randomfield" name="captchaValue">
                </div>
            </div>

            <div class="flex justify-center my-8">
                <button type="submit" id="submitButton" class="px-12 py-2 border rounded-3xl custom-button-green">
                    <%= currentLanguage==='en' ? 'Submit' : '送出' %>
                </button>
            </div>
            

        </form>
    </section>

    <!-- Captcha Styles -->
    <style>
        .captcha-char {
            display: inline-block;
            margin: 0 2px;
            transform-origin: center;
            font-size: 46px;
        }
        
        .captcha-italic {
            font-style: italic;
        }
        
        .captcha-bold {
            font-weight: 900;
        }
        
        .captcha-underline {
            text-decoration: underline;
        }

        /* Responsive adjustments for captcha layout */
        @media (max-width: 640px) {
            .captcha-wrapper {
                flex-direction: column;
            }
            .captcha-container {
                width: 100%;
                margin-bottom: 0.5rem;
            }
            .captcha-input {
                width: 100%;
            }
        }
    </style>

    <!-- 添加 JavaScript 來處理條件邏輯 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const termsContainer = document.getElementById('termsContainer');
            const agreeTerms = document.getElementById('agreeTerms');
            const submitButton = document.getElementById('submitButton');
            let hasScrolledToBottom = false;

            // 更新提交按鈕狀態
            function updateSubmitButton() {
                if (agreeTerms.checked) {
                    submitButton.disabled = false;
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                } else {
                    submitButton.disabled = true;
                    submitButton.classList.add('opacity-50', 'cursor-not-allowed');
                }
            }

            // 監聽滾動事件
            termsContainer.addEventListener('scroll', function() {
                // 檢查是否滾動到底部
                if (termsContainer.scrollHeight - termsContainer.scrollTop <= termsContainer.clientHeight + 1) {
                    hasScrolledToBottom = true;
                    agreeTerms.disabled = false;
                    updateSubmitButton();
                }
            });

            // 監聽勾選框變化
            agreeTerms.addEventListener('change', function() {
                updateSubmitButton();
            });

            // 初始化時，禁用同意選項，直到用戶滾動到底部
            agreeTerms.disabled = true;
            
            // 初始化時執行一次判斷
            updateSubmitButton();
            
            // Generate captcha on page load
            ChangeCaptcha();
        });
        
        // Captcha functions
        function ChangeCaptcha() {
            var chars = "0123456789abcdefghiklmnopqrstuvwxyz"; 
            var string_length = 6;
            var captchaString = '';
            
            // Generate the captcha string
            for (var i=0; i<string_length; i++) {
                var rnum = Math.floor(Math.random() * chars.length);
                captchaString += chars.substring(rnum,rnum+1);
            }
            
            // Store the value in the input field for validation
            document.getElementById('randomfield').value = captchaString;
            
            // Now let's create a fancy display with random styling
            var captchaDisplay = document.getElementById('captchaDisplay');
            if (captchaDisplay) {
                captchaDisplay.innerHTML = '';
                
                for (var i = 0; i < captchaString.length; i++) {
                    var span = document.createElement('span');
                    span.className = 'captcha-char';
                    span.textContent = captchaString[i];
                    
                    // Random rotation
                    var rotation = Math.floor(Math.random() * 40) - 20; // -20 to 20 degrees
                    span.style.transform = 'rotate(' + rotation + 'deg)';
                    
                    // Random styling
                    if (Math.random() > 0.5) span.classList.add('captcha-italic');
                    if (Math.random() > 0.5) span.classList.add('captcha-bold');
                    if (Math.random() > 0.5) span.classList.add('captcha-underline');
                    
                    // Random color
                    var colors = ['#000', '#333', '#555', '#111', '#222', '#444'];
                    span.style.color = colors[Math.floor(Math.random() * colors.length)];
                    
                    captchaDisplay.appendChild(span);
                }
            }
        }
        
        // Form submission validation
        document.querySelector('form').addEventListener('submit', function(event) {
            var captchaEntered = document.getElementById('captchaEnter').value;
            var captchaGenerated = document.getElementById('randomfield').value;
            
            if(captchaEntered !== captchaGenerated) {
                event.preventDefault();
                var lang = "<%= currentLanguage %>";
                alert(lang === 'en' ? 'Incorrect verification code. Please try again.' : '驗證碼錯誤，請重試');
            }
        });
    </script>