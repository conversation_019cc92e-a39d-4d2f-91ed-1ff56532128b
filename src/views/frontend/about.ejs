<%- contentFor('body') %>
    
    <!-- Navigation Tabs -->
    <nav class="flex my-4 px-6 sm:px-16 lg:px-32 overflow-auto">
        <% if (items && items.length> 0) { %>
            <% items.forEach(function(item) { %>
            <a href="#<%= item.title_en %>" 
            class="subtitle px-6 py-2 rounded-full mr-4 min-w-max custom-button-yellow">
                <%= currentLanguage==='en' ? item.title_en : item.title_tw %>
            </a>
            <% }); %>
        <% } %>
    </nav>
    <!-- Divider -->
    <div class="w-screen relative" style="left: 50%; right: 50%; margin-left: -50vw; margin-right: -50vw;">
        <hr class="border-t-2 border-gray-200 mb-12">
    </div>

    <% if (items && items.length> 0) { %>
        <% items.forEach(function(item) { %>
            <section id="<%= item.title_en %>" class="px-6 py-8 sm:px-16 sm:pb-16 lg:px-32 lg:pb-16">
                <h2 class="h4 text-left mb-4">
                    <%= currentLanguage==='en' ? item.title_en : item.title_tw %>
                </h2>
                <% if (item.type==='plain_text' ) { %>
                    <div class="w-full ">
                        <div class="prose max-w-none quill-content">
                            <div class="ql-container ql-snow" style="border: none;">
                                <div class="ql-editor quill-content-wrapper" <% if ((currentLanguage==='en' &&
                                    item._content_en_is_delta) || (currentLanguage==='tw' && item._content_tw_is_delta))
                                    { %>
                                    data-is-delta="true"
                                    <% } %>
                                        id="quill-content-<%= item.id %>-<%= currentLanguage %>">
                                                <%- currentLanguage==='en' ? item.content_en : item.content_tw %>
                                </div>
                            </div>
                        </div>
                    </div>
                    <% } else if (item.type==='image' ) { %>
                        <% if (item.imagePath || item.imagePathDesktop || item.imagePathTablet || item.imagePathMobile || 
                               item.imagePathDesktopEn || item.imagePathTabletEn || item.imagePathMobileEn) { %>
                            <div class="mt-4 flex justify-center">
                                <picture>
                                    <% if (currentLanguage === 'en') { %>
                                        <% if (item.imagePathMobileEn) { %>
                                            <source media="(max-width: 640px)" srcset="<%= item.imagePathMobileEn %>">
                                        <% } else if (item.imagePathMobile) { %>
                                            <source media="(max-width: 640px)" srcset="<%= item.imagePathMobile %>">
                                        <% } %>
                                        
                                        <% if (item.imagePathTabletEn) { %>
                                            <source media="(max-width: 1024px)" srcset="<%= item.imagePathTabletEn %>">
                                        <% } else if (item.imagePathTablet) { %>
                                            <source media="(max-width: 1024px)" srcset="<%= item.imagePathTablet %>">
                                        <% } %>
                                        
                                        <% if (item.imagePathDesktopEn) { %>
                                            <source media="(min-width: 1025px)" srcset="<%= item.imagePathDesktopEn %>">
                                        <% } else if (item.imagePathDesktop) { %>
                                            <source media="(min-width: 1025px)" srcset="<%= item.imagePathDesktop %>">
                                        <% } %>
                                        
                                        <img src="<%= item.imagePathDesktopEn || item.imagePathTabletEn || item.imagePathMobileEn || 
                                                    item.imagePathDesktop || item.imagePathTablet || item.imagePathMobile || item.imagePath %>"
                                            alt="<%= item.alt_en || 'Southern Taiwan Silicon Valley, led by NSTC, drives smart and sustainable industry growth through its promotion office.' %>"
                                            class="max-w-full h-auto rounded-lg shadow-sm">
                                    <% } else { %>
                                        <% if (item.imagePathMobile) { %>
                                            <source media="(max-width: 640px)" srcset="<%= item.imagePathMobile %>">
                                        <% } %>
                                        
                                        <% if (item.imagePathTablet) { %>
                                            <source media="(max-width: 1024px)" srcset="<%= item.imagePathTablet %>">
                                        <% } %>
                                        
                                        <% if (item.imagePathDesktop) { %>
                                            <source media="(min-width: 1025px)" srcset="<%= item.imagePathDesktop %>">
                                        <% } %>
                                        
                                        <img src="<%= item.imagePathDesktop || item.imagePathTablet || item.imagePathMobile || item.imagePath %>"
                                            alt="<%= item.alt_tw || '大南方新矽谷由國科會統籌，設置服務與推動辦公室，執行各項推動計畫，加速智慧化與永續產業發展。' %>"
                                            class="max-w-full h-auto rounded-lg shadow-sm">
                                    <% } %>
                                </picture>
                            </div>
                            <div class="mt-4 text-center text-gray-700 quill-content">
                                <div class="ql-container ql-snow" style="border: none;">
                                    <div class="ql-editor">
                                        <%- currentLanguage==='en' ? item.content_en : item.content_tw %>
                                    </div>
                                </div>
                            </div>
                            <% } %>
                                <% } %>
            </section>

            <% }); %>
                <% } %>

<%- contentFor('scripts') %>
<script>
    // 等待DOM完全加載
    document.addEventListener('DOMContentLoaded', function () {
        // 滾動偏移量設定（可調整）
        const scrollOffset = 120;
        // 獲取所有 subtitle 元素
        const subtitles = document.querySelectorAll('.subtitle');
        // 獲取所有 section 元素
        const sections = document.querySelectorAll('section[id]');
        // 預設顯示第一個標籤內容，隱藏其他
        sections.forEach((section, index) => {
            if (index === 0) {
                section.classList.remove('hidden');
            } else {
                section.classList.add('hidden');
            }
        });

        // 檢查URL是否有錨點，如果有則滾動到對應位置
        if (window.location.hash) {
            setTimeout(function() {
                const targetId = window.location.hash.substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    // 顯示目標內容，隱藏其他內容
                    sections.forEach(section => {
                        if (section.id === targetId) {
                            section.classList.remove('hidden');
                        } else {
                            section.classList.add('hidden');
                        }
                    });
                    
                    // 設置對應的subtitle為激活狀態
                    let activeSubtitle = null;
                    subtitles.forEach(subtitle => {
                        const subtitleTargetId = subtitle.getAttribute('href').substring(1);
                        if (subtitleTargetId === targetId) {
                            subtitle.classList.add('active');
                            activeSubtitle = subtitle;
                        } else {
                            subtitle.classList.remove('active');
                        }
                    });

                    // 滾動到目標區域，考慮偏移量
                    const elementPosition = targetElement.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - scrollOffset;
                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });
                }
            }, 100); // 100毫秒延遲，確保頁面元素都已載入
        }

        // 為每個 subtitle 添加點擊事件監聽器
        subtitles.forEach(subtitle => {
            subtitle.addEventListener('click', function (e) {
                // 阻止默認的錨點行為
                // e.preventDefault();

                // 移除所有 subtitle 的活動樣式
                subtitles.forEach(s => {
                    // 移除活動樣式
                    s.classList.remove('active');
                });

                // 為被點擊的元素添加活動樣式
                this.classList.add('active');

                const targetId = this.getAttribute('href').substring(1);

                // 顯示目標內容，隱藏其他內容
                sections.forEach(section => {
                    if (section.id === targetId) {
                        section.classList.remove('hidden');
                    } else {
                        section.classList.add('hidden');
                    }
                });

                // 如果需要，可以滾動到目標區域
                const targetElement = document.getElementById(targetId);
                if (targetElement) {
                    const elementPosition = targetElement.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.pageYOffset - scrollOffset;

                    window.scrollTo({
                        top: offsetPosition,
                        behavior: 'smooth'
                    });
                }
            });
        });

    });
</script>