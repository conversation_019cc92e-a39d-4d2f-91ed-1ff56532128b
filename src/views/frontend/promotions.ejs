<%- contentFor('body') %>
    <!-- Navigation Tabs -->
    <!-- 添加箭頭按鈕的導航欄容器 -->
    <div class="relative flex items-center my-4 px-6 sm:px-16 lg:px-32">
        <!-- 導航欄 -->
        <nav id="scrollable-nav" class="flex overflow-auto scrollbar-hide scroll-smooth">
            <a href="#platform" class="subtitle px-6 py-2 rounded-full mr-4 min-w-max custom-button-yellow active">
                <%= currentLanguage==='en' ? 'Taiwan Smart System Integration and Manufacturing Platform' : '臺灣智慧系統整合製造平台' %>
            </a>
            <% categories.forEach(category=> { %>
                <a href="#<%= category.slug %>" class="subtitle px-6 py-2 rounded-full mr-4 min-w-max custom-button-yellow">
                    <%= getContent(category, 'name' ) %>
                </a>
            <% }); %>
        </nav>
        <!-- 左箭頭按鈕 -->
        <button id="scroll-left" class="absolute left-0 sm:left-10 z-10 px-2 py-2 text-gray-600 bg-white rounded-full shadow-md hover:bg-gray-100" aria-label="向左捲動" title="向左捲動">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" role="img" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
        </button>
        <!-- 右箭頭按鈕 -->
        <button id="scroll-right" class="absolute right-0 sm:right-10 z-10 px-2 py-2 text-gray-600 bg-white rounded-full shadow-md hover:bg-gray-100" aria-label="向右捲動" title="向右捲動">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor" role="img" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
        </button>
    </div>

    <!-- Divider -->
    <div class="w-screen relative" style="left: 50%; right: 50%; margin-left: -50vw; margin-right: -50vw;">
        <hr class="border-t-2 border-gray-200 mb-12">
    </div>

    <!-- About platform -->
    <section id="platform" class="px-6 pb-8 sm:px-16 sm:pb-16 lg:px-32 lg:pb-32">
        <% if (platformsByCategory && platformsByCategory.length> 0) { %>
            <% platformsByCategory.forEach(function(category) { %>
                <% if (category.platforms && category.platforms.length>= 0) { %>

                    <!-- Non-Partner Content -->
                    <% if (!category.name_en.includes("artner")) { %>
                        <% category.platforms.forEach(function(platform) { %>

                            <!-- Plain_text Type -->
                            <% if (platform.type==='plain_text' ) { %>
                                <h2 class="h4 mb-4">
                                    <%= currentLanguage==='en' ? platform.title_en : platform.title_tw %>
                                </h2>
                                <div class="prose max-w-none quill-content">
                                    <div class="ql-container ql-snow" style="border: none;">
                                        <div class="ql-editor quill-content-wrapper"
                                            <% if ((currentLanguage==='en' && platform._content_en_is_delta) || (currentLanguage==='tw' && platform._content_tw_is_delta)) { %>
                                                data-is-delta="true"
                                            <% } %>
                                            id="quill-content-<%= platform.id %>-<%= currentLanguage %>">
                                                <%- currentLanguage==='en' ? platform.content_en : platform.content_tw %>
                                        </div>
                                    </div>
                                </div>
                                <% if ((currentLanguage==='en' && platform.imagePathEn) || (currentLanguage==='tw' && platform.imagePath) || (currentLanguage==='en' && !platform.imagePathEn && platform.imagePath)) { %>
                                    <div class="mt-4">
                                        <% if (currentLanguage === 'en' && platform.imagePathEn) { %>
                                            <img src="<%= platform.imagePathEn %>"
                                                alt="<%= platform.alt_en || platform.title_en %>"
                                                class="w-full h-auto rounded-lg">
                                        <% } else { %>
                                            <img src="<%= platform.imagePath %>"
                                                alt="<%= currentLanguage === 'en' ? (platform.alt_en || platform.title_en) : (platform.alt_tw || platform.title_tw) %>"
                                                class="w-full h-auto rounded-lg">
                                        <% } %>
                                    </div>
                                <% } %>

                            <!-- Image Type -->
                            <% } else if (platform.type==='image' ) { %>
                                <% if ((currentLanguage==='en' && platform.imagePathEn) || (currentLanguage==='tw' && platform.imagePath) || (currentLanguage==='en' && !platform.imagePathEn && platform.imagePath)) { %>
                                    <h2 class="h4 mb-4">
                                        <%= currentLanguage==='en' ? platform.title_en : platform.title_tw %>
                                    </h2>
                                    <div class="mb-10">
                                        <% if (currentLanguage === 'en' && platform.imagePathEn) { %>
                                            <img src="<%= platform.imagePathEn %>"
                                            alt="<%= platform.alt_en || 'Taiwan Smart System Integration and Manufacturing Platform offers consulting, matching, design, and production services to help businesses implement smart applications.' %>"
                                            class="w-full h-auto rounded-lg">
                                        <% } else { %>
                                            <img src="<%= platform.imagePath %>"
                                            alt="<%= currentLanguage === 'en' ? (platform.alt_en || 'Taiwan Smart System Integration and Manufacturing Platform offers consulting, matching, design, and production services to help businesses implement smart applications.') : (platform.alt_tw || '臺灣智慧系統整合製造平台透過專案小組提供諮詢、媒合、設計開發、驗證量產等客製化整合服務，協助業者落實智慧化應用。') %>"
                                            class="w-full h-auto rounded-lg">
                                        <% } %>
                                    </div>
                                <% } %>

                            <!-- Attachment Type -->
                            <% } else if (platform.type==='attachment_only' ) { %>
                                <h2 class="h4 mb-4">
                                    <%= currentLanguage==='en' ? platform.title_en : platform.title_tw %>
                                </h2>
                                <% if (platform.attachments && platform.attachments.length> 0) { %>
                                    <div class="mt-4">
                                        <div class="space-y-2">
                                            <% platform.attachments.forEach(function(attachment) { %>
                                                <div class="bg-gray-100 rounded-2xl">
                                                    <div class="flex items-center justify-between px-6 py-2">
                                                        <!-- 標題部分 -->
                                                        <p class="">
                                                            <%= currentLanguage==='en'
                                                                ? (attachment.attachment_name_en || attachment.originalName)
                                                                : (attachment.attachment_name_tw || attachment.originalName) %>
                                                        </p>
                                                        <!-- 下載按鈕部分 -->
                                                        <a href="<%= encodeURI(attachment.path) %>" download="<%= attachment.originalName %>" title="<%= attachment.originalName %>" class="inline-flex items-center px-0 sm:px-5 py-0">
                                                            <img src="/images/desktop/downloadcloud.png" alt="<%= currentLanguage==='en' ? 'Download icon' : '下載雲' %>" class="h-8 mr-0 sm:mr-2 object-contain">
                                                            <span class="hidden sm:block underline">
                                                                <%= currentLanguage==='en' ? 'File Download' : '檔案下載' %>
                                                            </span>
                                                        </a>
                                                    </div>
                                                </div>
                                            <% }); %>
                                        </div>
                                    </div>
                                <% } else { %>
                                    <div class="bg-gray-100 p-4 rounded-lg text-center text-gray-500">
                                        <%= currentLanguage==='en' ? 'No attachments' : '沒有附件' %>
                                    </div>
                                <% } %>
                            <% } %>
                        <% }); %>
                    <% } else { %>
                        <!-- Partner Categories Section -->
                        <% if (partnerCategories && partnerCategories.length > 0) { %>
                            <% partnerCategories.forEach(function(category) { %>
                                <h2 class="h4 mb-4">
                                    <%= currentLanguage==='en' ? 'Partners' : '合作伙伴' %>
                                </h2>

                                <!-- Partners Type -->
                                <div class="flex flex-col sm:flex-row mb-8 sm:mb-16">
                                    <!-- 手機版下拉選單 -->
                                    <div class="block w-full sm:hidden mb-6 p-2 relative rounded-xl border">
                                        <label for="categorySelect" class="hidden"><%= currentLanguage==='en' ? "Industry menu" : "產業選單" %></label>
                                        <select id="categorySelect" name="categorySelect" class="w-full text-gray-600 border-0 focus:ring-0 py-2 pl-2 pr-10 appearance-none">
                                            <% category.platforms.forEach(function(platform) { %>
                                                <option value="<%= platform.title_en %>" selected>
                                                    <%= currentLanguage==='en' ? platform.title_en : platform.title_tw %>
                                                </option>
                                            <% }); %>
                                        </select>
                                        <!-- 自定義下拉箭頭 -->
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-4 text-gray-500">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" aria-hidden="true">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                            </svg>
                                        </div>
                                    </div>
                                    <!-- 左側選單 (Desktop) -->
                                    <div class="hidden sm:block sm:w-3/12 mr-6">
                                        <ul class="">
                                            <% category.platforms.forEach(function(platform) { %>
                                                <li class="">
                                                    <button class="categoryTab block px-3 py-2 mb-2 w-full text-center bg-gray-100 text-gray-500" type="button">
                                                        <%= currentLanguage==='en' ? platform.title_en : platform.title_tw %>
                                                    </button>
                                                </li>
                                            <% }); %>
                                        </ul>
                                    </div>

                                    <!-- 主內容區 -->
                                    <div class="w-full sm:w-9/12 bg-white border-2 rounded-xl border-gray-100 p-4 mb-8 sm:mb-0 min-h-full">
                                        <!-- 頂部標籤頁 -->
                                        <div class="flex justify-center">
                                            <button class="companyTab px-4 py-2 border-b-2 border-custom-blue text-custom-blue font-medium">
                                                <%= currentLanguage==='en' ? 'Suppliers' : '供應商' %>
                                            </button>
                                            <button class="companyTab px-4 py-2 border-b-2 border-gray-100 text-gray-500">
                                                <%= currentLanguage==='en' ? 'Buyers' : '需求商' %>
                                            </button>
                                        </div>
                                        <% category.platforms.forEach(function(platform) { %>
                                            <%
                                            let partnersData = platform.parsedPartnersData || {
                                                suppliers: { companies_en: [], companies_tw: [] },
                                                buyers: { companies_en: [], companies_tw: [] }
                                            };

                                            // Get the right companies array based on language
                                            const suppliersCompanies = currentLanguage === 'en'
                                                ? (partnersData.suppliers?.companies_en || [])
                                                : (partnersData.suppliers?.companies_tw || []);

                                            const buyersCompanies = currentLanguage === 'en'
                                                ? (partnersData.buyers?.companies_en || [])
                                                : (partnersData.buyers?.companies_tw || []);

                                            const hasSuppliers = suppliersCompanies.length > 0;
                                            const hasBuyers = buyersCompanies.length > 0;
                                            %>
                                            <div class="companyList">
                                                <% if (hasSuppliers) { %>
                                                    <!-- 供應商列表 -->
                                                    <div class="supplierList py-6 grid grid-cols-1 sm:grid-cols-2 gap-x-2 gap-y-4">
                                                        <!-- 供應商卡片 -->
                                                        <% suppliersCompanies.forEach(function(company) { %>
                                                            <div class="partner-card h-full flex items-center justify-center">
                                                                <p class="text-center"><%= company %></p>
                                                            </div>
                                                        <% }); %>
                                                    </div>
                                                <% } %>
                                                <% if (hasBuyers) { %>
                                                    <!-- 需求商列表 -->
                                                    <div class="demanderList py-6 grid grid-cols-1 sm:grid-cols-2 gap-x-2 gap-y-4 hidden">
                                                        <!-- 需求商卡片 -->
                                                        <% buyersCompanies.forEach(function(company) { %>
                                                            <div class="partner-card h-full flex items-center justify-center">
                                                                <p class="text-center"><%= company %></p>
                                                            </div>
                                                        <% }); %>
                                                    </div>
                                                <% } %>
                                            </div>
                                        <% }); %>
                                    </div>
                                </div>
                            <% }); %>
                        <% } %>
                    <% } %>


                <% } %>
            <% }); %>
        <% } %>

    </section>

    <!-- Promotion sections -->
    <% if (items && items.length> 0) { %>
        <% items.forEach(item=> { %>
            <section id="<%= item.category.slug %>" class="px-6 pb-8 sm:px-16 sm:pb-16 lg:px-32 lg:pb-32">
                <h2 class="h4 mb-8">
                    <%= language==='en' ? item.title_en : item.title_tw %>
                </h2>
                <!-- Hero Image -->
                <% if ((language === 'en' && item.imagePathEn) || (language === 'tw' && item.imagePath) || (language === 'en' && !item.imagePathEn && item.imagePath)) { %>
                <div class="mb-8">
                    <% if (language === 'en' && item.imagePathEn) { %>
                        <img src="/<%= item.imagePathEn %>" alt="<%= item.alt_en || item.title_en %>"
                            class="w-full h-auto rounded-lg">
                    <% } else { %>
                        <img src="/<%= item.imagePath %>" alt="<%= language === 'en' ? (item.alt_en || item.title_en) : (item.alt_tw || item.title_tw) %>"
                            class="w-full h-auto rounded-lg">
                    <% } %>
                </div>
                <% } %>
                <!-- Description and CTA in flex layout -->
                <div class="flex flex-col">
                    <!-- Description on left - 8/12 width on desktop -->
                    <div class="w-full mb-6 promotion-content">
                        <% const content=language==='en' ? item.content_en : item.content_tw; %>
                        <div class="prose max-w-none quill-content">
                            <div class="ql-container ql-snow" style="border: none;">
                                <div class="ql-editor">
                                    <%- content %>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CTA Button on right - 4/12 width on desktop -->
                    <% if ((language === 'en' && item.urlEn) || (language === 'tw' && item.url) || (language === 'en' && !item.urlEn && item.url)) { %>
                    <div class="w-full min-w-max  flex justify-center">
                        <% if (language === 'en' && item.urlEn) { %>
                            <a href="<%= item.urlEn %>" target="_blank" title="另開新視窗" rel="noopener noreferrer"
                                class="no-underline inline-flex items-center px-6 py-3 border rounded-3xl custom-button-green">
                                <span><%= "Enter " + item.title_en %></span>
                                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        <% } else { %>
                            <a href="<%= item.url %>" target="_blank" title="另開新視窗"  rel="noopener noreferrer"
                                class="no-underline inline-flex items-center px-6 py-3 border rounded-3xl custom-button-green">
                                <span><%= language==='en' ? "Access " + item.title_en : "進入" + item.title_tw %></span>
                                <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                </svg>
                            </a>
                        <% } %>
                    </div>
                    <% } %>
                </div>
            </section>
        <% }); %>
    <% } %>

    <!-- Promotion Downloads Section -->
    <% if (locals.promotionAttachments && promotionAttachments.length > 0) { %>
        <section id="promotionDownloads" class="px-6 pb-8 sm:px-16 sm:pb-16 lg:px-32">
            <%
                // 建立三層結構：分類 > 分組 > 下載項目
                const hierarchicalDownloads = {};

                // 將下載項目按分類和分組進行層次分組
                promotionAttachments.forEach(attachment => {
                    // 使用分類名稱作為第一層
                    const categoryName = attachment.group?.category ?
                        (language === 'en' ? attachment.group.category.name_en : attachment.group.category.name_tw)
                        : (language === 'en' ? 'Uncategorized' : '未分類');

                    // 使用分組名稱作為第二層
                    const groupName = attachment.group ?
                        (language === 'en' ? attachment.group.name_en : attachment.group.name_tw)
                        : (language === 'en' ? 'Others' : '其他');

                    if (!hierarchicalDownloads[categoryName]) {
                        hierarchicalDownloads[categoryName] = {};
                    }

                    if (!hierarchicalDownloads[categoryName][groupName]) {
                        hierarchicalDownloads[categoryName][groupName] = [];
                    }

                    hierarchicalDownloads[categoryName][groupName].push(attachment);
                });

                // 遍歷每個分類
                Object.keys(hierarchicalDownloads).forEach((categoryName, categoryIndex) => {
                %>

                <!-- 分類分隔線 -->
                <% if (categoryIndex > 0) { %>
                    <div class="my-12">
                        <hr class="border-t-4 border-blue-300">
                    </div>
                <% } %>

                <!-- 分類標題 -->
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-blue-600 mb-2">
                        <i class="fas fa-folder mr-2"></i><%= categoryName %>
                    </h2>
                </div>

                <%
                    const groupsInCategory = hierarchicalDownloads[categoryName];
                    // 遍歷該分類下的每個分組
                    Object.keys(groupsInCategory).forEach((groupName, groupIndex) => {
                %>

                    <!-- 分組分隔線 -->
                    <% if (groupIndex > 0) { %>
                        <div class="my-6">
                            <hr class="border-t-2 border-gray-200">
                        </div>
                    <% } %>

                    <!-- 分組標題 -->
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">
                        <i class="fas fa-layer-group mr-2"></i><%= groupName %>
                    </h3>

                    <!-- 該分組下的所有下載項目 -->
                    <div class="ml-4">
                        <% groupsInCategory[groupName].forEach(function(attachment) { %>
                            <!-- 橫幅卡片 -->
                            <div class="bg-gray-100 rounded-2xl mb-4">
                                <div class="flex items-center justify-between px-6 py-2">
                                    <!-- 標題部分 -->
                                    <div class="flex items-center">
                                        <i class="fas fa-file-download mr-3 text-gray-600"></i>
                                        <div>
                                            <p class="font-medium">
                                                <%= language === 'en' ?
                                                    (attachment.attachment_name_en || attachment.title_en || attachment.originalName) :
                                                    (attachment.attachment_name_tw || attachment.title_tw || attachment.originalName) %>
                                            </p>
                                            <% if ((language === 'en' && attachment.description_en) || (language === 'tw' && attachment.description_tw)) { %>
                                                <p class="text-sm text-gray-600">
                                                    <%= language === 'en' ? attachment.description_en : attachment.description_tw %>
                                                </p>
                                            <% } %>
                                        </div>
                                    </div>

                                    <!-- 下載按鈕部分 -->
                                    <a href="/admin/promotions/attachments/<%= attachment.id %>/download" title="<%= attachment.originalName %>" class="inline-flex items-center px-0 sm:px-5 py-0 hover:bg-gray-200 rounded-lg transition-colors">
                                        <img src="/images/desktop/downloadcloud.png" alt="下載雲" class="h-8 mr-0 sm:mr-2 object-contain">
                                        <span class="hidden sm:block underline text-blue-600"><%= language === 'en' ? 'Download' : '檔案下載' %></span>
                                    </a>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                <% }); %>
            <% }); %>
        </section>
    <% } %>

<%- contentFor('scripts') %>
    <script>
        // 等待DOM完全加載
        document.addEventListener('DOMContentLoaded', function () {
            // 獲取所有 section 元素
            const sections = document.querySelectorAll('section[id]');
            // 預設顯示第一個標籤內容，隱藏其他
            sections.forEach((section, index) => {
                if (index === 0) {
                    section.classList.remove('hidden');
                } else {
                    section.classList.add('hidden');
                }
            });

            ///////////////////////////////////////////////////////////

            // 滾動偏移量設定（可調整）
            const scrollOffset = 120;
            // 獲取所有 subtitle 元素
            const subtitles = document.querySelectorAll('.subtitle');
            // 獲取所有 navBar
            const navBar = document.getElementById('scrollable-nav');
            const leftButton = document.getElementById('scroll-left');
            const rightButton = document.getElementById('scroll-right');
            const scrollAmount = 200; // 每次滾動的像素量

            // 新增函數：滾動到選定的 subtitle
            function scrollToSelectedSubtitle(selectedSubtitle) {
                if (!selectedSubtitle || !navBar) return;
                
                // 獲取 subtitle 的位置和尺寸資訊
                const subtitleRect = selectedSubtitle.getBoundingClientRect();
                const navBarRect = navBar.getBoundingClientRect();
                
                // 檢查 subtitle 是否在可視區域內
                const isVisible = (
                    subtitleRect.left >= navBarRect.left &&
                    subtitleRect.right <= navBarRect.right
                );
                
                if (!isVisible) {
                    // 計算需要滾動的距離，使 subtitle 置中
                    const scrollNeeded = selectedSubtitle.offsetLeft - navBar.offsetLeft - 
                                        (navBar.clientWidth / 2) + (subtitleRect.width / 2);
                    
                    // 平滑滾動到計算出的位置
                    navBar.scrollTo({
                    left: scrollNeeded,
                    behavior: 'smooth'
                    });
                }
            }

            // 左箭頭按鈕點擊事件
            leftButton.addEventListener('click', function () {
                navBar.scrollLeft -= scrollAmount;
            });
            // 右箭頭按鈕點擊事件
            rightButton.addEventListener('click', function () {
                navBar.scrollLeft += scrollAmount;
            });

            // 檢查是否需要顯示箭頭按鈕
            function checkScrollButtons() {
                // 隱藏/顯示左箭頭
                if (navBar.scrollLeft <= 0) {
                    leftButton.classList.add('opacity-50', 'cursor-not-allowed');
                    leftButton.setAttribute('disabled', 'true');
                    leftButton.setAttribute('tabindex', '-1'); // 移除焦點
                } else {
                    leftButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    leftButton.removeAttribute('disabled');
                    leftButton.removeAttribute('tabindex'); // 恢復焦點
                }
                // 隱藏/顯示右箭頭
                if (navBar.scrollLeft + navBar.clientWidth >= navBar.scrollWidth) {
                    rightButton.classList.add('opacity-50', 'cursor-not-allowed');
                    rightButton.setAttribute('disabled', 'true');
                    rightButton.setAttribute('tabindex', '-1'); // 移除焦點
                } else {
                    rightButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    rightButton.removeAttribute('disabled');
                    rightButton.removeAttribute('tabindex'); // 恢復焦點
                }
            }

            // 檢查URL是否有錨點，如果有則滾動到對應位置
            if (window.location.hash) {
                setTimeout(function() {
                    const targetId = window.location.hash.substring(1);
                    const targetElement = document.getElementById(targetId);
                    
                    if (targetElement) {
                        // 顯示目標內容，隱藏其他內容
                        sections.forEach(section => {
                            if (section.id === targetId) {
                                section.classList.remove('hidden');
                            } else {
                                section.classList.add('hidden');
                            }
                        });
                        
                        // 設置對應的subtitle為激活狀態
                        let activeSubtitle = null;
                        subtitles.forEach(subtitle => {
                            const subtitleTargetId = subtitle.getAttribute('href').substring(1);
                            if (subtitleTargetId === targetId) {
                                subtitle.classList.add('active');
                                activeSubtitle = subtitle;
                            } else {
                                subtitle.classList.remove('active');
                            }
                        });

                        
                        // 確保被選取的 subtitle 在可視區域內
                        if (activeSubtitle) {
                            scrollToSelectedSubtitle(activeSubtitle);
                        }

                        // 滾動到目標區域，考慮偏移量
                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - scrollOffset;
                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });
                    }
                }, 100); // 100毫秒延遲，確保頁面元素都已載入
            }

            // 為每個 subtitle 添加點擊事件監聽器
            subtitles.forEach(subtitle => {
                subtitle.addEventListener('click', function (e) {
                    // 阻止默認的錨點行為
                    // e.preventDefault();

                    // 移除所有 subtitle 的活動樣式
                    subtitles.forEach(s => {
                        // 移除活動樣式
                        s.classList.remove('active');
                    });

                    // 為被點擊的元素添加活動樣式
                    this.classList.add('active');

                    const targetId = this.getAttribute('href').substring(1);

                    // 顯示目標內容，隱藏其他內容
                    sections.forEach(section => {
                        if (section.id === targetId) {
                            section.classList.remove('hidden');
                        } else {
                            section.classList.add('hidden');
                        }
                    });

                    // 如果需要，可以滾動到目標區域
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        const elementPosition = targetElement.getBoundingClientRect().top;
                        const offsetPosition = elementPosition + window.pageYOffset - scrollOffset;

                        window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                        });
                    }

                    // 確保 subtitle 可見
                    scrollToSelectedSubtitle(this);
                });
            });

            // 綁定滾動事件
            navBar.addEventListener('scroll', checkScrollButtons);
            // 頁面載入時執行一次
            checkScrollButtons();
            // 視窗調整大小時檢查
            window.addEventListener('resize', checkScrollButtons);

            ///////////////////////////////////////////////////////////

            // 產業切換功能
            const categoryTabs = document.querySelectorAll('.categoryTab');
            const companyLists = document.querySelectorAll('.companyList');

                // 初始化 - 只顯示第一個類別的 companyList
            if (companyLists.length > 0) {
                // 隱藏所有 companyList
                companyLists.forEach((list, index) => {
                    if (index === 0) {
                        list.classList.remove('hidden'); // 顯示第一個
                    } else {
                        list.classList.add('hidden');// 隱藏其他
                    }
                });

                // 設定第一個 categoryTab 為活動狀態
                if (categoryTabs.length > 0) {
                    categoryTabs[0].classList.add('border-custom-blue', 'border-l-4');
                    categoryTabs[0].classList.remove('text-gray-500');
                }
            }

            categoryTabs.forEach((categoryTab, tabIndex) => {
                categoryTab.addEventListener('click', function() {
                    // 移除所有標籤的活動狀態
                    categoryTabs.forEach(t => {
                        t.classList.remove('border-custom-blue', 'border-l-4');
                        t.classList.add('text-gray-500');
                    });

                    // 設置當前標籤為活動狀態
                    this.classList.remove('text-gray-500');
                    this.classList.add('border-custom-blue', 'border-l-4');

                    // 隱藏所有 companyList，只顯示對應的那一個
                    companyLists.forEach((list, listIndex) => {
                        if (listIndex === tabIndex) {
                            list.style.display = 'block'; // 顯示當前選中的類別
                        } else {
                            list.style.display = 'none'; // 隱藏其他類別
                        }
                    });
                });
            });

            ///////////////////////////////////////////////////////////

            // 公司切換功能
            const companyTabs = document.querySelectorAll('.companyTab');
            const supplierLists = document.querySelectorAll('.supplierList');
            const demanderLists = document.querySelectorAll('.demanderList');

            companyTabs.forEach((companyTab, index) => {
                companyTab.addEventListener('click', function() {
                    companyTabs.forEach(tab => {
                        // 切換標籤樣式
                        tab.classList.toggle('border-custom-blue', tab === this);
                        tab.classList.toggle('text-custom-blue', tab === this);
                        tab.classList.toggle('text-gray-500', tab !== this);
                        tab.classList.toggle('border-gray-100', tab !== this);
                    });
                    // 切換內容顯示
                    const isSupplierTab = index === 0;
                    supplierLists.forEach((list, index) => {
                        list.classList.toggle('hidden', !isSupplierTab);
                    });
                    demanderLists.forEach((list, index) => {
                        list.classList.toggle('hidden', isSupplierTab);
                    });
                });
            });

            ///////////////////////////////////////////////////////////

        });
    </script>