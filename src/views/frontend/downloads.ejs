<%- contentFor('body') %>

    <!-- Download categories -->
    <section id="aboutplatform" class="px-6 pt-16 pb-8 sm:px-16 sm:pb-16 lg:px-32">
        <div class="rounded-3xl border border-custom-green px-4 py-8 mb-12 sm:px-16">
            <h2 class="h4 text-center mb-4 sm:mb-8 text-custom-blue"><%= language === 'en' ? 'Download Categories' : '下載分類' %></h2>

            <!-- Filter Bar -->
            <div class="bg-white mb-4 relative">
                <form action="/<%= language %>/downloads" method="GET" class="flex flex-col">
                    <label for="category" class="w-auto mx-4 hidden"><%= language === 'en' ? 'Category' : '分類' %></label>
                    <select id="category" name="category"
                        class="w-full text-gray-600 px-4 py-4 border rounded-lg focus:ring-0 appearance-none">
                        <option value=""><%= language === 'tw' ? '選擇分類' : 'Select Categories' %></option>
                        <% categories.forEach(function(category) { %>
                            <option value="<%= category.id %>"
                                <%=filters.category===category.id.toString() ? 'selected' : '' %>>
                                <%= category.name %>
                            </option>
                            <% }); %>
                    </select>
                    <button class="absolute right-3 bottom-4" type="submit">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true" role="img" aria-labelledby="filterIconTitle">
                            <title id="filterIconTitle"><%= language === 'en' ? 'Filter' : '篩選' %></title>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                </form>
                <!-- 自定義下拉箭頭 -->
                <!-- <div class="pointer-events-none absolute right-8 bottom-4 flex items-center px-4 text-gray-500">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </div> -->
            </div>

            <!-- Search Bar -->
            <div class="relative">
                <form action="/<%= language %>/downloads" method="GET" class="flex flex-col">
                    <label for="search" class="w-auto mx-4 hidden"><%= language === 'en' ? 'Search' : '搜尋' %></label>
                    <input type="text" id="search" name="search" placeholder="<%= language === 'tw' ? '搜尋下載項目...' : 'Search downloads...' %>" value="<%= filters.search %>"
                        class="w-full px-4 py-4 border rounded-xl">
                    <button class="absolute right-3 bottom-4" type="submit">
                        <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true" role="img" aria-labelledby="searchIconTitle">
                            <title id="searchIconTitle"><%= language === 'en' ? 'Search' : '搜尋' %></title>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Download List -->
    
    <section id="downloadList" class="px-6 pb-8 sm:px-16 sm:pb-16 lg:px-32">
        <% if (locals.downloads && downloads.length> 0) { %>
            <% 
                // 建立三層結構：分類 > 分組 > 下載項目
                const hierarchicalDownloads = {};

                // 將下載項目按分類和分組進行層次分組
                downloads.forEach(download => {
                    // 使用分類名稱作為第一層
                    const categoryName = download.category ? 
                        (language === 'en' ? download.category.name_en : download.category.name_tw) 
                        : (language === 'en' ? 'Uncategorized' : '未分類');
                    
                    // 使用分組名稱作為第二層，優先使用group關聯，然後使用舊的group_name字段
                    let groupName;
                    if (download.group) {
                        groupName = language === 'en' ? download.group.name_en : download.group.name_tw;
                    } else {
                        groupName = language === 'en' ? 
                            (download.group_name_en || 'Others') : 
                            (download.group_name_tw || '其他');
                    }
                    
                    if (!hierarchicalDownloads[categoryName]) {
                        hierarchicalDownloads[categoryName] = {};
                    }
                    
                    if (!hierarchicalDownloads[categoryName][groupName]) {
                        hierarchicalDownloads[categoryName][groupName] = [];
                    }
                    
                    hierarchicalDownloads[categoryName][groupName].push(download);
                });

                // 遍歷每個分類
                Object.keys(hierarchicalDownloads).forEach((categoryName, categoryIndex) => {
                %>

                <!-- 分類分隔線 -->
                <% if (categoryIndex > 0) { %>
                    <div class="my-12">
                        <hr class="border-t-4 border-blue-300">
                    </div>
                <% } %>

                <!-- 分類標題 -->
                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-blue-600 mb-2">
                        <i class="fas fa-folder mr-2"></i><%= categoryName %>
                    </h2>
                </div>

                <% 
                    const groupsInCategory = hierarchicalDownloads[categoryName];
                    // 遍歷該分類下的每個分組
                    Object.keys(groupsInCategory).forEach((groupName, groupIndex) => {
                %>

                    <!-- 分組分隔線 -->
                    <% if (groupIndex > 0) { %>
                        <div class="my-6">
                            <hr class="border-t-2 border-gray-200">
                        </div>
                    <% } %>

                    <!-- 分組標題 -->
                    <h3 class="text-lg font-semibold text-gray-700 mb-4">
                        <i class="fas fa-layer-group mr-2"></i><%= groupName %>
                    </h3>
                    
                    <!-- 該分組下的所有下載項目 -->
                    <div class="ml-4">
                        <% groupsInCategory[groupName].forEach(function(download) { %>
                            <!-- 橫幅卡片 -->
                            <div class="bg-gray-100 rounded-2xl mb-4">
                                <div class="flex items-center justify-between px-6 py-2">
                                    <!-- 標題部分 -->
                                    <div class="flex items-center">
                                        <i class="fas fa-file-download mr-3 text-gray-600"></i>
                                        <div>
                                            <p class="font-medium"><%= download.title %></p>
                                            <% if (download.description) { %>
                                                <p class="text-sm text-gray-600"><%= download.description %></p>
                                            <% } %>
                                        </div>
                                    </div>
                                    
                                    <!-- 下載按鈕部分 -->
                                    <a href="/<%= language %>/downloads/<%= download.id %>" title="<%= download.originalName %>" class="inline-flex items-center px-0 sm:px-5 py-0 hover:bg-gray-200 rounded-lg transition-colors">
                                        <img src="/images/desktop/downloadcloud.png" alt="下載雲" class="h-8 mr-0 sm:mr-2 object-contain">
                                        <span class="hidden sm:block underline text-blue-600"><%= language === 'en' ? 'Download' : '檔案下載' %></span>
                                    </a>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                <% }); %>
            <% }); %>
        <% } else { %>
            <div>
                <h3 class="h5 mb-6">
                    <%= language === 'en' ? 'No files' : '查無資料' %>
                </h3>
            </div>
        <% } %>
    </section>
    