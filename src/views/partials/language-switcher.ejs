<div class="language-switcher block flex mx-4 items-center relative">
    <a href="#" id="language-toggle" class="text-gray-600 hover:text-gray-900">
        <span class="hidden"><%= currentLanguage==='en' ? 'Language menu' : '語言選單' %></span>
        <svg class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" alt="<%= currentLanguage==='en' ? 'Language menu' : '語言選單' %>">
            <circle cx="12" cy="12" r="10" stroke-width="2"/>
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10" stroke-width="2"/>
            <path d="M2 12h20M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z" stroke-width="2"/>
        </svg>
    </a>
    <div id="language-list" class="absolute top-full w-24 right-0 bg-white shadow-md rounded-md py-2 px-3 hidden">
        <a href="<%= switchLanguageUrl('tw') %>" class="<%= currentLanguage === 'tw' ? 'font-bold' : 'text-gray-600 hover:text-gray-900' %>">
            <span class="ml-1">中文</span>
        </a>
        <a href="<%= switchLanguageUrl('en') %>" class="<%= currentLanguage === 'en' ? 'font-bold' : 'text-gray-600 hover:text-gray-900' %>">
            <span class="ml-1">EN</span>
        </a>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const languageToggle = document.getElementById('language-toggle');
        const languageList = document.getElementById('language-list');
        
        // 點擊 SVG 時切換語言列表的顯示
        languageToggle.addEventListener('click', function(e) {
            e.preventDefault();
            languageList.classList.toggle('hidden');
        });
        
        // 點擊外部區域時關閉語言列表
        document.addEventListener('click', function(e) {
            if (!languageToggle.contains(e.target) && !languageList.contains(e.target)) {
                languageList.classList.add('hidden');
            }
        });
    });
</script>