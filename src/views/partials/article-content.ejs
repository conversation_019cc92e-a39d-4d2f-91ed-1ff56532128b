<h1 class="text-3xl font-bold text-gray-900">
    <%= article.title %>
</h1>

<% if (article.category) { %>
    <div class="mt-2">
        <span class="inline-flex items-center px-3 py-0.5 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
            <% const language = locals.currentLanguage || 'en'; %>
            <% const categoryNameField = `name_${language}`; %>
            <%= article.category[categoryNameField] || article.category.name_en %>
        </span>
    </div>
<% } %>

<div class="mt-6 prose prose-blue prose-lg max-w-none article-content">
    <%- article.content %>
</div>

<div class="mt-6 border-t pt-6">
    <p class="text-sm text-gray-500">
        <% const language = locals.currentLanguage || 'en'; %>
        <%= language === 'en' ? 'Published on' : '發布於' %> <%= new Date(article.createdAt).toLocaleDateString(language === 'en' ? 'en-US' : 'zh-TW') %>
        <% if (article.author) { %>
            <%= language === 'en' ? 'by' : '作者' %> <%= article.author.username %>
        <% } %>
    </p>
</div>