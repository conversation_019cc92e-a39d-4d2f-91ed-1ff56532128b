const { metrics } = require('../config/metrics');
const promClient = require('prom-client');

// Create status code-specific metrics
const http2xxRequestsTotal = new promClient.Counter({
  name: 'http_2xx_requests_total',
  help: 'Total number of HTTP 2xx (success) responses',
  labelNames: ['method', 'route']
});

const http4xxRequestsTotal = new promClient.Counter({
  name: 'http_4xx_requests_total',
  help: 'Total number of HTTP 4xx (client error) responses',
  labelNames: ['method', 'route']
});

const http5xxRequestsTotal = new promClient.Counter({
  name: 'http_5xx_requests_total',
  help: 'Total number of HTTP 5xx (server error) responses',
  labelNames: ['method', 'route']
});

const httpResponseTime = new promClient.Histogram({
  name: 'http_response_time_seconds',
  help: 'Duration of HTTP responses in seconds',
  labelNames: ['method', 'route', 'status_code'],
  buckets: [0.01, 0.05, 0.1, 0.5, 1, 2, 5]
});

// Register the metrics
if (metrics.register) {
  metrics.register.registerMetric(http2xxRequestsTotal);
  metrics.register.registerMetric(http4xxRequestsTotal);
  metrics.register.registerMetric(http5xxRequestsTotal);
  metrics.register.registerMetric(httpResponseTime);
}

/**
 * Middleware to track HTTP response metrics by status code
 */
const httpMetricsMiddleware = (req, res, next) => {
  // Skip metrics tracking for the metrics endpoint itself
  if (req.path === '/metrics') {
    return next();
  }

  // Start timing the request
  const startTime = process.hrtime();

  // Store original end method
  const originalEnd = res.end;

  // Override end method to capture metrics before response is sent
  res.end = function(chunk, encoding) {
    // Calculate response time
    const [seconds, nanoseconds] = process.hrtime(startTime);
    const duration = seconds + nanoseconds / 1e9;

    // Get cleaned path (remove query params and ids for more meaningful grouping)
    let route = req.route ? req.route.path : req.path;
    
    // Add general HTTP duration metric
    httpResponseTime.observe({
      method: req.method,
      route,
      status_code: res.statusCode
    }, duration);

    // Increment counters based on status code
    const statusCode = res.statusCode;
    
    // 2xx Success responses
    if (statusCode >= 200 && statusCode < 300) {
      http2xxRequestsTotal.inc({
        method: req.method,
        route
      });
    } 
    // 4xx Client errors
    else if (statusCode >= 400 && statusCode < 500) {
      http4xxRequestsTotal.inc({
        method: req.method,
        route
      });
    } 
    // 5xx Server errors
    else if (statusCode >= 500) {
      http5xxRequestsTotal.inc({
        method: req.method,
        route
      });
    }

    // Call original end method
    return originalEnd.call(this, chunk, encoding);
  };

  next();
};

module.exports = {
  httpMetricsMiddleware,
  metrics: {
    http2xxRequestsTotal,
    http4xxRequestsTotal,
    http5xxRequestsTotal,
    httpResponseTime
  }
}; 