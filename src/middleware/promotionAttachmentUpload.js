const multer = require('multer');
const path = require('path');
const fs = require('fs');

// Create upload directory if it doesn't exist
const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'promotions', 'attachments');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// Configure multer storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        // Generate unique filename with timestamp
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        const filename = 'promotion-attachment-' + uniqueSuffix + ext;
        cb(null, filename);
    }
});

// File filter to allow specific file types
const fileFilter = (req, file, cb) => {
    // Allowed file types
    const allowedTypes = [
        // Documents
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        // Archives
        'application/zip',
        'application/x-rar-compressed',
        'application/x-7z-compressed',
        // Images
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        // Text
        'text/plain',
        'text/csv'
    ];

    if (allowedTypes.includes(file.mimetype)) {
        cb(null, true);
    } else {
        cb(new Error('不支援的檔案格式。請上傳 PDF、Word、Excel、PowerPoint、壓縮檔或圖片檔案。'), false);
    }
};

// Configure multer
const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 50 * 1024 * 1024, // 50MB limit
    }
});

// Middleware function
const promotionAttachmentUpload = (req, res, next) => {
    const singleUpload = upload.single('file');
    
    singleUpload(req, res, function (err) {
        if (err instanceof multer.MulterError) {
            if (err.code === 'LIMIT_FILE_SIZE') {
                req.flash('error_msg', '檔案大小超過限制（最大 50MB）');
            } else {
                req.flash('error_msg', '檔案上傳錯誤：' + err.message);
            }
            return res.redirect('back');
        } else if (err) {
            req.flash('error_msg', err.message);
            return res.redirect('back');
        }

        // Add relative path to req.file for database storage
        if (req.file) {
            req.file.relativePath = path.join('uploads', 'promotions', 'attachments', req.file.filename);
        }

        next();
    });
};

module.exports = promotionAttachmentUpload;
