const logger = require('../config/logger');

/**
 * HTTP Logging Middleware
 * 
 * This middleware:
 * 1. Adds request/response logging with context
 * 2. Tracks response time
 * 3. Captures user agent, IP, host, status code, and language
 */
function httpLoggingMiddleware(req, res, next) {
  try {
    // If OpenTelemetry httpLoggerMiddleware is available, use it and skip the fallback
    if (typeof logger.httpLoggerMiddleware === 'function') {
      return logger.httpLoggerMiddleware(req, res, next);
    }
    
    // For paths that don't need logging, skip
    const skippedPaths = ['/health', '/favicon.ico', '/__webpack_hmr'];
    if (skippedPaths.some(path => req.path.startsWith(path))) {
      return next();
    }
    
    // Skip logging for static files
    const isStaticFile = /\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/.test(req.path);
    if (isStaticFile) {
      return next();
    }
    
    // Fallback implementation if httpLoggerMiddleware is not available
    const startTime = Date.now();
    
    // Track response completion
    const originalEnd = res.end;
    res.end = function(...args) {
      try {
        // Calculate response time
        const responseTime = Date.now() - startTime;
        res.locals.responseTime = responseTime;
        
        // Skip logging for successful static file responses
        if (isStaticFile && res.statusCode === 200) {
          return originalEnd.apply(this, args);
        }
        
        // Skip logging for fast, successful requests in production
        const isFastSuccessfulRequest = res.statusCode === 200 && responseTime < 100;
        if (process.env.NODE_ENV === 'production' && isFastSuccessfulRequest) {
          return originalEnd.apply(this, args);
        }
        
        // Enhanced client IP detection with fallbacks and proxy support
        const getClientIp = (req) => {
          // Check for proxy headers first
          if (req.headers['x-forwarded-for']) {
            // Extract first IP which is typically the client IP in a proxy chain
            const forwardedIps = req.headers['x-forwarded-for'].split(',');
            const clientIp = forwardedIps[0].trim();
            return clientIp;
          }
          
          // Try req.ip which is set by Express when trust proxy is enabled
          if (req.ip) {
            return req.ip;
          }
          
          // Fallback to socket address
          if (req.connection && req.connection.remoteAddress) {
            return req.connection.remoteAddress;
          }
          
          // Last resort
          if (req.socket && req.socket.remoteAddress) {
            return req.socket.remoteAddress;
          }
          
          return 'unknown';
        };
        
        const clientIp = getClientIp(req);
        
        // Log using standard logger with structured data
        logger.info('HTTP Request', {
          method: req.method,
          url: req.originalUrl || req.url,
          status: res.statusCode,
          responseTime,
          ip: clientIp,
          userAgent: req.headers['user-agent'],
          referrer: req.headers.referer || req.headers.referrer,
          contentLength: res.getHeader('content-length'),
          contentType: res.getHeader('content-type')
        });
      } catch (error) {
        // Fail silently but log to console
        console.error('Error in HTTP logging middleware:', error);
      }
      
      // Call the original end method
      return originalEnd.apply(this, args);
    };
    
    next();
  } catch (error) {
    // If anything fails, log it and continue the middleware chain
    console.error('Error in HTTP logging middleware setup:', error);
    next();
  }
}

module.exports = httpLoggingMiddleware; 