const multer = require('multer');
const path = require('path');
const fs = require('fs');
const logger = require('../config/logger');

// Get allowed file types from environment variables or use defaults
const ALLOWED_IMAGE_TYPES = process.env.ALLOWED_IMAGE_TYPES || 'jpg,jpeg,png,gif,webp';
const ALLOWED_ATTACHMENT_TYPES = process.env.ALLOWED_ATTACHMENT_TYPES || 'pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar';

// Parse the comma-separated lists into arrays
const allowedImageExtensions = ALLOWED_IMAGE_TYPES.split(',').map(ext => `.${ext.trim().toLowerCase()}`);
const allowedAttachmentExtensions = ALLOWED_ATTACHMENT_TYPES.split(',').map(ext => `.${ext.trim().toLowerCase()}`);

// Create uploads directories if they don't exist
const uploadDir = path.join(__dirname, '../../public/uploads/platform');
const attachmentDir = path.join(uploadDir, 'attachments');

[uploadDir, attachmentDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    logger.info(`Created directory: ${dir}`);
  }
});

// Configure storage for images and attachments
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    // Store attachments in a separate directory
    const dest = file.fieldname === 'attachments' ? attachmentDir : uploadDir;
    cb(null, dest);
  },
  filename: function (req, file, cb) {
    // Generate a unique filename with timestamp and random number
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    // Properly handle Chinese filename encoding
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
    const ext = path.extname(originalName);
    const baseName = path.basename(originalName, ext);
    const prefix = file.fieldname === 'attachments' ? 'attachment' : 'platform';
    // Keep the original filename with Chinese characters - don't encode it
    // The filesystem can handle UTF-8 filenames properly
    cb(null, `${prefix}-${uniqueSuffix}-${baseName}${ext}`);
  }
});

// Map of common file extensions to MIME types
const mimeTypeMap = {
  // Image types
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.png': 'image/png',
  '.gif': 'image/gif',
  '.webp': 'image/webp',

  // Document types
  '.pdf': 'application/pdf',
  '.doc': 'application/msword',
  '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  '.xls': 'application/vnd.ms-excel',
  '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  '.ppt': 'application/vnd.ms-powerpoint',
  '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  '.txt': 'text/plain',
  '.zip': 'application/zip',
  '.rar': 'application/x-rar-compressed'
};

// Generate allowed MIME types based on allowed extensions
const allowedImageMimeTypes = allowedImageExtensions
  .map(ext => mimeTypeMap[ext])
  .filter(Boolean);

const allowedAttachmentMimeTypes = allowedAttachmentExtensions
  .map(ext => mimeTypeMap[ext])
  .filter(Boolean);

// File filter for images - accepts all file types
const imageFilter = (req, file, cb) => {
  // Properly handle Chinese filename encoding
  const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');

  // Accept all file types
  cb(null, true);
};

// File filter for attachments - accepts all file types
const attachmentFilter = (req, file, cb) => {
  // Properly handle Chinese filename encoding
  const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
  // Accept all file types
  cb(null, true);
};

// Configure multer without file type filtering
const upload = multer({
  storage: storage,
  // No file filtering - accept all files
  limits: {
    fileSize: file => {
      // Use environment variables for file size limits or default to 5MB for images, 50MB for attachments
      const maxImageSize = parseInt(process.env.MAX_IMAGE_SIZE) || 5 * 1024 * 1024;
      const maxAttachmentSize = parseInt(process.env.MAX_ATTACHMENT_SIZE) || 50 * 1024 * 1024;
      return file.fieldname === 'attachments' ? maxAttachmentSize : maxImageSize;
    },
    // Add a high fieldSize limit to handle large base64 encoded images in form fields
    fieldSize: parseInt(process.env.MAX_UPLOAD_SIZE) || 100 * 1024 * 1024 // Use MAX_UPLOAD_SIZE from env or default to 100MB
  }
});

module.exports = upload;