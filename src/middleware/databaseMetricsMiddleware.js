const { metrics, register, enableMetrics } = require('../config/metrics');
const promClient = require('prom-client');

// Create MySQL specific metrics
const prismaPoolConnectionsTotal = new promClient.Gauge({
  name: 'prisma_pool_connections_total',
  help: 'The total number of connections in the pool',
  labelNames: ['database']
});

const prismaPoolConnectionsBusy = new promClient.Gauge({
  name: 'prisma_pool_connections_busy',
  help: 'The number of active connections in use',
  labelNames: ['database']
});

const prismaPoolConnectionsIdle = new promClient.Gauge({
  name: 'prisma_pool_connections_idle',
  help: 'The number of idle connections in the pool',
  labelNames: ['database']
});

const prismaClientQueriesTotal = new promClient.Counter({
  name: 'prisma_client_queries_total',
  help: 'Total number of Prisma Client queries executed',
  labelNames: ['model', 'operation']
});

// Register Prisma metrics
if (enableMetrics && register) {
  register.registerMetric(prismaPoolConnectionsTotal);
  register.registerMetric(prismaPoolConnectionsBusy);
  register.registerMetric(prismaPoolConnectionsIdle);
  register.registerMetric(prismaClientQueriesTotal);
}

// Function to collect Prisma metrics
const collectPrismaMetrics = async (prisma) => {
  if (!enableMetrics || !prisma.$metrics) {
    return;
  }

  try {
    // Get the database name
    const dbName = process.env.DATABASE_URL 
      ? new URL(process.env.DATABASE_URL).pathname.replace('/', '') 
      : 'default';

    // Get Prisma metrics in JSON format
    const metricsData = await prisma.$metrics.json();
    
    if (metricsData) {
      // Update pool connection metrics
      if (metricsData.pool) {
        prismaPoolConnectionsTotal.set({ database: dbName }, metricsData.pool.size || 0);
        prismaPoolConnectionsBusy.set({ database: dbName }, metricsData.pool.busy || 0);
        prismaPoolConnectionsIdle.set({ database: dbName }, metricsData.pool.idle || 0);
      }
      
      // Update query metrics
      if (metricsData.queries) {
        // Aggregate queries by model and operation
        const queryStats = {};
        
        metricsData.queries.forEach(query => {
          const key = `${query.model || 'unknown'}-${query.operation || 'unknown'}`;
          if (!queryStats[key]) {
            queryStats[key] = 0;
          }
          queryStats[key]++;
        });
        
        // Update the counter for each model/operation combination
        Object.entries(queryStats).forEach(([key, count]) => {
          const [model, operation] = key.split('-');
          const current = prismaClientQueriesTotal.get({ model, operation }) || 0;
          const increment = count - current;
          
          if (increment > 0) {
            prismaClientQueriesTotal.inc({ model, operation }, increment);
          }
        });
      }
    }
  } catch (error) {
    console.error('Failed to collect Prisma metrics:', error);
  }
};

// Create a middleware function to wrap Prisma queries with metrics
const trackDatabaseMetrics = (prisma) => {
  // If metrics are disabled, return the original prisma instance
  if (!enableMetrics) {
    return prisma;
  }
  
  // Set up periodic collection of Prisma metrics
  const interval = parseInt(process.env.METRICS_COLLECT_INTERVAL || '15000', 10);
  setInterval(() => collectPrismaMetrics(prisma), interval);
  
  // Get all model names that have query methods
  const models = Object.keys(prisma).filter(key => 
    typeof prisma[key] === 'object' && 
    prisma[key] !== null &&
    !['$connect', '$disconnect', '$on', '$transaction', '$use', '$extends', '$metrics'].includes(key)
  );

  // For each model, wrap its methods with metrics
  models.forEach(modelName => {
    // Skip if not an actual model
    if (modelName.startsWith('$')) return;
    
    const model = prisma[modelName];
    const operations = [
      'findUnique', 'findMany', 'findFirst', 
      'create', 'createMany', 
      'update', 'updateMany', 
      'upsert', 
      'delete', 'deleteMany', 
      'count', 
      'aggregate', 
      'groupBy'
    ];

    // For each operation method that exists on the model, wrap it with metrics
    operations.forEach(operation => {
      if (typeof model[operation] === 'function') {
        const originalMethod = model[operation];
        
        // Replace the method with a wrapper that records metrics
        model[operation] = async function(...args) {
          // Increment query counter
          metrics.databaseQueriesTotal.inc({
            operation,
            model: modelName
          });
          
          // Measure query duration
          const startTime = process.hrtime();
          
          try {
            // Execute the original query
            const result = await originalMethod.apply(this, args);
            
            // Calculate duration in seconds
            const [seconds, nanoseconds] = process.hrtime(startTime);
            const duration = seconds + nanoseconds / 1e9;
            
            // Record query duration
            metrics.databaseQueryDurationSeconds.observe(
              { operation, model: modelName },
              duration
            );
            
            return result;
          } catch (error) {
            // Still record duration even if query fails
            const [seconds, nanoseconds] = process.hrtime(startTime);
            const duration = seconds + nanoseconds / 1e9;
            
            metrics.databaseQueryDurationSeconds.observe(
              { operation, model: modelName },
              duration
            );
            
            // Re-throw the error
            throw error;
          }
        };
      }
    });
  });

  // Add a /metrics/prisma endpoint for Prometheus format metrics
  const getPrometheusMetrics = async () => {
    if (!prisma.$metrics) {
      return "# Prisma metrics are not available\n";
    }
    
    try {
      // Get Prisma metrics in Prometheus format
      return await prisma.$metrics.prometheus();
    } catch (error) {
      console.error('Failed to get Prisma metrics in Prometheus format:', error);
      return "# Error getting Prisma metrics\n";
    }
  };

  // Add the getPrometheusMetrics function to the prisma client for external use
  prisma.getPrometheusMetrics = getPrometheusMetrics;

  return prisma;
};

module.exports = { 
  trackDatabaseMetrics,
  prismaMetrics: {
    prismaPoolConnectionsTotal,
    prismaPoolConnectionsBusy,
    prismaPoolConnectionsIdle,
    prismaClientQueriesTotal
  }
}; 