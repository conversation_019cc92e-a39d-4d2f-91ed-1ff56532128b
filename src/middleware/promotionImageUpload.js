const multer = require('multer');
const path = require('path');
const fs = require('fs');
const logger = require('../config/logger');

// Get allowed file types from environment variables or use defaults
const ALLOWED_PROMOTION_IMAGE_TYPES = process.env.ALLOWED_PROMOTION_IMAGE_TYPES || 'jpg,jpeg,png,gif,webp';
const MAX_PROMOTION_IMAGE_SIZE = parseInt(process.env.MAX_PROMOTION_IMAGE_SIZE) || 5 * 1024 * 1024; // 5MB default

// Parse allowed types
const allowedExtensions = ALLOWED_PROMOTION_IMAGE_TYPES.split(',').map(ext => `.${ext.trim().toLowerCase()}`);

// Map of common file extensions to MIME types
const mimeTypeMap = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp'
};

// Generate allowed MIME types based on allowed extensions
const allowedMimeTypes = allowedExtensions
    .map(ext => mimeTypeMap[ext])
    .filter(Boolean);

// Create uploads directory if it doesn't exist
const uploadDir = path.join(__dirname, '../../public/uploads/promotions');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
    logger.info(`Created directory: ${uploadDir}`);
}

// Define the URL path for serving the images (without 'public' prefix)
const urlPath = 'uploads/promotions';

// Configure storage
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        // Create year/month folder structure
        const now = new Date();
        const yearMonth = `${now.getFullYear()}/${(now.getMonth() + 1).toString().padStart(2, '0')}`;
        const uploadPath = `${uploadDir}/${yearMonth}`;

        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }

        cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
        // Generate unique filename
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        const fieldName = file.fieldname === 'imageEn' ? 'promotion-en' : 'promotion';
        cb(null, `${fieldName}-${uniqueSuffix}${ext}`);
    }
});

// File filter - accepts all file types for images
const fileFilter = (req, file, cb) => {
    // Properly handle Chinese filename encoding
    const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');

    // Accept all file types
    cb(null, true);
};

// Create multer upload instance
const upload = multer({
    storage,
    limits: {
        fileSize: MAX_PROMOTION_IMAGE_SIZE,
        fieldSize: parseInt(process.env.MAX_UPLOAD_SIZE) || 100 * 1024 * 1024 // Use MAX_UPLOAD_SIZE from env or default to 100MB for field size
    }
    // No file filter - accept all file types
});

// Middleware function
const promotionImageUpload = (req, res, next) => {
    logger.info('Promotion image upload middleware started');

    // Log the request content type to debug multipart/form-data issues
    logger.info(`Request Content-Type: ${req.headers['content-type']}`);

    const uploadHandler = upload.fields([
        { name: 'image', maxCount: 1 },
        { name: 'imageEn', maxCount: 1 }
    ]);

    uploadHandler(req, res, (err) => {
        if (err) {
            logger.error(`Error uploading promotion image: ${err.message}`, err);
            req.flash('error_msg', `Error uploading image: ${err.message}`);

            // Determine where to redirect based on the route
            const redirectPath = req.path.includes('/edit/')
                ? `/admin/promotions/items/edit/${req.params.id}`
                : '/admin/promotions/items/create';

            return res.redirect(redirectPath);
        }

        // Log success information
        if (req.files) {
            // Process each uploaded file
            if (req.files.image && req.files.image.length > 0) {
                const file = req.files.image[0];
                // Store the URL path (not the filesystem path) in req.file.url for easy access
                // This is what should be used for displaying the image in HTML
                const yearMonth = new Date().toISOString().slice(0, 7).replace('-', '/');
                file.url = `/${urlPath}/${yearMonth}/${file.filename}`;

                // Store the relative path (without 'public') for database storage
                file.relativePath = `${urlPath}/${yearMonth}/${file.filename}`;

                logger.info(`Chinese image uploaded successfully: ${JSON.stringify({
                    filename: file.filename,
                    originalname: file.originalname,
                    mimetype: file.mimetype,
                    size: file.size,
                    path: file.path,
                    url: file.url,
                    relativePath: file.relativePath
                })}`);
            }

            if (req.files.imageEn && req.files.imageEn.length > 0) {
                const file = req.files.imageEn[0];
                // Store the URL path for English image
                const yearMonth = new Date().toISOString().slice(0, 7).replace('-', '/');
                file.url = `/${urlPath}/${yearMonth}/${file.filename}`;

                // Store the relative path for database storage
                file.relativePath = `${urlPath}/${yearMonth}/${file.filename}`;

                logger.info(`English image uploaded successfully: ${JSON.stringify({
                    filename: file.filename,
                    originalname: file.originalname,
                    mimetype: file.mimetype,
                    size: file.size,
                    path: file.path,
                    url: file.url,
                    relativePath: file.relativePath
                })}`);
            }
        } else {
            logger.info('No files were uploaded (this is normal if no image fields were provided)');
        }

        next();
    });
};

module.exports = promotionImageUpload;
