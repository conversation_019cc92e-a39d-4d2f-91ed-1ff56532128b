const logger = require('../config/logger');
const prisma = require('../lib/prisma');
const { COMMON_ERROR_CODES, AUTH_ERROR_CODES, VISIT_ERROR_CODES } = require('../config/errorCodes');

// Ensure user is authenticated
const isAuthenticated = async (req, res, next) => {
    // Skip auth check for public routes
    const publicRoutes = ['/login', '/logout', '/guest-login'];
    if (publicRoutes.includes(req.path)) {
        return next();
    }

    if (!req.session.user) {
        req.flash('error_msg', '請登錄以訪問此資源');
        return res.redirect('/admin/login');
    }

    try {
        const user = await prisma.user.findUnique({
            where: { id: req.session.user.id },
            select: {
                id: true,
                username: true,
                email: true,
                isActive: true,
                contactName_zh: true,
                contactName_en: true,
                contactPhone: true,
                organizationName_zh: true,
                organizationName_en: true,
                role: {
                    select: {
                        id: true,
                        name: true,
                        permissions: true
                    }
                }
            }
        });

        if (!user) {
            req.session.destroy();
            req.flash('error_msg', '請登錄以訪問此資源');
            return res.redirect('/admin/login');
        }

        // Update session with fresh user data
        req.session.user = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role.name,
            permissions: user.role.permissions,
            contactName_zh: user.contactName_zh || null,
            contactName_en: user.contactName_en || null,
            contactPhone: user.contactPhone || null,
            organizationName_zh: user.organizationName_zh || null,
            organizationName_en: user.organizationName_en || null
        };

        // Add user to locals for views
        res.locals.user = req.session.user;
        next();
    } catch (error) {
        logger.error('Error authenticating user:', error);
        req.flash('error_msg', '發生錯誤，請重試。');
        return res.redirect('/admin/login');
    }
};

// Ensure user has required role
const hasRole = (roles) => {
    return (req, res, next) => {
        if (!req.session.user) {
            req.flash('error_msg', '請登錄以訪問此資源');
            logger.warn(`${AUTH_ERROR_CODES.UNAUTHORIZED_ACCESS}: Unauthorized access attempt to ${req.originalUrl}`);
            return res.redirect('/admin/login');
        }

        const userRole = req.session.user.role;

        // Convert roles to array if it's a single string
        const requiredRoles = Array.isArray(roles) ? roles : [roles];

        if (!requiredRoles.includes(userRole)) {
            req.flash('error_msg', '您沒有權限訪問此資源');
            logger.warn(`${COMMON_ERROR_CODES.FORBIDDEN}: User ${req.session.user.username} (${userRole}) attempted to access ${req.originalUrl}`);
            return res.redirect('/admin/dashboard');
        }

        next();
    };
};

// Check if user has specific permission
const hasPermission = (permission) => {
    return (req, res, next) => {
        if (!req.session.user) {
            req.flash('error_msg', '請登錄以訪問此資源');
            logger.warn(`${AUTH_ERROR_CODES.UNAUTHORIZED_ACCESS}: Unauthorized access attempt to ${req.originalUrl}`);
            return res.redirect('/admin/login');
        }

        const userRole = req.session.user.role;
        const userPermissions = req.session.user.permissions || [];

        // Special handling for guest users
        if (userRole === 'guest') {
            // Allow guests to access only dashboard and visits pages
            if (req.path === '/dashboard' || req.path.startsWith('/visits')) {
                return next();
            } else {
                // For any other route, redirect to visits
                req.flash('info_msg', '訪客用戶只能訪問訪廠資訊頁面');
                logger.info(`${COMMON_ERROR_CODES.FORBIDDEN}: Guest user ${req.session.user.username} redirected from ${req.originalUrl} to /admin/visits`);
                return res.redirect('/admin/visits');
            }
        }

        // Special handling for visitor users (read-only access to visits)
        if (userRole === 'visitor') {
            // Visitor users only have access to visits pages
            if (req.path === '/visits' ||
                req.path.startsWith('/visits/view/')) {
                return next();
            } else {
                // For any route (including dashboard), redirect to visits
                req.flash('info_msg', '僅限查看用戶只能查看訪廠資訊');
                logger.info(`${VISIT_ERROR_CODES.PERMISSION_ERROR}: Visitor user ${req.session.user.username} attempted to access ${req.originalUrl}`);
                return res.redirect('/admin/visits');
            }
        }

        // Super admin has all permissions
        if (userRole === 'super_admin') {
            return next();
        }

        // Admin has most permissions except super admin ones
        if (userRole === 'admin' && !permission.startsWith('super_admin:')) {
            return next();
        }

        // For other roles, check permissions normally
        if (!userPermissions.includes(permission)) {
            req.flash('error_msg', '您沒有權限訪問此資源');
            logger.warn(`${COMMON_ERROR_CODES.FORBIDDEN}: User ${req.session.user.username} lacks permission ${permission} for ${req.originalUrl}`);

            // Avoid redirect loop - if we're already on dashboard, render access denied
            if (req.path === '/dashboard' && permission === 'access:dashboard') {
                return res.render('admin/access-denied', {
                    title: '拒絕訪問',
                    layout: 'layouts/admin',
                    user: req.session.user
                });
            }

            return res.redirect('/admin');
        }

        next();
    };
};

// Check if user owns the resource or has sufficient permissions
const isOwnerOrHasPermission = (permission, getResourceOwnerId) => {
    return async (req, res, next) => {
        if (!req.session.user) {
            req.flash('error_msg', '請登錄以訪問此資源');
            logger.warn(`${AUTH_ERROR_CODES.UNAUTHORIZED_ACCESS}: Unauthorized access attempt to ${req.originalUrl}`);
            return res.redirect('/admin/login');
        }

        const userRole = req.session.user.role;
        const userPermissions = req.session.user.permissions || [];

        // Super admin has all permissions
        if (userRole === 'super_admin') {
            return next();
        }

        // Guest users cannot modify content
        if (userRole === 'guest') {
            req.flash('info_msg', '訪客用戶不能修改內容');
            logger.info(`${COMMON_ERROR_CODES.FORBIDDEN}: Guest user ${req.session.user.username} attempted to modify content at ${req.originalUrl}`);
            return res.redirect('/admin/dashboard');
        }

        // Visitor users cannot modify content
        if (userRole === 'visitor') {
            req.flash('info_msg', '僅限查看用戶不能修改內容');
            logger.info(`${VISIT_ERROR_CODES.PERMISSION_ERROR}: Visitor user ${req.session.user.username} attempted to modify content at ${req.originalUrl}`);
            return res.redirect('/admin/visits');
        }

        try {
            // Get the resource owner's ID using the provided function
            const ownerId = await getResourceOwnerId(req);

            // Check if user is the owner
            if (ownerId === req.session.user.id) {
                return next();
            }

            // If not owner, check if they have the required permission
            if (!userPermissions.includes(permission)) {
                req.flash('error_msg', '您沒有權限訪問此資源');
                logger.warn(`${COMMON_ERROR_CODES.FORBIDDEN}: User ${req.session.user.username} is not the owner and lacks permission ${permission} for ${req.originalUrl}`);
                return res.redirect('/admin/dashboard');
            }

            next();
        } catch (error) {
            logger.error(`${COMMON_ERROR_CODES.INTERNAL_SERVER_ERROR}: Error checking resource ownership:`, error);
            req.flash('error_msg', '檢查權限時發生錯誤');
            return res.redirect('/admin/dashboard');
        }
    };
};

module.exports = {
    isAuthenticated,
    hasRole,
    hasPermission,
    isOwnerOrHasPermission
};
