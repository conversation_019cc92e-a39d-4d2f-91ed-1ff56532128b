const logger = require('../config/logger');

// List of suspicious paths that should automatically return 404
const SUSPICIOUS_PATHS = [
  // WordPress related
  'wp-admin',
  'wp-login',
  'wp-login.php',
  'wp-config',
  'wp-content',
  'wp-includes',
  
  // PHP related
  'phpmyadmin',
  'phpinfo',
  'phpinfo.php',
  'php.php',
  'info.php',
  
  // Common admin/login paths
  'admin.php',
  'admin/login',
  'administrator',
  'administrator.php',
  'login.php',
  'admin.html',
  'panel.php',
  'admin/panel',
  'login.html',
  'adminpanel',
  
  // Common exploits
  '.env',
  '.git',
  '.svn',
  'web.config',
  '.htaccess',
  
  // Shell access attempts
  'shell',
  'shell.php',
  'cmd',
  'cmd.php',
  'console',
  'console.php',
  
  // Database related
  'sqliteadmin',
  'mysql',
  'sql',
  'myadmin',
  'dbadmin',
  
  // CMS related (other than WordPress)
  'administrator/index.php', // Joomla
  'admin/index.php',         // <PERSON><PERSON>la
  'joomla',
  'drupal',
  'typo3',
  'magento',
  'shop',
  
  // Common API probing paths
  'api/v1/auth',
  'api/auth',
  'api/login',
  'api/admin',
  
  // Common vulnerability scanners
  'scripts',
  'awstats',
  'webalizer',
  
  // Misc
  'install',
  'install.php',
  'setup',
  'setup.php',
  'config',
  'config.php',
  'register',
  'register.php',
];

const isSuspiciousPath = (path) => {
  // Get path without leading slash and query parameters
  const normalizedPath = path.replace(/^\/+/, '').split('?')[0].split('#')[0].toLowerCase();
  
  // Direct matches (full path or as first directory)
  for (const suspiciousPath of SUSPICIOUS_PATHS) {
    if (normalizedPath === suspiciousPath || 
        normalizedPath.startsWith(suspiciousPath + '/') ||
        normalizedPath.endsWith('/' + suspiciousPath)) {
      return true;
    }
  }
  
  // Match PHP extensions on any path that's not explicitly allowed
  if (normalizedPath.endsWith('.php') && !normalizedPath.includes('allowed-php-path')) {
    return true;
  }
  
  // Extensions commonly used for exploits
  const suspiciousExtensions = ['.aspx', '.asp', '.cgi', '.pl', '.jsp', '.dll'];
  for (const ext of suspiciousExtensions) {
    if (normalizedPath.endsWith(ext)) {
      return true;
    }
  }
  
  return false;
};

/**
 * Middleware function to block suspicious paths
 */
const blockSuspiciousPathsMiddleware = (req, res, next) => {
  // Skip for legitimate paths (admin routes, static files, etc.)
  if (req.path.startsWith('/admin') || 
      req.path.startsWith('/static') || 
      req.path.startsWith('/uploads') ||
      req.path === '/metrics' ||
      req.path === '/health') {
    return next();
  }
  
  // Check if the path is suspicious
  if (isSuspiciousPath(req.path)) {
    // Log the blocked attempt
    logger.warn(`Blocked suspicious path access: ${req.path}`, {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path,
      method: req.method
    });
    
    // Return 404 response
    return res.status(404).render('frontend/404', {
      title: '404 Not Found',
      layout: 'layouts/frontend'
    });
  }
  
  // Path is not suspicious, continue to next middleware
  next();
};

module.exports = blockSuspiciousPathsMiddleware; 