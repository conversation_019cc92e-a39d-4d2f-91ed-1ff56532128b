const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const { PROMOTION_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

// List all promotion groups
exports.listGroups = async (req, res) => {
  try {
    const groups = await prisma.promotionGroup.findMany({
      where: { deletedAt: null },
      orderBy: [
        { categoryId: 'asc' },
        { order: 'asc' }
      ],
      include: {
        category: true,
        _count: {
          select: { attachments: true }
        }
      }
    });

    res.render('admin/promotions/groups/list', {
      title: 'Promotion Groups',
      layout: 'layouts/admin',
      groups,
      success_msg: req.flash('success_msg'),
      error_msg: req.flash('error_msg')
    });
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.GROUP_LIST_FAILED || 'PG001',
      '載入推動方案分組失敗',
      '/admin/promotions'
    );
  }
};

// Render create group form
exports.renderCreateGroup = async (req, res) => {
  try {
    const categories = await prisma.promotionCategory.findMany({
      where: { deletedAt: null },
      orderBy: { order: 'asc' }
    });

    res.render('admin/promotions/groups/create', {
      title: 'Create Promotion Group',
      layout: 'layouts/admin',
      categories
    });
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.GROUP_CREATE_FORM_FAILED || 'PG002',
      '載入建立分組表單失敗',
      '/admin/promotions/groups'
    );
  }
};

// Create a new group
exports.createGroup = async (req, res) => {
  try {
    const { name_en, name_tw, description_en, description_tw, order, categoryId } = req.body;

    await prisma.promotionGroup.create({
      data: {
        name_en,
        name_tw,
        description_en,
        description_tw,
        order: order ? parseInt(order) : 0,
        categoryId: parseInt(categoryId)
      }
    });

    req.flash('success_msg', 'Promotion group created successfully');
    return res.redirect('/admin/promotions/groups');
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.GROUP_CREATE_FAILED || 'PG003',
      '建立推動方案分組失敗',
      '/admin/promotions/groups/create'
    );
  }
};

// Render edit group form
exports.renderEditGroup = async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return handleControllerError(
        new Error('Invalid group ID'),
        req,
        res,
        PROMOTION_ERROR_CODES?.INVALID_GROUP_ID || 'PG004',
        '無效的分組 ID',
        '/admin/promotions/groups'
      );
    }

    const [group, categories] = await Promise.all([
      prisma.promotionGroup.findUnique({
        where: { id },
        include: { category: true }
      }),
      prisma.promotionCategory.findMany({
        where: { deletedAt: null },
        orderBy: { order: 'asc' }
      })
    ]);

    if (!group) {
      return handleControllerError(
        new Error('Group not found'),
        req,
        res,
        PROMOTION_ERROR_CODES?.GROUP_NOT_FOUND || 'PG005',
        '找不到分組',
        '/admin/promotions/groups'
      );
    }

    res.render('admin/promotions/groups/edit', {
      title: 'Edit Promotion Group',
      layout: 'layouts/admin',
      group,
      categories
    });
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.GROUP_EDIT_FORM_FAILED || 'PG006',
      '載入推動方案分組失敗',
      '/admin/promotions/groups'
    );
  }
};

// Update a group
exports.updateGroup = async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return handleControllerError(
        new Error('Invalid group ID'),
        req,
        res,
        PROMOTION_ERROR_CODES?.INVALID_GROUP_ID || 'PG007',
        '無效的分組 ID',
        '/admin/promotions/groups'
      );
    }

    const { name_en, name_tw, description_en, description_tw, order, categoryId } = req.body;

    await prisma.promotionGroup.update({
      where: { id },
      data: {
        name_en,
        name_tw,
        description_en,
        description_tw,
        order: order ? parseInt(order) : 0,
        categoryId: parseInt(categoryId),
        updatedAt: new Date()
      }
    });

    req.flash('success_msg', '推動方案分組已成功更新');
    return res.redirect('/admin/promotions/groups');
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.GROUP_UPDATE_FAILED || 'PG008',
      '更新推動方案分組失敗',
      '/admin/promotions/groups'
    );
  }
};

// Delete a group
exports.deleteGroup = async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return handleControllerError(
        new Error('Invalid group ID'),
        req,
        res,
        PROMOTION_ERROR_CODES?.INVALID_GROUP_ID || 'PG009',
        '無效的分組 ID',
        '/admin/promotions/groups'
      );
    }

    await prisma.promotionGroup.update({
      where: { id },
      data: { deletedAt: new Date() }
    });

    req.flash('success_msg', '推動方案分組已成功刪除');
    return res.redirect('/admin/promotions/groups');
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.GROUP_DELETE_FAILED || 'PG010',
      '刪除推動方案分組失敗',
      '/admin/promotions/groups'
    );
  }
};

// API: Get groups by category (for dynamic loading)
exports.getGroupsByCategory = async (req, res) => {
  try {
    const categoryId = parseInt(req.params.categoryId, 10);

    if (isNaN(categoryId)) {
      return res.status(400).json({ error: 'Invalid category ID' });
    }

    const groups = await prisma.promotionGroup.findMany({
      where: {
        categoryId: categoryId,
        deletedAt: null
      },
      orderBy: { order: 'asc' }
    });

    res.json(groups);
  } catch (error) {
    logger.error('Error fetching groups by category:', error);
    res.status(500).json({ error: 'Failed to fetch groups' });
  }
};
