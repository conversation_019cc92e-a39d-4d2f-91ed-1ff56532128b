const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const { CONTACT_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

// Admin: List all contact categories
exports.listCategories = async (req, res) => {
    try {
        const categories = await prisma.contactCategory.findMany({
            where: {
                deletedAt: null
            },
            orderBy: {
                order: 'asc'
            }
        });
        
        res.render('admin/contact/categories/index', {
            title: 'Contact Categories',
            categories
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CONTACT_ERROR_CODES.CATEGORY_ERROR,
            '載入聯絡分類失敗',
            '/admin/dashboard'
        );
    }
};

// Admin: Render create contact category form
exports.renderCreateCategory = (req, res) => {
    try {
        res.render('admin/contact/categories/create', {
            title: 'Create Contact Category'
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CONTACT_ERROR_CODES.CATEGORY_ERROR,
            '載入建立分類表單失敗',
            '/admin/contact/categories'
        );
    }
};

// Admin: Create a new contact category
exports.createCategory = async (req, res) => {
    try {
        const { name_en, name_tw, order } = req.body;
        
        await prisma.contactCategory.create({
            data: {
                name_en,
                name_tw,
                order: order ? parseInt(order) : 0
            }
        });
        
        req.flash('success_msg', '聯絡分類已成功建立');
        res.redirect('/admin/contact/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CONTACT_ERROR_CODES.CATEGORY_ERROR,
            '建立聯絡分類失敗',
            '/admin/contact/categories/create'
        );
    }
};

// Admin: Render edit contact category form
exports.renderEditCategory = async (req, res) => {
    try {
        const { id } = req.params;
        
        const category = await prisma.contactCategory.findUnique({
            where: { id: parseInt(id) }
        });
        
        if (!category) {
            return handleControllerError(
                new Error('Contact category not found'),
                req,
                res,
                CONTACT_ERROR_CODES.CATEGORY_ERROR,
                '找不到聯絡分類',
                '/admin/contact/categories'
            );
        }
        
        res.render('admin/contact/categories/edit', {
            title: 'Edit Contact Category',
            category
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CONTACT_ERROR_CODES.CATEGORY_ERROR,
            '載入聯絡分類失敗',
            '/admin/contact/categories'
        );
    }
};

// Admin: Update a contact category
exports.updateCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const { name_en, name_tw, order } = req.body;
        
        await prisma.contactCategory.update({
            where: { id: parseInt(id) },
            data: {
                name_en,
                name_tw,
                order: order ? parseInt(order) : 0,
                updatedAt: new Date()
            }
        });
        
        req.flash('success_msg', '聯絡分類已成功更新');
        res.redirect('/admin/contact/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CONTACT_ERROR_CODES.CATEGORY_ERROR,
            '更新聯絡分類失敗',
            `/admin/contact/categories/edit/${req.params.id}`
        );
    }
};

// Admin: Delete a contact category
exports.deleteCategory = async (req, res) => {
    try {
        const { id } = req.params;
        
        // Soft delete the category
        await prisma.contactCategory.update({
            where: { id: parseInt(id) },
            data: {
                deletedAt: new Date()
            }
        });
        
        req.flash('success_msg', '聯絡分類已成功刪除');
        res.redirect('/admin/contact/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CONTACT_ERROR_CODES.CATEGORY_ERROR,
            '刪除聯絡分類失敗',
            '/admin/contact/categories'
        );
    }
}; 