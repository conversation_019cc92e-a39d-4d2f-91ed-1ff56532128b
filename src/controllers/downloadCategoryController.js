const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const { DOWNLOAD_CATEGORY_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

// List all download categories
exports.listCategories = async (req, res) => {
    try {
        const categories = await prisma.downloadCategory.findMany({
            where: { deletedAt: null },
            orderBy: { order: 'asc' },
            include: {
                _count: {
                    select: { downloads: true }
                }
            }
        });

        res.render('admin/downloads/categories/list', {
            title: 'Download Categories',
            layout: 'layouts/admin',
            categories,
            success_msg: req.flash('success_msg'),
            error_msg: req.flash('error_msg')
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_CATEGORY_ERROR_CODES.LIST_FAILED,
            '載入下載分類失敗',
            '/admin/downloads'
        );
    }
};

// Render create category form
exports.renderCreateCategory = (req, res) => {
    try {
        res.render('admin/downloads/categories/create', {
            title: 'Create Download Category',
            layout: 'layouts/admin'
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_CATEGORY_ERROR_CODES.CREATE_FORM_FAILED,
            '載入建立分類表單失敗',
            '/admin/downloads/categories'
        );
    }
};

// Create a new category
exports.createCategory = async (req, res) => {
    try {
        const { name_en, name_tw, description_en, description_tw, order } = req.body;

        await prisma.downloadCategory.create({
            data: {
                name_en,
                name_tw,
                description_en,
                description_tw,
                order: order ? parseInt(order) : 0
            }
        });

        req.flash('success_msg', 'Download category created successfully');
        return res.redirect('/admin/downloads/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_CATEGORY_ERROR_CODES.CREATE_FAILED,
            '建立下載分類失敗',
            '/admin/downloads/categories/create'
        );
    }
};

// Render edit category form
exports.renderEditCategory = async (req, res) => {
    try {
        // Parse the ID from the request params
        const id = parseInt(req.params.id, 10);
        
        if (isNaN(id)) {
            return handleControllerError(
                new Error('Invalid category ID'),
                req,
                res,
                DOWNLOAD_CATEGORY_ERROR_CODES.INVALID_ID,
                '無效的分類 ID',
                '/admin/downloads/categories'
            );
        }
        
        const category = await prisma.downloadCategory.findUnique({
            where: { id }
        });

        if (!category) {
            return handleControllerError(
                new Error('Category not found'),
                req,
                res,
                DOWNLOAD_CATEGORY_ERROR_CODES.NOT_FOUND,
                '找不到分類',
                '/admin/downloads/categories'
            );
        }

        res.render('admin/downloads/categories/edit', {
            title: 'Edit Download Category',
            layout: 'layouts/admin',
            category
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_CATEGORY_ERROR_CODES.EDIT_FORM_FAILED,
            '載入下載分類失敗',
            '/admin/downloads/categories'
        );
    }
};

// Update a category
exports.updateCategory = async (req, res) => {
    try {
        // Parse the ID from the request params
        const id = parseInt(req.params.id, 10);
        
        if (isNaN(id)) {
            return handleControllerError(
                new Error('Invalid category ID'),
                req,
                res,
                DOWNLOAD_CATEGORY_ERROR_CODES.INVALID_ID,
                '無效的分類 ID',
                '/admin/downloads/categories'
            );
        }
        
        const { name_en, name_tw, description_en, description_tw, order } = req.body;

        await prisma.downloadCategory.update({
            where: { id },
            data: {
                name_en,
                name_tw,
                description_en,
                description_tw,
                order: order ? parseInt(order) : 0,
                updatedAt: new Date()
            }
        });

        req.flash('success_msg', '下載分類已成功更新');
        return res.redirect('/admin/downloads/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_CATEGORY_ERROR_CODES.UPDATE_FAILED,
            '更新下載分類失敗',
            '/admin/downloads/categories'
        );
    }
};

// Delete a category
exports.deleteCategory = async (req, res) => {
    try {
        // Parse the ID from the request params
        const id = parseInt(req.params.id, 10);
        
        if (isNaN(id)) {
            return handleControllerError(
                new Error('Invalid category ID'),
                req,
                res,
                DOWNLOAD_CATEGORY_ERROR_CODES.INVALID_ID,
                '無效的分類 ID',
                '/admin/downloads/categories'
            );
        }
        
        await prisma.downloadCategory.update({
            where: { id },
            data: { deletedAt: new Date() }
        });

        req.flash('success_msg', '下載分類已成功刪除');
        return res.redirect('/admin/downloads/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_CATEGORY_ERROR_CODES.DELETE_FAILED,
            '刪除下載分類失敗',
            '/admin/downloads/categories'
        );
    }
};
