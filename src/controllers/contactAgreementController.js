const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const logger = require('../config/logger');
const { CONTACT_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

/**
 * Get the contact agreement or create a default one if none exists
 */
async function getOrCreateAgreement() {
  // Try to get the first agreement (there should only be one)
  let agreement = await prisma.contactUsAgreement.findFirst();

  // If no agreement exists, create a default one
  if (!agreement) {
    agreement = await prisma.contactUsAgreement.create({
      data: {
        content_en: '<p>Please configure the English agreement content.</p>',
        content_tw: '<p>請設定中文同意書內容。</p>'
      }
    });
  }

  return agreement;
}

/**
 * Render the agreement editor page
 */
exports.showAgreementEditor = async (req, res) => {
  try {
    const agreement = await getOrCreateAgreement();
    res.render('admin/contact/agreement/index', {
      title: '管理聯絡表單同意書',
      agreement
    });
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      CONTACT_ERROR_CODES.AGREEMENT_ERROR,
      '載入同意書時發生錯誤',
      '/admin/contact'
    );
  }
};

/**
 * Update the agreement content
 */
exports.updateAgreement = async (req, res) => {
  try {
    const { content_en, content_tw } = req.body;

    // Input validation
    if (!content_en || !content_tw) {
      return handleControllerError(
        new Error('Missing required fields'),
        req,
        res,
        CONTACT_ERROR_CODES.AGREEMENT_ERROR,
        '中英文內容皆為必填',
        '/admin/contact/agreement'
      );
    }

    // Get the current agreement
    let agreement = await getOrCreateAgreement();

    // Update the agreement
    agreement = await prisma.contactUsAgreement.update({
      where: { id: agreement.id },
      data: {
        content_en,
        content_tw,
        updatedAt: new Date()
      }
    });

    req.flash('success', '同意書已成功更新');
    res.redirect('/admin/contact/agreement');
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      CONTACT_ERROR_CODES.AGREEMENT_ERROR,
      '更新同意書時發生錯誤',
      '/admin/contact/agreement'
    );
  }
}; 