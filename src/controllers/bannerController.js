const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const { BANNER_ERROR_CODES, COMMON_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError, handleApiError } = require('../utils/errorHandler');
const unlinkAsync = promisify(fs.unlink);

// List all banners
exports.listBanners = async (req, res) => {
    try {
        const banners = await prisma.banner.findMany({
            orderBy: {
                createdAt: 'desc'
            },
            include: {
                createdBy: {
                    select: {
                        username: true
                    }
                }
            }
        });

        res.render('admin/banners/list', {
            title: 'Banners',
            layout: 'layouts/admin',
            banners
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            BANNER_ERROR_CODES.LIST_FAILED,
            '載入橫幅失敗',
            '/admin/dashboard'
        );
    }
};

// Render create banner form
exports.renderCreateBanner = (req, res) => {
    try {
        res.render('admin/banners/create', {
            title: 'Create Banner',
            layout: 'layouts/admin'
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            BANNER_ERROR_CODES.CREATE_FAILED,
            '載入建立橫幅表單失敗',
            '/admin/banners'
        );
    }
};

// Create a new banner
exports.createBanner = async (req, res) => {
    try {
        const { title_en, title_tw, description_en, description_tw, url, isActive, order } = req.body;
        
        if (!req.files || !req.files.media) {
            return handleControllerError(
                new Error('Main media file is required'),
                req,
                res,
                BANNER_ERROR_CODES.UPLOAD_ERROR,
                '請上傳主要媒體檔案',
                '/admin/banners/create'
            );
        }

        // Get the main media file
        const mainMedia = req.files.media[0];
        
        // Determine if it's an image or video based on mimetype
        const mediaType = mainMedia.mimetype.startsWith('image/') ? 'image' : 'video';
        
        // Store the path relative to the public directory for proper URL generation
        const relativePath = '/uploads/banners/' + path.basename(mainMedia.path);
        
        // Get responsive image paths if they exist
        let mediaPathDesktop = null;
        let mediaPathTablet = null;
        let mediaPathMobile = null;
        
        if (req.files.mediaDesktop) {
            mediaPathDesktop = '/uploads/banners/' + path.basename(req.files.mediaDesktop[0].path);
        }
        
        if (req.files.mediaTablet) {
            mediaPathTablet = '/uploads/banners/' + path.basename(req.files.mediaTablet[0].path);
        }
        
        if (req.files.mediaMobile) {
            mediaPathMobile = '/uploads/banners/' + path.basename(req.files.mediaMobile[0].path);
        }
        
        await prisma.banner.create({
            data: {
                title_en,
                title_tw,
                description_en,
                description_tw,
                url,
                mediaPath: relativePath,
                mediaType,
                mediaPathDesktop,
                mediaPathTablet,
                mediaPathMobile,
                order: order ? parseInt(order) : 0,
                isActive: isActive === 'true',
                userId: req.session.user.id
            }
        });

        req.flash('success_msg', '橫幅已成功建立');
        res.redirect('/admin/banners');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            BANNER_ERROR_CODES.CREATE_FAILED,
            '建立橫幅失敗',
            '/admin/banners/create'
        );
    }
};

// Render edit banner form
exports.renderEditBanner = async (req, res) => {
    try {
        const bannerId = parseInt(req.params.id);
        
        const banner = await prisma.banner.findUnique({
            where: { id: bannerId }
        });

        if (!banner) {
            return handleControllerError(
                new Error('Banner not found'),
                req,
                res,
                BANNER_ERROR_CODES.NOT_FOUND,
                '找不到橫幅',
                '/admin/banners'
            );
        }

        res.render('admin/banners/edit', {
            title: 'Edit Banner',
            layout: 'layouts/admin',
            banner
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            BANNER_ERROR_CODES.UPDATE_FAILED,
            '載入橫幅失敗',
            '/admin/banners'
        );
    }
};

// Update a banner
exports.updateBanner = async (req, res) => {
    try {
        const bannerId = parseInt(req.params.id);
        const { title_en, title_tw, description_en, description_tw, url, isActive, order } = req.body;

        const banner = await prisma.banner.findUnique({
            where: { id: bannerId }
        });

        if (!banner) {
            return handleControllerError(
                new Error('Banner not found'),
                req,
                res,
                BANNER_ERROR_CODES.NOT_FOUND,
                '找不到橫幅',
                '/admin/banners'
            );
        }

        const updateData = {
            title_en,
            title_tw,
            description_en,
            description_tw,
            url,
            order: order ? parseInt(order) : 0,
            isActive: isActive === 'true'
        };

        // If a new main file is uploaded, update the media info
        if (req.files && req.files.media) {
            // Delete the old file if it exists
            try {
                // The actual physical path on disk
                const oldFilePath = path.join(process.cwd(), 'public', banner.mediaPath);
                
                logger.info(`Attempting to delete old file at: ${oldFilePath}`);
                
                if (fs.existsSync(oldFilePath)) {
                    await unlinkAsync(oldFilePath);
                    logger.info(`Successfully deleted old file: ${oldFilePath}`);
                } else {
                    logger.warn(`Old file not found for deletion: ${oldFilePath}`);
                }
            } catch (err) {
                logger.error(`Error deleting old banner file: ${banner.mediaPath}`, {
                    error: err,
                    errorCode: COMMON_ERROR_CODES.FILE_SYSTEM_ERROR
                });
            }

            // Update with new file info
            const mainMedia = req.files.media[0];
            const relativePath = '/uploads/banners/' + path.basename(mainMedia.path);
            updateData.mediaPath = relativePath;
            updateData.mediaType = mainMedia.mimetype.startsWith('image/') ? 'image' : 'video';
        }
        
        // Update desktop image if provided
        if (req.files && req.files.mediaDesktop) {
            // Delete the old file if it exists
            if (banner.mediaPathDesktop) {
                try {
                    const oldFilePath = path.join(process.cwd(), 'public', banner.mediaPathDesktop);
                    if (fs.existsSync(oldFilePath)) {
                        await unlinkAsync(oldFilePath);
                    }
                } catch (err) {
                    logger.error(`Error deleting old desktop banner file: ${banner.mediaPathDesktop}`, {
                        error: err,
                        errorCode: COMMON_ERROR_CODES.FILE_SYSTEM_ERROR
                    });
                }
            }
            
            // Update with new file info
            updateData.mediaPathDesktop = '/uploads/banners/' + path.basename(req.files.mediaDesktop[0].path);
        }
        
        // Update tablet image if provided
        if (req.files && req.files.mediaTablet) {
            // Delete the old file if it exists
            if (banner.mediaPathTablet) {
                try {
                    const oldFilePath = path.join(process.cwd(), 'public', banner.mediaPathTablet);
                    if (fs.existsSync(oldFilePath)) {
                        await unlinkAsync(oldFilePath);
                    }
                } catch (err) {
                    logger.error(`Error deleting old tablet banner file: ${banner.mediaPathTablet}`, {
                        error: err,
                        errorCode: COMMON_ERROR_CODES.FILE_SYSTEM_ERROR
                    });
                }
            }
            
            // Update with new file info
            updateData.mediaPathTablet = '/uploads/banners/' + path.basename(req.files.mediaTablet[0].path);
        }
        
        // Update mobile image if provided
        if (req.files && req.files.mediaMobile) {
            // Delete the old file if it exists
            if (banner.mediaPathMobile) {
                try {
                    const oldFilePath = path.join(process.cwd(), 'public', banner.mediaPathMobile);
                    if (fs.existsSync(oldFilePath)) {
                        await unlinkAsync(oldFilePath);
                    }
                } catch (err) {
                    logger.error(`Error deleting old mobile banner file: ${banner.mediaPathMobile}`, {
                        error: err,
                        errorCode: COMMON_ERROR_CODES.FILE_SYSTEM_ERROR
                    });
                }
            }
            
            // Update with new file info
            updateData.mediaPathMobile = '/uploads/banners/' + path.basename(req.files.mediaMobile[0].path);
        }

        await prisma.banner.update({
            where: { id: bannerId },
            data: updateData
        });

        req.flash('success_msg', '橫幅已成功更新');
        res.redirect('/admin/banners');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            BANNER_ERROR_CODES.UPDATE_FAILED,
            '更新橫幅失敗',
            `/admin/banners/edit/${req.params.id}`
        );
    }
};

// Toggle banner active status
exports.toggleBannerStatus = async (req, res) => {
    try {
        const bannerId = parseInt(req.params.id);
        
        const banner = await prisma.banner.findUnique({
            where: { id: bannerId }
        });

        if (!banner) {
            return handleApiError(
                new Error('Banner not found'),
                req,
                res,
                BANNER_ERROR_CODES.NOT_FOUND,
                '找不到橫幅'
            );
        }

        await prisma.banner.update({
            where: { id: bannerId },
            data: {
                isActive: !banner.isActive
            }
        });

        return res.json({ 
            success: true, 
            isActive: !banner.isActive,
            message: `橫幅已成功${!banner.isActive ? '啟用' : '停用'}` 
        });
    } catch (error) {
        return handleApiError(
            error,
            req,
            res,
            BANNER_ERROR_CODES.ACTIVE_ERROR,
            '更新橫幅狀態失敗'
        );
    }
};

// Delete a banner
exports.deleteBanner = async (req, res) => {
    try {
        const bannerId = parseInt(req.params.id);
        
        const banner = await prisma.banner.findUnique({
            where: { id: bannerId }
        });

        if (!banner) {
            return handleControllerError(
                new Error('Banner not found'),
                req,
                res,
                BANNER_ERROR_CODES.NOT_FOUND,
                '找不到橫幅',
                '/admin/banners'
            );
        }

        // Delete the main file
        try {
            // The actual physical path on disk
            const filePath = path.join(process.cwd(), 'public', banner.mediaPath);
            
            logger.info(`Attempting to delete file at: ${filePath}`);
            
            if (fs.existsSync(filePath)) {
                await unlinkAsync(filePath);
                logger.info(`Successfully deleted file: ${filePath}`);
            } else {
                logger.warn(`File not found for deletion: ${filePath}`);
            }
        } catch (err) {
            logger.error(`Error deleting banner file: ${banner.mediaPath}`, {
                error: err,
                errorCode: COMMON_ERROR_CODES.FILE_SYSTEM_ERROR
            });
            // Continue with banner deletion even if file deletion fails
        }
        
        // Delete responsive image files if they exist
        if (banner.mediaPathDesktop) {
            try {
                const filePath = path.join(process.cwd(), 'public', banner.mediaPathDesktop);
                if (fs.existsSync(filePath)) {
                    await unlinkAsync(filePath);
                }
            } catch (err) {
                logger.error(`Error deleting desktop banner file: ${banner.mediaPathDesktop}`, {
                    error: err,
                    errorCode: COMMON_ERROR_CODES.FILE_SYSTEM_ERROR
                });
            }
        }
        
        if (banner.mediaPathTablet) {
            try {
                const filePath = path.join(process.cwd(), 'public', banner.mediaPathTablet);
                if (fs.existsSync(filePath)) {
                    await unlinkAsync(filePath);
                }
            } catch (err) {
                logger.error(`Error deleting tablet banner file: ${banner.mediaPathTablet}`, {
                    error: err,
                    errorCode: COMMON_ERROR_CODES.FILE_SYSTEM_ERROR
                });
            }
        }
        
        if (banner.mediaPathMobile) {
            try {
                const filePath = path.join(process.cwd(), 'public', banner.mediaPathMobile);
                if (fs.existsSync(filePath)) {
                    await unlinkAsync(filePath);
                }
            } catch (err) {
                logger.error(`Error deleting mobile banner file: ${banner.mediaPathMobile}`, {
                    error: err,
                    errorCode: COMMON_ERROR_CODES.FILE_SYSTEM_ERROR
                });
            }
        }

        // Delete the banner record
        await prisma.banner.delete({
            where: { id: bannerId }
        });

        req.flash('success_msg', '橫幅已成功刪除');
        res.redirect('/admin/banners');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            BANNER_ERROR_CODES.DELETE_FAILED,
            '刪除橫幅失敗',
            '/admin/banners'
        );
    }
};