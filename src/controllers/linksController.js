const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const fs = require('fs');
const path = require('path');
const logger = require('../config/logger');
const { COMMON_ERROR_CODES, LINK_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

// Display list of all links
exports.index = async (req, res) => {
    try {
        const links = await prisma.link.findMany({
            orderBy: {
                order: 'asc'
            }
        });

        res.render('admin/links/index', {
            title: 'Links Management',
            links
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            LINK_ERROR_CODES.LIST_FAILED,
            '載入連結失敗',
            '/admin/dashboard'
        );
    }
};

// Display link create form
exports.createForm = async (req, res) => {
    try {
        res.render('admin/links/create', {
            title: 'Create Link'
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            LINK_ERROR_CODES.FORM_LOAD_FAILED,
            '載入建立連結表單失敗',
            '/admin/links'
        );
    }
};

// Handle link create
exports.create = async (req, res) => {
    try {
        const { title_en, title_tw, url, order } = req.body;
        let image = null;

        // Handle image upload
        if (req.file) {
            // Store the path relative to the public directory for proper URL generation
            image = `/uploads/links/${path.basename(req.file.path)}`;
        }

        // Create link
        await prisma.link.create({
            data: {
                title_en,
                title_tw,
                url,
                image,
                order: parseInt(order) || 0,
                active: true
            }
        });

        req.flash('success_msg', '連結已成功建立');
        res.redirect('/admin/links');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            LINK_ERROR_CODES.CREATE_FAILED,
            '建立連結失敗',
            '/admin/links/create'
        );
    }
};

// Display link edit form
exports.editForm = async (req, res) => {
    try {
        const { id } = req.params;
        const link = await prisma.link.findUnique({
            where: { id: parseInt(id) }
        });

        if (!link) {
            return handleControllerError(
                new Error('Link not found'),
                req,
                res,
                LINK_ERROR_CODES.NOT_FOUND,
                '找不到連結',
                '/admin/links'
            );
        }

        res.render('admin/links/edit', {
            title: 'Edit Link',
            link
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            LINK_ERROR_CODES.FORM_LOAD_FAILED,
            '載入連結失敗',
            '/admin/links'
        );
    }
};

// Handle link update
exports.update = async (req, res) => {
    try {
        const { id } = req.params;
        const { title_en, title_tw, url, order, removeImage } = req.body;
        
        const link = await prisma.link.findUnique({
            where: { id: parseInt(id) }
        });

        if (!link) {
            return handleControllerError(
                new Error('Link not found'),
                req,
                res,
                LINK_ERROR_CODES.NOT_FOUND,
                '找不到連結',
                '/admin/links'
            );
        }

        let image = link.image;

        // Handle image removal
        if (removeImage === 'on' && link.image) {
            const imagePath = path.join(__dirname, '../../public', link.image);
            try {
                if (fs.existsSync(imagePath)) {
                    fs.unlinkSync(imagePath);
                }
                image = null;
            } catch (err) {
                logger.warn(`${LINK_ERROR_CODES.IMAGE_PROCESSING_ERROR}: Failed to delete image: ${err.message}`);
                // Continue even if image deletion fails
            }
        }

        // Handle new image upload
        if (req.file) {
            // Remove old image if exists
            if (link.image) {
                const oldImagePath = path.join(__dirname, '../../public', link.image);
                try {
                    if (fs.existsSync(oldImagePath)) {
                        fs.unlinkSync(oldImagePath);
                    }
                } catch (err) {
                    logger.warn(`${LINK_ERROR_CODES.IMAGE_PROCESSING_ERROR}: Failed to delete old image: ${err.message}`);
                    // Continue even if image deletion fails
                }
            }

            // Store the path relative to the public directory for proper URL generation
            image = `/uploads/links/${path.basename(req.file.path)}`;
        }

        // Update link
        await prisma.link.update({
            where: { id: parseInt(id) },
            data: {
                title_en,
                title_tw,
                url,
                image,
                order: parseInt(order) || 0
            }
        });

        req.flash('success_msg', '連結已成功更新');
        res.redirect('/admin/links');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            LINK_ERROR_CODES.UPDATE_FAILED,
            '更新連結失敗',
            `/admin/links/edit/${req.params.id}`
        );
    }
};

// Handle link delete
exports.delete = async (req, res) => {
    try {
        const { id } = req.params;
        
        const link = await prisma.link.findUnique({
            where: { id: parseInt(id) }
        });

        if (!link) {
            return handleControllerError(
                new Error('Link not found'),
                req,
                res,
                LINK_ERROR_CODES.NOT_FOUND,
                '找不到連結',
                '/admin/links'
            );
        }

        // Delete image if exists
        if (link.image) {
            const imagePath = path.join(__dirname, '../../public', link.image);
            try {
                if (fs.existsSync(imagePath)) {
                    fs.unlinkSync(imagePath);
                }
            } catch (err) {
                logger.warn(`${LINK_ERROR_CODES.IMAGE_PROCESSING_ERROR}: Failed to delete image during link deletion: ${err.message}`);
                // Continue even if image deletion fails
            }
        }

        // Delete link
        await prisma.link.delete({
            where: { id: parseInt(id) }
        });

        req.flash('success_msg', '連結已成功刪除');
        res.redirect('/admin/links');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            LINK_ERROR_CODES.DELETE_FAILED,
            '刪除連結失敗',
            '/admin/links'
        );
    }
};

// Toggle link active status
exports.toggleActive = async (req, res) => {
    try {
        const { id } = req.params;
        
        const link = await prisma.link.findUnique({
            where: { id: parseInt(id) }
        });

        if (!link) {
            return handleControllerError(
                new Error('Link not found'),
                req,
                res,
                LINK_ERROR_CODES.NOT_FOUND,
                '找不到連結',
                '/admin/links'
            );
        }

        // Toggle active status
        await prisma.link.update({
            where: { id: parseInt(id) },
            data: {
                active: !link.active
            }
        });

        req.flash('success_msg', `連結已${link.active ? '停用' : '啟用'}`);
        res.redirect('/admin/links');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            LINK_ERROR_CODES.TOGGLE_STATUS_FAILED,
            '更新連結狀態失敗',
            '/admin/links'
        );
    }
}; 