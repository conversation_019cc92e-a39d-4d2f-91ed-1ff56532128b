const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const { CATEGORY_PERMISSION_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError, handleApiError } = require('../utils/errorHandler');

/**
 * Assign category permissions to a user
 */
exports.assignCategoryPermissions = async (req, res) => {
    try {
        const { userId } = req.params;
        const { permissions } = req.body;

        // Validate user exists
        const user = await prisma.user.findUnique({
            where: { id: parseInt(userId) },
            include: { role: true }
        });

        if (!user) {
            return handleControllerError(
                new Error('User not found'),
                req,
                res,
                CATEGORY_PERMISSION_ERROR_CODES.USER_NOT_FOUND,
                '找不到使用者',
                '/admin/users'
            );
        }

        // Only assign category permissions to editors
        if (user.role.name !== 'editor') {
            return handleControllerError(
                new Error('Category permissions can only be assigned to editors'),
                req,
                res,
                CATEGORY_PERMISSION_ERROR_CODES.INVALID_ROLE,
                '分類權限只能指派給編輯者',
                `/admin/users/edit/${userId}`
            );
        }

        // Validate permissions format
        if (!Array.isArray(permissions)) {
            return handleControllerError(
                new Error('Invalid permissions format'),
                req,
                res,
                CATEGORY_PERMISSION_ERROR_CODES.INVALID_FORMAT,
                '權限格式無效',
                `/admin/users/edit/${userId}`
            );
        }

        // Begin transaction
        try {
            await prisma.$transaction(async (tx) => {
                // Delete existing permissions
                await tx.categoryPermission.deleteMany({
                    where: { userId: parseInt(userId) }
                });

                // Create new permissions
                for (const perm of permissions) {
                    await tx.categoryPermission.create({
                        data: {
                            userId: parseInt(userId),
                            categoryId: parseInt(perm.categoryId),
                            canView: perm.canView || false,
                            canCreate: perm.canCreate || false,
                            canEdit: perm.canEdit || false,
                            canDelete: perm.canDelete || false
                        }
                    });
                }
            });
        } catch (txError) {
            return handleControllerError(
                txError,
                req,
                res,
                CATEGORY_PERMISSION_ERROR_CODES.TRANSACTION_FAILED,
                '處理權限更新時發生錯誤',
                `/admin/users/edit/${userId}`
            );
        }

        req.flash('success_msg', '分類權限已成功更新');
        res.redirect(`/admin/users/edit/${userId}`);
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CATEGORY_PERMISSION_ERROR_CODES.ASSIGN_FAILED,
            '更新分類權限失敗',
            `/admin/users/edit/${userId}`
        );
    }
};

/**
 * Get category permissions for a user
 */
exports.getCategoryPermissions = async (req, res) => {
    try {
        const { userId } = req.params;

        const permissions = await prisma.categoryPermission.findMany({
            where: { userId: parseInt(userId) },
            include: { category: true }
        });

        res.json(permissions);
    } catch (error) {
        handleApiError(
            error,
            req,
            res,
            CATEGORY_PERMISSION_ERROR_CODES.FETCH_FAILED,
            '取得分類權限失敗'
        );
    }
};

/**
 * Check if user has permission for a specific category action
 */
exports.checkCategoryPermission = async (userId, categoryId, action) => {
    try {
        const permission = await prisma.categoryPermission.findUnique({
            where: {
                userId_categoryId: {
                    userId: parseInt(userId),
                    categoryId: parseInt(categoryId)
                }
            }
        });

        if (!permission) {
            return false;
        }

        switch (action) {
            case 'view':
                return permission.canView;
            case 'create':
                return permission.canCreate;
            case 'edit':
                return permission.canEdit;
            case 'delete':
                return permission.canDelete;
            default:
                return false;
        }
    } catch (error) {
        logger.error('Error checking category permission:', {
            errorCode: CATEGORY_PERMISSION_ERROR_CODES.CHECK_FAILED,
            userId,
            categoryId,
            action,
            error
        });
        return false;
    }
};
