const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const { CATEGORY_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

exports.listCategories = async (req, res) => {
    try {
        const categories = await prisma.category.findMany({
            where: {
                parentId: null // Get only top-level categories
            },
            include: {
                children: {
                    include: {
                        _count: {
                            select: {
                                articles: true
                            }
                        }
                    }
                },
                _count: {
                    select: {
                        articles: true
                    }
                }
            },
            orderBy: {
                order: 'asc'
            }
        });

        res.render('admin/categories/list', {
            title: 'Categories',
            layout: 'layouts/admin',
            categories
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CATEGORY_ERROR_CODES.LIST_FAILED,
            '載入分類失敗',
            '/admin/dashboard'
        );
    }
};

exports.renderCreateCategory = async (req, res) => {
    try {
        // Get all categories for parent selection
        const categories = await prisma.category.findMany({
            orderBy: {
                name_en: 'asc'
            }
        });

        res.render('admin/categories/create', {
            title: 'Create Category',
            layout: 'layouts/admin',
            categories
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CATEGORY_ERROR_CODES.LIST_FAILED,
            '載入分類表單失敗',
            '/admin/categories'
        );
    }
};

exports.createCategory = async (req, res) => {
    try {
        const { 
            name_en, 
            name_tw, 
            description_en, 
            description_tw, 
            parentId, 
            order,
            type
        } = req.body;

        await prisma.category.create({
            data: {
                name_en,
                name_tw,
                description_en,
                description_tw,
                parentId: parentId ? parseInt(parentId) : null,
                order: order ? parseInt(order) : 0,
                type: type || 'article'
            }
        });

        req.flash('success_msg', '分類已成功建立');
        res.redirect('/admin/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CATEGORY_ERROR_CODES.CREATE_FAILED,
            '建立分類失敗',
            '/admin/categories/create'
        );
    }
};

exports.renderEditCategory = async (req, res) => {
    try {
        const categoryId = parseInt(req.params.id);

        const [category, categories] = await Promise.all([
            prisma.category.findUnique({
                where: { id: categoryId },
                include: {
                    parent: true,
                    children: true
                }
            }),
            prisma.category.findMany({
                where: {
                    NOT: {
                        id: categoryId // Exclude current category from parent options
                    }
                },
                orderBy: {
                    name_en: 'asc'
                }
            })
        ]);

        if (!category) {
            return handleControllerError(
                new Error('Category not found'),
                req,
                res,
                CATEGORY_ERROR_CODES.NOT_FOUND,
                '找不到分類',
                '/admin/categories'
            );
        }

        res.render('admin/categories/edit', {
            title: 'Edit Category',
            layout: 'layouts/admin',
            category,
            categories
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CATEGORY_ERROR_CODES.LIST_FAILED,
            '載入分類失敗',
            '/admin/categories'
        );
    }
};

exports.updateCategory = async (req, res) => {
    try {
        const categoryId = parseInt(req.params.id);
        const { 
            name_en, 
            name_tw, 
            description_en, 
            description_tw, 
            parentId, 
            order,
            type
        } = req.body;

        // Check if trying to set as parent of itself
        if (parentId && parseInt(parentId) === categoryId) {
            return handleControllerError(
                new Error('Category cannot be its own parent'),
                req,
                res,
                CATEGORY_ERROR_CODES.INVALID_PARENT,
                '分類不能設定為自己的父分類',
                `/admin/categories/edit/${categoryId}`
            );
        }

        // Check if trying to set as parent one of its descendants
        if (parentId) {
            const descendants = await getDescendants(categoryId);
            if (descendants.includes(parseInt(parentId))) {
                return handleControllerError(
                    new Error('Cannot set a descendant as parent'),
                    req,
                    res,
                    CATEGORY_ERROR_CODES.INVALID_PARENT,
                    '不能將子分類設定為父分類',
                    `/admin/categories/edit/${categoryId}`
                );
            }
        }

        await prisma.category.update({
            where: { id: categoryId },
            data: {
                name_en,
                name_tw,
                description_en,
                description_tw,
                parentId: parentId ? parseInt(parentId) : null,
                order: order ? parseInt(order) : 0,
                type: type || 'article'
            }
        });

        req.flash('success_msg', '分類已成功更新');
        res.redirect('/admin/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CATEGORY_ERROR_CODES.UPDATE_FAILED,
            '更新分類失敗',
            `/admin/categories/edit/${req.params.id}`
        );
    }
};

exports.deleteCategory = async (req, res) => {
    const categoryId = parseInt(req.params.id);

    try {
        const category = await prisma.category.findUnique({
            where: { id: categoryId },
            include: {
                articles: true,
                children: true
            }
        });

        if (!category) {
            return handleControllerError(
                new Error('Category not found'),
                req,
                res,
                CATEGORY_ERROR_CODES.NOT_FOUND,
                '找不到分類',
                '/admin/categories'
            );
        }

        if (category.articles.length > 0) {
            return handleControllerError(
                new Error('Cannot delete category with articles'),
                req,
                res,
                CATEGORY_ERROR_CODES.DELETE_FAILED,
                '無法刪除包含文章的分類。請先移動或刪除文章。',
                '/admin/categories'
            );
        }

        if (category.children.length > 0) {
            return handleControllerError(
                new Error('Cannot delete category with subcategories'),
                req,
                res,
                CATEGORY_ERROR_CODES.DELETE_FAILED,
                '無法刪除包含子分類的分類。請先移動或刪除子分類。',
                '/admin/categories'
            );
        }

        await prisma.category.delete({
            where: { id: categoryId }
        });

        req.flash('success_msg', '分類已成功刪除');
        res.redirect('/admin/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CATEGORY_ERROR_CODES.DELETE_FAILED,
            '刪除分類失敗',
            '/admin/categories'
        );
    }
};

// Helper function to get all descendant category IDs
async function getDescendants(categoryId) {
    const descendants = [];
    
    async function collect(parentId) {
        const children = await prisma.category.findMany({
            where: { parentId },
            select: { id: true }
        });
        
        for (const child of children) {
            descendants.push(child.id);
            await collect(child.id);
        }
    }
    
    await collect(categoryId);
    return descendants;
}
