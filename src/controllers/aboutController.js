const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const unlinkAsync = promisify(fs.unlink);
const multer = require('multer');
const { ABOUT_ERROR_CODES, COMMON_ERROR_CODES, FRONTEND_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError, handleFrontendError } = require('../utils/errorHandler');

// Configure multer for image uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadPath = path.join(__dirname, '../../public/uploads/about');
        // Create directory if it doesn't exist
        if (!fs.existsSync(uploadPath)) {
            fs.mkdirSync(uploadPath, { recursive: true });
        }
        cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        let suffix = '';

        // Add appropriate suffix based on field name
        if (file.fieldname === 'imageDesktop') {
            suffix = '-desktop';
        } else if (file.fieldname === 'imageTablet') {
            suffix = '-tablet';
        } else if (file.fieldname === 'imageMobile') {
            suffix = '-mobile';
        } else if (file.fieldname === 'imageDesktopEn') {
            suffix = '-desktop-en';
        } else if (file.fieldname === 'imageTabletEn') {
            suffix = '-tablet-en';
        } else if (file.fieldname === 'imageMobileEn') {
            suffix = '-mobile-en';
        }

        cb(null, 'about-' + uniqueSuffix + suffix + path.extname(file.originalname));
    }
});

exports.upload = multer({
    storage: storage,
    limits: {
        fileSize: parseInt(process.env.MAX_IMAGE_SIZE) || 5 * 1024 * 1024, // Use MAX_IMAGE_SIZE from env or default to 5MB
        fieldSize: parseInt(process.env.MAX_UPLOAD_SIZE) || 100 * 1024 * 1024 // Use MAX_UPLOAD_SIZE from env or default to 100MB
    },
    fileFilter: function (req, file, cb) {
        // Accept images only - use allowed image types from env if available
        const allowedTypes = process.env.ALLOWED_IMAGE_TYPES ?
            process.env.ALLOWED_IMAGE_TYPES.split(',').join('|') :
            'jpg|jpeg|png|gif';

        const regex = new RegExp(`\\.(${allowedTypes})$`, 'i');

        if (!file.originalname.match(regex)) {
            return cb(new Error(`Only image files are allowed! Supported types: ${allowedTypes.toUpperCase()}`), false);
        }
        cb(null, true);
    }
}).fields([
    { name: 'imageDesktop', maxCount: 1 },
    { name: 'imageTablet', maxCount: 1 },
    { name: 'imageMobile', maxCount: 1 },
    { name: 'imageDesktopEn', maxCount: 1 },
    { name: 'imageTabletEn', maxCount: 1 },
    { name: 'imageMobileEn', maxCount: 1 }
]);

// Admin: List all about items
exports.listItems = async (req, res) => {
    try {
        const items = await prisma.aboutItem.findMany({
            where: {
                deletedAt: null
            },
            orderBy: {
                order: 'asc'
            },
            include: {
                author: {
                    select: {
                        username: true
                    }
                }
            }
        });

        res.render('admin/about/index', {
            title: 'About Page Items',
            items
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            ABOUT_ERROR_CODES.LIST_FAILED,
            '載入關於頁面項目失敗',
            '/admin/dashboard'
        );
    }
};

// Admin: Render create about item form
exports.renderCreateItem = (req, res) => {
    try {
        res.render('admin/about/create', {
            title: 'Create About Item'
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            ABOUT_ERROR_CODES.CREATE_FORM_FAILED,
            '載入建立表單失敗',
            '/admin/about'
        );
    }
};

// Admin: Create a new about item
exports.createItem = async (req, res) => {
    try {
        const { title_en, title_tw, type, content_en, content_tw, order, alt_en, alt_tw } = req.body;

        // Process content based on type
        let processedContentEn = content_en;
        let processedContentTw = content_tw;

        // Check content length and truncate if necessary
        // Use environment variable MAX_UPLOAD_SIZE or default to 100MB
        const MAX_CONTENT_LENGTH = parseInt(process.env.MAX_UPLOAD_SIZE) || 100 * 1024 * 1024;

        if (processedContentEn && processedContentEn.length > MAX_CONTENT_LENGTH) {
            logger.warn(`Content_en for "${title_en}" was truncated from ${processedContentEn.length} to ${MAX_CONTENT_LENGTH} bytes`);
            processedContentEn = processedContentEn.substring(0, MAX_CONTENT_LENGTH);
        }

        if (processedContentTw && processedContentTw.length > MAX_CONTENT_LENGTH) {
            logger.warn(`Content_tw for "${title_tw}" was truncated from ${processedContentTw.length} to ${MAX_CONTENT_LENGTH} bytes`);
            processedContentTw = processedContentTw.substring(0, MAX_CONTENT_LENGTH);
        }

        // Handle image uploads
        let imagePath = null;
        let imagePathDesktop = null;
        let imagePathTablet = null;
        let imagePathMobile = null;
        let imagePathDesktopEn = null;
        let imagePathTabletEn = null;
        let imagePathMobileEn = null;

        if (req.files && type === 'image') {
            // Handle Chinese desktop image
            if (req.files.imageDesktop && req.files.imageDesktop.length > 0) {
                imagePathDesktop = `/uploads/about/${req.files.imageDesktop[0].filename}`;
                // Set the main image path to desktop image if available
                imagePath = imagePathDesktop;
            }

            // Handle Chinese tablet image
            if (req.files.imageTablet && req.files.imageTablet.length > 0) {
                imagePathTablet = `/uploads/about/${req.files.imageTablet[0].filename}`;
                // If no desktop image, use tablet as main image
                if (!imagePath) imagePath = imagePathTablet;
            }

            // Handle Chinese mobile image
            if (req.files.imageMobile && req.files.imageMobile.length > 0) {
                imagePathMobile = `/uploads/about/${req.files.imageMobile[0].filename}`;
                // If no desktop or tablet image, use mobile as main image
                if (!imagePath) imagePath = imagePathMobile;
            }

            // Handle English desktop image
            if (req.files.imageDesktopEn && req.files.imageDesktopEn.length > 0) {
                imagePathDesktopEn = `/uploads/about/${req.files.imageDesktopEn[0].filename}`;
                // If no Chinese images at all, use English desktop as main image
                if (!imagePath) imagePath = imagePathDesktopEn;
            }

            // Handle English tablet image
            if (req.files.imageTabletEn && req.files.imageTabletEn.length > 0) {
                imagePathTabletEn = `/uploads/about/${req.files.imageTabletEn[0].filename}`;
                // If no main image set yet, use English tablet
                if (!imagePath) imagePath = imagePathTabletEn;
            }

            // Handle English mobile image
            if (req.files.imageMobileEn && req.files.imageMobileEn.length > 0) {
                imagePathMobileEn = `/uploads/about/${req.files.imageMobileEn[0].filename}`;
                // If no main image set yet, use English mobile
                if (!imagePath) imagePath = imagePathMobileEn;
            }
        }

        // Create the about item
        await prisma.aboutItem.create({
            data: {
                title_en,
                title_tw,
                type,
                content_en: processedContentEn,
                content_tw: processedContentTw,
                imagePath,
                imagePathDesktop,
                imagePathTablet,
                imagePathMobile,
                imagePathDesktopEn,
                imagePathTabletEn,
                imagePathMobileEn,
                alt_en,
                alt_tw,
                order: order ? parseInt(order) : 0,
                authorId: req.session.user.id
            }
        });

        req.flash('success_msg', '項目已成功建立');
        res.redirect('/admin/about');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            ABOUT_ERROR_CODES.CREATE_FAILED,
            '建立項目失敗',
            '/admin/about/create'
        );
    }
};

// Admin: Render edit about item form
exports.renderEditItem = async (req, res) => {
    try {
        const { id } = req.params;

        const item = await prisma.aboutItem.findUnique({
            where: { id: parseInt(id) }
        });

        if (!item) {
            return handleControllerError(
                new Error(`About item with ID ${id} not found`),
                req,
                res,
                ABOUT_ERROR_CODES.NOT_FOUND,
                '找不到項目',
                '/admin/about'
            );
        }

        // Process content for Quill.js
        let processedItem = { ...item };

        // For bullet points, ensure content is valid JSON
        if (item.type === 'bullet_points') {
            try {
                // If content is already a string, make sure it's valid JSON
                if (typeof item.content_en === 'string') {
                    // Try to parse and re-stringify to ensure valid JSON
                    const parsedEn = JSON.parse(item.content_en);
                    processedItem.content_en = JSON.stringify(parsedEn);
                } else {
                    // If it's an object, stringify it
                    processedItem.content_en = JSON.stringify(item.content_en);
                }

                if (typeof item.content_tw === 'string') {
                    const parsedTw = JSON.parse(item.content_tw);
                    processedItem.content_tw = JSON.stringify(parsedTw);
                } else {
                    processedItem.content_tw = JSON.stringify(item.content_tw);
                }
            } catch (e) {
                logger.error('Error processing bullet points for edit:', {
                    error: e,
                    errorCode: ABOUT_ERROR_CODES.CONTENT_PROCESSING_ERROR,
                    itemId: id
                });
                // Provide empty valid JSON if parsing fails
                processedItem.content_en = JSON.stringify({ ops: [] });
                processedItem.content_tw = JSON.stringify({ ops: [] });
            }
        }

        res.render('admin/about/edit', {
            title: 'Edit About Item',
            item: processedItem
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            ABOUT_ERROR_CODES.EDIT_FORM_FAILED,
            '載入編輯表單失敗',
            '/admin/about'
        );
    }
};

// Admin: Update an about item
exports.updateItem = async (req, res) => {
    try {
        const { id } = req.params;
        const { title_en, title_tw, type, content_en, content_tw, order, alt_en, alt_tw } = req.body;

        // Get the existing item
        const existingItem = await prisma.aboutItem.findUnique({
            where: { id: parseInt(id) }
        });

        if (!existingItem) {
            return handleControllerError(
                new Error(`About item with ID ${id} not found`),
                req,
                res,
                ABOUT_ERROR_CODES.NOT_FOUND,
                '找不到項目',
                '/admin/about'
            );
        }

        // Process content based on type
        let processedContentEn = content_en;
        let processedContentTw = content_tw;

        // Check content length and truncate if necessary
        // Use environment variable MAX_UPLOAD_SIZE or default to 100MB
        const MAX_CONTENT_LENGTH = parseInt(process.env.MAX_UPLOAD_SIZE) || 100 * 1024 * 1024;

        if (processedContentEn && processedContentEn.length > MAX_CONTENT_LENGTH) {
            logger.warn(`Content_en for "${title_en}" was truncated from ${processedContentEn.length} to ${MAX_CONTENT_LENGTH} bytes`);
            processedContentEn = processedContentEn.substring(0, MAX_CONTENT_LENGTH);
        }

        if (processedContentTw && processedContentTw.length > MAX_CONTENT_LENGTH) {
            logger.warn(`Content_tw for "${title_tw}" was truncated from ${processedContentTw.length} to ${MAX_CONTENT_LENGTH} bytes`);
            processedContentTw = processedContentTw.substring(0, MAX_CONTENT_LENGTH);
        }

        // Handle image uploads or removal
        let imagePath = existingItem.imagePath;
        let imagePathDesktop = existingItem.imagePathDesktop;
        let imagePathTablet = existingItem.imagePathTablet;
        let imagePathMobile = existingItem.imagePathMobile;
        let imagePathDesktopEn = existingItem.imagePathDesktopEn;
        let imagePathTabletEn = existingItem.imagePathTabletEn;
        let imagePathMobileEn = existingItem.imagePathMobileEn;

        // Remove existing images if type changed from image
        if (type !== 'image') {
            // Helper function to delete file if it exists
            const deleteFileIfExists = async (filePath) => {
                if (filePath) {
                    const fullPath = path.join(__dirname, '../../public', filePath);
                    try {
                        await unlinkAsync(fullPath);
                    } catch (err) {
                        logger.error(`Failed to delete image file: ${err.message}`, {
                            error: err,
                            errorCode: ABOUT_ERROR_CODES.FILE_DELETION_FAILED,
                            filePath: fullPath
                        });
                    }
                }
            };

            // Delete all image files
            await deleteFileIfExists(existingItem.imagePath);
            await deleteFileIfExists(existingItem.imagePathDesktop);
            await deleteFileIfExists(existingItem.imagePathTablet);
            await deleteFileIfExists(existingItem.imagePathMobile);
            await deleteFileIfExists(existingItem.imagePathDesktopEn);
            await deleteFileIfExists(existingItem.imagePathTabletEn);
            await deleteFileIfExists(existingItem.imagePathMobileEn);

            // Reset all image paths
            imagePath = null;
            imagePathDesktop = null;
            imagePathTablet = null;
            imagePathMobile = null;
            imagePathDesktopEn = null;
            imagePathTabletEn = null;
            imagePathMobileEn = null;
        }

        // Add new images if uploaded
        if (req.files && type === 'image') {
            // Helper function to delete file if it exists
            const deleteFileIfExists = async (filePath) => {
                if (filePath) {
                    const fullPath = path.join(__dirname, '../../public', filePath);
                    try {
                        await unlinkAsync(fullPath);
                    } catch (err) {
                        logger.error(`Failed to delete image file: ${err.message}`, {
                            error: err,
                            errorCode: ABOUT_ERROR_CODES.FILE_DELETION_FAILED,
                            filePath: fullPath
                        });
                    }
                }
            };

            // Handle Chinese desktop image
            if (req.files.imageDesktop && req.files.imageDesktop.length > 0) {
                // Delete old desktop image if exists
                await deleteFileIfExists(existingItem.imagePathDesktop);

                // Set new desktop image path
                imagePathDesktop = `/uploads/about/${req.files.imageDesktop[0].filename}`;

                // Update main image path if it was previously desktop or not set
                if (!imagePath || imagePath === existingItem.imagePathDesktop) {
                    imagePath = imagePathDesktop;
                }
            }

            // Handle Chinese tablet image
            if (req.files.imageTablet && req.files.imageTablet.length > 0) {
                // Delete old tablet image if exists
                await deleteFileIfExists(existingItem.imagePathTablet);

                // Set new tablet image path
                imagePathTablet = `/uploads/about/${req.files.imageTablet[0].filename}`;

                // Update main image path if it was previously tablet or not set
                if (!imagePath || imagePath === existingItem.imagePathTablet) {
                    imagePath = imagePathTablet;
                }
            }

            // Handle Chinese mobile image
            if (req.files.imageMobile && req.files.imageMobile.length > 0) {
                // Delete old mobile image if exists
                await deleteFileIfExists(existingItem.imagePathMobile);

                // Set new mobile image path
                imagePathMobile = `/uploads/about/${req.files.imageMobile[0].filename}`;

                // Update main image path if it was previously mobile or not set
                if (!imagePath || imagePath === existingItem.imagePathMobile) {
                    imagePath = imagePathMobile;
                }
            }

            // Handle English desktop image
            if (req.files.imageDesktopEn && req.files.imageDesktopEn.length > 0) {
                // Delete old English desktop image if exists
                await deleteFileIfExists(existingItem.imagePathDesktopEn);

                // Set new English desktop image path
                imagePathDesktopEn = `/uploads/about/${req.files.imageDesktopEn[0].filename}`;

                // Update main image path if it was previously English desktop or not set
                if (!imagePath || imagePath === existingItem.imagePathDesktopEn) {
                    imagePath = imagePathDesktopEn;
                }
            }

            // Handle English tablet image
            if (req.files.imageTabletEn && req.files.imageTabletEn.length > 0) {
                // Delete old English tablet image if exists
                await deleteFileIfExists(existingItem.imagePathTabletEn);

                // Set new English tablet image path
                imagePathTabletEn = `/uploads/about/${req.files.imageTabletEn[0].filename}`;

                // Update main image path if it was previously English tablet or not set
                if (!imagePath || imagePath === existingItem.imagePathTabletEn) {
                    imagePath = imagePathTabletEn;
                }
            }

            // Handle English mobile image
            if (req.files.imageMobileEn && req.files.imageMobileEn.length > 0) {
                // Delete old English mobile image if exists
                await deleteFileIfExists(existingItem.imagePathMobileEn);

                // Set new English mobile image path
                imagePathMobileEn = `/uploads/about/${req.files.imageMobileEn[0].filename}`;

                // Update main image path if it was previously English mobile or not set
                if (!imagePath || imagePath === existingItem.imagePathMobileEn) {
                    imagePath = imagePathMobileEn;
                }
            }
        }

        // Update the about item
        await prisma.aboutItem.update({
            where: { id: parseInt(id) },
            data: {
                title_en,
                title_tw,
                type,
                content_en: processedContentEn,
                content_tw: processedContentTw,
                imagePath,
                imagePathDesktop,
                imagePathTablet,
                imagePathMobile,
                imagePathDesktopEn,
                imagePathTabletEn,
                imagePathMobileEn,
                alt_en,
                alt_tw,
                order: order ? parseInt(order) : 0,
                updatedAt: new Date()
            }
        });

        req.flash('success_msg', '項目已成功更新');
        res.redirect('/admin/about');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            ABOUT_ERROR_CODES.UPDATE_FAILED,
            '更新項目失敗',
            `/admin/about/edit/${req.params.id}`
        );
    }
};

// Admin: Delete an about item
exports.deleteItem = async (req, res) => {
    try {
        const { id } = req.params;

        // Get the item to check if it has images
        const item = await prisma.aboutItem.findUnique({
            where: { id: parseInt(id) }
        });

        if (!item) {
            return handleControllerError(
                new Error(`About item with ID ${id} not found`),
                req,
                res,
                ABOUT_ERROR_CODES.NOT_FOUND,
                '找不到項目',
                '/admin/about'
            );
        }

        // Soft delete the item
        await prisma.aboutItem.update({
            where: { id: parseInt(id) },
            data: {
                deletedAt: new Date()
            }
        });

        // Helper function to delete file if it exists
        const deleteFileIfExists = async (filePath) => {
            if (filePath) {
                const fullPath = path.join(__dirname, '../../public', filePath);
                try {
                    await unlinkAsync(fullPath);
                } catch (err) {
                    logger.error(`Failed to delete image file: ${err.message}`, {
                        error: err,
                        errorCode: ABOUT_ERROR_CODES.FILE_DELETION_FAILED,
                        filePath: fullPath
                    });
                }
            }
        };

        // Delete all image files
        await deleteFileIfExists(item.imagePath);
        await deleteFileIfExists(item.imagePathDesktop);
        await deleteFileIfExists(item.imagePathTablet);
        await deleteFileIfExists(item.imagePathMobile);
        await deleteFileIfExists(item.imagePathDesktopEn);
        await deleteFileIfExists(item.imagePathTabletEn);
        await deleteFileIfExists(item.imagePathMobileEn);

        req.flash('success_msg', '項目已成功刪除');
        res.redirect('/admin/about');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            ABOUT_ERROR_CODES.DELETE_FAILED,
            '刪除項目失敗',
            '/admin/about'
        );
    }
};

// Frontend: Display about page
exports.showAboutPage = async (req, res) => {
    try {
        const language = req.params.language;

        // Get all about items
        const items = await prisma.aboutItem.findMany({
            where: {
                deletedAt: null
            },
            orderBy: {
                order: 'asc'
            }
        });

        // Process items for display
        const processedItems = items.map(item => {
            let processedItem = { ...item };

            // Process bullet points
            if (item.type === 'bullet_points') {
                try {
                    // Ensure content is parsed as JSON if it's a string
                    if (typeof item.content_en === 'string') {
                        processedItem.content_en = JSON.parse(item.content_en);
                    }
                    if (typeof item.content_tw === 'string') {
                        processedItem.content_tw = JSON.parse(item.content_tw);
                    }
                } catch (e) {
                    logger.error('Error parsing bullet points:', {
                        error: e,
                        errorCode: ABOUT_ERROR_CODES.CONTENT_PROCESSING_ERROR,
                        itemId: item.id
                    });
                    processedItem.content_en = { ops: [] };
                    processedItem.content_tw = { ops: [] };
                }
            } else if (item.type === 'plain_text') {
                // For plain text, ensure the HTML content is properly sanitized but preserves formatting
                // No need to modify, as it will be rendered with <%- %> in the template
                // But we need to make sure it's a string
                if (typeof processedItem.content_en !== 'string') {
                    processedItem.content_en = String(processedItem.content_en || '');
                }
                if (typeof processedItem.content_tw !== 'string') {
                    processedItem.content_tw = String(processedItem.content_tw || '');
                }

                // Check if the content might be Quill Delta JSON that was mistakenly saved as plain text
                try {
                    // If content starts with { and contains "ops", it might be a Quill Delta object
                    if (processedItem.content_en.trim().startsWith('{') && processedItem.content_en.includes('"ops"')) {
                        const deltaObj = JSON.parse(processedItem.content_en);
                        if (deltaObj && deltaObj.ops) {
                            // It's a Delta object, convert it to HTML
                            const tempDiv = document.createElement('div');
                            const quill = new Quill(tempDiv);
                            quill.setContents(deltaObj);
                            processedItem.content_en = tempDiv.querySelector('.ql-editor').innerHTML;
                        }
                    }

                    if (processedItem.content_tw.trim().startsWith('{') && processedItem.content_tw.includes('"ops"')) {
                        const deltaObj = JSON.parse(processedItem.content_tw);
                        if (deltaObj && deltaObj.ops) {
                            // It's a Delta object, convert it to HTML
                            const tempDiv = document.createElement('div');
                            const quill = new Quill(tempDiv);
                            quill.setContents(deltaObj);
                            processedItem.content_tw = tempDiv.querySelector('.ql-editor').innerHTML;
                        }
                    }
                } catch (e) {
                    // If parsing fails, it's likely already HTML content, so we can ignore this error
                    logger.debug('Content appears to be HTML already, not Delta JSON:', e.message);
                }

                // Check if the content might be Quill Delta JSON that was mistakenly saved as plain text
                try {
                    // If content starts with { and contains "ops", it might be a Quill Delta object
                    if (processedItem.content_en.trim().startsWith('{') && processedItem.content_en.includes('"ops"')) {
                        const deltaObj = JSON.parse(processedItem.content_en);
                        if (deltaObj && deltaObj.ops) {
                            // It's a Delta object, we'll pass it as is and let the frontend handle it
                            // We'll add a flag to indicate it's a Delta object
                            processedItem._content_en_is_delta = true;
                        }
                    }

                    if (processedItem.content_tw.trim().startsWith('{') && processedItem.content_tw.includes('"ops"')) {
                        const deltaObj = JSON.parse(processedItem.content_tw);
                        if (deltaObj && deltaObj.ops) {
                            // It's a Delta object, we'll pass it as is and let the frontend handle it
                            // We'll add a flag to indicate it's a Delta object
                            processedItem._content_tw_is_delta = true;
                        }
                    }
                } catch (e) {
                    // If parsing fails, it's likely already HTML content, so we can ignore this error
                    logger.debug('Content appears to be HTML already, not Delta JSON:', e.message);
                }
            }

            return processedItem;
        });

        res.render('frontend/about', {
            title: language === 'en' ? 'About Us' : '關於我們',
            items: processedItems,
            layout: 'layouts/frontend',
            currentLanguage: language || 'en'
        });
    } catch (error) {
        handleFrontendError(
            error,
            req,
            res,
            ABOUT_ERROR_CODES.FRONTEND_RENDER_FAILED,
            language === 'en' ? 'Failed to load about page' : '載入關於頁面失敗',
            'frontend/error'
        );
    }
}; 