const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const { DOWNLOAD_GROUP_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

// List all download groups
exports.listGroups = async (req, res) => {
  try {
    const groups = await prisma.downloadGroup.findMany({
      where: { deletedAt: null },
      orderBy: [
        { categoryId: 'asc' },
        { order: 'asc' }
      ],
      include: {
        category: true,
        _count: {
          select: { downloads: true }
        }
      }
    });

    res.render('admin/downloads/groups/list', {
      title: 'Download Groups',
      layout: 'layouts/admin',
      groups,
      success_msg: req.flash('success_msg'),
      error_msg: req.flash('error_msg')
    });
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      DOWNLOAD_GROUP_ERROR_CODES?.LIST_FAILED || 'DG001',
      '載入下載分組失敗',
      '/admin/downloads'
    );
  }
};

// Render create group form
exports.renderCreateGroup = async (req, res) => {
  try {
    const categories = await prisma.downloadCategory.findMany({
      where: { deletedAt: null },
      orderBy: { order: 'asc' }
    });

    res.render('admin/downloads/groups/create', {
      title: 'Create Download Group',
      layout: 'layouts/admin',
      categories
    });
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      DOWNLOAD_GROUP_ERROR_CODES?.CREATE_FORM_FAILED || 'DG002',
      '載入建立分組表單失敗',
      '/admin/downloads/groups'
    );
  }
};

// Create a new group
exports.createGroup = async (req, res) => {
  try {
    const { name_en, name_tw, description_en, description_tw, order, categoryId } = req.body;

    await prisma.downloadGroup.create({
      data: {
        name_en,
        name_tw,
        description_en,
        description_tw,
        order: order ? parseInt(order) : 0,
        categoryId: parseInt(categoryId)
      }
    });

    req.flash('success_msg', 'Download group created successfully');
    return res.redirect('/admin/downloads/groups');
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      DOWNLOAD_GROUP_ERROR_CODES?.CREATE_FAILED || 'DG003',
      '建立下載分組失敗',
      '/admin/downloads/groups/create'
    );
  }
};

// Render edit group form
exports.renderEditGroup = async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return handleControllerError(
        new Error('Invalid group ID'),
        req,
        res,
        DOWNLOAD_GROUP_ERROR_CODES?.INVALID_ID || 'DG004',
        '無效的分組 ID',
        '/admin/downloads/groups'
      );
    }

    const [group, categories] = await Promise.all([
      prisma.downloadGroup.findUnique({
        where: { id },
        include: { category: true }
      }),
      prisma.downloadCategory.findMany({
        where: { deletedAt: null },
        orderBy: { order: 'asc' }
      })
    ]);

    if (!group) {
      return handleControllerError(
        new Error('Group not found'),
        req,
        res,
        DOWNLOAD_GROUP_ERROR_CODES?.NOT_FOUND || 'DG005',
        '找不到分組',
        '/admin/downloads/groups'
      );
    }

    res.render('admin/downloads/groups/edit', {
      title: 'Edit Download Group',
      layout: 'layouts/admin',
      group,
      categories
    });
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      DOWNLOAD_GROUP_ERROR_CODES?.EDIT_FORM_FAILED || 'DG006',
      '載入下載分組失敗',
      '/admin/downloads/groups'
    );
  }
};

// Update a group
exports.updateGroup = async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return handleControllerError(
        new Error('Invalid group ID'),
        req,
        res,
        DOWNLOAD_GROUP_ERROR_CODES?.INVALID_ID || 'DG007',
        '無效的分組 ID',
        '/admin/downloads/groups'
      );
    }

    const { name_en, name_tw, description_en, description_tw, order, categoryId } = req.body;

    await prisma.downloadGroup.update({
      where: { id },
      data: {
        name_en,
        name_tw,
        description_en,
        description_tw,
        order: order ? parseInt(order) : 0,
        categoryId: parseInt(categoryId),
        updatedAt: new Date()
      }
    });

    req.flash('success_msg', '下載分組已成功更新');
    return res.redirect('/admin/downloads/groups');
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      DOWNLOAD_GROUP_ERROR_CODES?.UPDATE_FAILED || 'DG008',
      '更新下載分組失敗',
      '/admin/downloads/groups'
    );
  }
};

// Delete a group
exports.deleteGroup = async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return handleControllerError(
        new Error('Invalid group ID'),
        req,
        res,
        DOWNLOAD_GROUP_ERROR_CODES?.INVALID_ID || 'DG009',
        '無效的分組 ID',
        '/admin/downloads/groups'
      );
    }

    await prisma.downloadGroup.update({
      where: { id },
      data: { deletedAt: new Date() }
    });

    req.flash('success_msg', '下載分組已成功刪除');
    return res.redirect('/admin/downloads/groups');
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      DOWNLOAD_GROUP_ERROR_CODES?.DELETE_FAILED || 'DG010',
      '刪除下載分組失敗',
      '/admin/downloads/groups'
    );
  }
};

// API: Get groups by category (for dynamic loading)
exports.getGroupsByCategory = async (req, res) => {
  try {
    const categoryId = parseInt(req.params.categoryId, 10);

    if (isNaN(categoryId)) {
      return res.status(400).json({ error: 'Invalid category ID' });
    }

    const groups = await prisma.downloadGroup.findMany({
      where: {
        categoryId: categoryId,
        deletedAt: null
      },
      orderBy: { order: 'asc' }
    });

    res.json(groups);
  } catch (error) {
    logger.error('Error fetching groups by category:', error);
    res.status(500).json({ error: 'Failed to fetch groups' });
  }
}; 