const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const fs = require('fs');
const path = require('path');
const multer = require('multer');
const logger = require('../config/logger');
const { FRONTPAGE_ERROR_CODES, COMMON_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

// Configure multer storage
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        const uploadDir = path.join(process.cwd(), 'public', 'uploads', 'frontpages');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir, { recursive: true });
        }
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        // Generate a unique filename using timestamp and random number
        const uniqueFilename = `${Date.now()}-${Math.round(Math.random() * 1000000)}${path.extname(file.originalname)}`;
        cb(null, uniqueFilename);
    }
});

const upload = multer({
    storage: storage,
    limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
    fileFilter: function (req, file, cb) {
        if (file.mimetype.startsWith('image/')) {
            cb(null, true);
        } else {
            cb(new Error('只允許上傳圖片檔案！'), false);
        }
    }
});

// Helper function to process images (simplified without sharp)
function processImage(file) {
    // Simply return the file information without processing
    return {
        filename: path.basename(file.path),
        originalName: file.originalname,
        path: `/uploads/frontpages/${path.basename(file.path)}`
    };
}

// Frontpage Items Controllers
exports.index = async (req, res) => {
    try {
        const items = await prisma.frontpageItem.findMany({
            include: {
                category: true,
                images: {
                    orderBy: {
                        order: 'asc'
                    }
                }
            },
            orderBy: {
                order: 'asc'
            }
        });
        
        res.render('admin/frontpage/index', {
            title: '首頁管理',
            items,
            success_msg: req.flash('success_msg'),
            error_msg: req.flash('error_msg')
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FRONTPAGE_ERROR_CODES.LIST_FAILED,
            '載入首頁項目失敗',
            '/admin/dashboard'
        );
    }
};

exports.createForm = async (req, res) => {
    try {
        const categories = await prisma.frontpageCategory.findMany({
            orderBy: {
                order: 'asc'
            }
        });
        
        res.render('admin/frontpage/items/create', {
            title: '新增首頁項目',
            categories,
            success_msg: req.flash('success_msg'),
            error_msg: req.flash('error_msg')
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FRONTPAGE_ERROR_CODES.CREATE_FORM_FAILED,
            '載入表單失敗',
            '/admin/frontpage'
        );
    }
};

exports.create = async (req, res) => {
    try {
        logger.info('Create function called');
        logger.info('Request body:', req.body);
        logger.info('Request files:', req.files);
        
        const { 
            title_tw, title_en, content_tw, content_en, type, categoryId, order, status, 
            image_alts, image_urls, image_alts_en, image_urls_en, redirect_to_edit
        } = req.body;
        
        // Create the frontpage item
        const item = await prisma.frontpageItem.create({
            data: {
                title_tw,
                title_en,
                content_tw: type === 'plain_text' ? content_tw : null,
                content_en: type === 'plain_text' ? content_en : null,
                type,
                order: parseInt(order) || 0,
                status,
                categoryId: parseInt(categoryId),
                authorId: req.session.user.id
            }
        });
        
        // Handle image uploads for picture type
        if (type === 'picture' && Array.isArray(req.files) && req.files.length > 0) {
            logger.info('Processing image uploads, found files:', req.files.length);
            
            // Group files by fieldname
            const chineseImages = req.files.filter(file => file.fieldname === 'images[]');
            const englishImages = req.files.filter(file => file.fieldname === 'images_en[]');
            
            logger.info('Chinese images:', chineseImages.length);
            logger.info('English images:', englishImages.length);
            
            // Process Chinese images
            if (chineseImages.length > 0) {
                const alts = image_alts ? (Array.isArray(image_alts) ? image_alts : [image_alts]) : [];
                const urls = image_urls ? (Array.isArray(image_urls) ? image_urls : [image_urls]) : [];
                
                for (let i = 0; i < chineseImages.length; i++) {
                    const processedImage = processImage(chineseImages[i]);
                    
                    await prisma.frontpageImage.create({
                        data: {
                            filename: processedImage.filename,
                            originalName: processedImage.originalName,
                            path: processedImage.path,
                            alt: alts[i] || null,
                            url: urls[i] || null,
                            order: i,
                            language: 'tw',
                            itemId: item.id
                        }
                    });
                }
            }
            
            // Process English images
            if (englishImages.length > 0) {
                const alts = image_alts_en ? (Array.isArray(image_alts_en) ? image_alts_en : [image_alts_en]) : [];
                const urls = image_urls_en ? (Array.isArray(image_urls_en) ? image_urls_en : [image_urls_en]) : [];
                
                for (let i = 0; i < englishImages.length; i++) {
                    const processedImage = processImage(englishImages[i]);
                    
                    await prisma.frontpageImage.create({
                        data: {
                            filename: processedImage.filename,
                            originalName: processedImage.originalName,
                            path: processedImage.path,
                            alt: alts[i] || null,
                            url: urls[i] || null,
                            order: i,
                            language: 'en',
                            itemId: item.id
                        }
                    });
                }
            }
        }
        
        req.flash('success_msg', '首頁項目已成功建立');
        
        // Check if redirect_to_edit is true, redirect to edit page if so
        if (redirect_to_edit === 'true') {
            res.redirect(`/admin/frontpage/items/${item.id}/edit`);
        } else {
            res.redirect('/admin/frontpage');
        }
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FRONTPAGE_ERROR_CODES.CREATE_FAILED,
            '建立首頁項目失敗',
            '/admin/frontpage/items/create'
        );
    }
};

exports.editForm = async (req, res) => {
    try {
        const { id } = req.params;
        
        const item = await prisma.frontpageItem.findUnique({
            where: { id: parseInt(id) },
            include: {
                images: {
                    orderBy: {
                        order: 'asc'
                    }
                }
            }
        });
        
        if (!item) {
            return handleControllerError(
                new Error(`Frontpage item with ID ${id} not found`),
                req,
                res,
                FRONTPAGE_ERROR_CODES.NOT_FOUND,
                '找不到該首頁項目',
                '/admin/frontpage'
            );
        }
        
        const categories = await prisma.frontpageCategory.findMany({
            orderBy: {
                order: 'asc'
            }
        });
        
        res.render('admin/frontpage/items/edit', {
            title: '編輯首頁項目',
            item,
            categories,
            success_msg: req.flash('success_msg'),
            error_msg: req.flash('error_msg')
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FRONTPAGE_ERROR_CODES.EDIT_FORM_FAILED,
            '載入編輯表單失敗',
            '/admin/frontpage'
        );
    }
};

exports.update = async (req, res) => {
    try {
        const { id } = req.params;
        logger.info('Update function called for id:', id);
        logger.info('Request body:', req.body);
        logger.info('Request files:', req.files);
        
        const { 
            title_tw, title_en, content_tw, content_en, type, categoryId, order, status, 
            existingImages, deleteImages, imageAlts, imageUrls, 
            image_alts, image_urls, image_alts_en, image_urls_en 
        } = req.body;
        
        // Update the frontpage item
        await prisma.frontpageItem.update({
            where: { id: parseInt(id) },
            data: {
                title_tw,
                title_en,
                content_tw: type === 'plain_text' ? content_tw : null,
                content_en: type === 'plain_text' ? content_en : null,
                type,
                order: parseInt(order) || 0,
                status,
                categoryId: parseInt(categoryId)
            }
        });
        
        // Handle image deletions
        if (deleteImages && Array.isArray(deleteImages)) {
            for (const imageId of deleteImages) {
                const image = await prisma.frontpageImage.findUnique({
                    where: { id: parseInt(imageId) }
                });
                
                if (image) {
                    // Delete the file
                    const filePath = path.join(process.cwd(), 'public', image.path);
                    try {
                        if (fs.existsSync(filePath)) {
                            fs.unlinkSync(filePath);
                            logger.info(`Successfully deleted image file: ${filePath}`);
                        }
                    } catch (err) {
                        logger.error(`Failed to delete image file: ${filePath}`, {
                            error: err,
                            errorCode: FRONTPAGE_ERROR_CODES.FILE_DELETION_FAILED
                        });
                    }
                    
                    // Delete the database record
                    await prisma.frontpageImage.delete({
                        where: { id: parseInt(imageId) }
                    });
                }
            }
        }
        
        // Update existing images (orders, alts, and urls)
        if (existingImages && Array.isArray(existingImages)) {
            for (const imageId of existingImages) {
                const order = req.body.imageOrders[imageId];
                const alt = imageAlts ? imageAlts[imageId] : null;
                const url = imageUrls ? imageUrls[imageId] : null;
                
                if (order !== undefined) {
                    await prisma.frontpageImage.update({
                        where: { id: parseInt(imageId) },
                        data: { 
                            order: parseInt(order) || 0,
                            alt: alt || null,
                            url: url || null
                        }
                    });
                }
            }
        }
        
        // Handle new image uploads
        if (type === 'picture' && Array.isArray(req.files) && req.files.length > 0) {
            logger.info('Processing image uploads, found files:', req.files.length);
            
            // Get the current highest order for Chinese images
            const highestOrderTwImage = await prisma.frontpageImage.findFirst({
                where: { 
                    itemId: parseInt(id),
                    language: 'tw'
                },
                orderBy: { order: 'desc' }
            });
            
            // Get the current highest order for English images
            const highestOrderEnImage = await prisma.frontpageImage.findFirst({
                where: { 
                    itemId: parseInt(id),
                    language: 'en'
                },
                orderBy: { order: 'desc' }
            });
            
            // Group files by fieldname
            const chineseImages = req.files.filter(file => file.fieldname === 'images[]');
            const englishImages = req.files.filter(file => file.fieldname === 'images_en[]');
            
            logger.info('Chinese images:', chineseImages.length);
            logger.info('English images:', englishImages.length);
            
            // Process Chinese images
            if (chineseImages.length > 0) {
                let startOrder = highestOrderTwImage ? highestOrderTwImage.order + 1 : 0;
                
                const alts = image_alts ? (Array.isArray(image_alts) ? image_alts : [image_alts]) : [];
                const urls = image_urls ? (Array.isArray(image_urls) ? image_urls : [image_urls]) : [];
                
                for (let i = 0; i < chineseImages.length; i++) {
                    const processedImage = processImage(chineseImages[i]);
                    
                    await prisma.frontpageImage.create({
                        data: {
                            filename: processedImage.filename,
                            originalName: processedImage.originalName,
                            path: processedImage.path,
                            alt: alts[i] || null,
                            url: urls[i] || null,
                            order: startOrder + i,
                            language: 'tw',
                            itemId: parseInt(id)
                        }
                    });
                }
            }
            
            // Process English images
            if (englishImages.length > 0) {
                let startOrder = highestOrderEnImage ? highestOrderEnImage.order + 1 : 0;
                
                const alts = image_alts_en ? (Array.isArray(image_alts_en) ? image_alts_en : [image_alts_en]) : [];
                const urls = image_urls_en ? (Array.isArray(image_urls_en) ? image_urls_en : [image_urls_en]) : [];
                
                for (let i = 0; i < englishImages.length; i++) {
                    const processedImage = processImage(englishImages[i]);
                    
                    await prisma.frontpageImage.create({
                        data: {
                            filename: processedImage.filename,
                            originalName: processedImage.originalName,
                            path: processedImage.path,
                            alt: alts[i] || null,
                            url: urls[i] || null,
                            order: startOrder + i,
                            language: 'en',
                            itemId: parseInt(id)
                        }
                    });
                }
            }
        }
        
        req.flash('success_msg', '首頁項目已成功更新');
        res.redirect('/admin/frontpage');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FRONTPAGE_ERROR_CODES.UPDATE_FAILED,
            '更新首頁項目失敗',
            `/admin/frontpage/items/${req.params.id}/edit`
        );
    }
};

exports.delete = async (req, res) => {
    try {
        const { id } = req.params;
        
        // Get the item with its images
        const item = await prisma.frontpageItem.findUnique({
            where: { id: parseInt(id) },
            include: { images: true }
        });
        
        if (!item) {
            return handleControllerError(
                new Error(`Frontpage item with ID ${id} not found`),
                req,
                res,
                FRONTPAGE_ERROR_CODES.NOT_FOUND,
                '找不到該首頁項目',
                '/admin/frontpage'
            );
        }
        
        // Delete associated images
        for (const image of item.images) {
            const filePath = path.join(process.cwd(), 'public', image.path);
            try {
                if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                    logger.info(`Successfully deleted image file: ${filePath}`);
                }
            } catch (err) {
                logger.error(`Failed to delete image file: ${filePath}`, {
                    error: err,
                    errorCode: FRONTPAGE_ERROR_CODES.FILE_DELETION_FAILED
                });
            }
        }
        
        // Delete the item (cascade will delete the images in the database)
        await prisma.frontpageItem.delete({
            where: { id: parseInt(id) }
        });
        
        req.flash('success_msg', '首頁項目已成功刪除');
        res.redirect('/admin/frontpage');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FRONTPAGE_ERROR_CODES.DELETE_FAILED,
            '刪除首頁項目失敗',
            '/admin/frontpage'
        );
    }
};

// Frontpage Categories Controllers
exports.categoriesIndex = async (req, res) => {
    try {
        const categories = await prisma.frontpageCategory.findMany({
            orderBy: {
                order: 'asc'
            }
        });
        
        res.render('admin/frontpage/categories/index', {
            title: '首頁分類管理',
            categories,
            success_msg: req.flash('success_msg'),
            error_msg: req.flash('error_msg')
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FRONTPAGE_ERROR_CODES.CATEGORY_LIST_FAILED,
            '載入首頁分類失敗',
            '/admin/dashboard'
        );
    }
};

exports.createCategory = async (req, res) => {
    try {
        const { name_tw, order } = req.body;
        
        await prisma.frontpageCategory.create({
            data: {
                name_tw,
                order: parseInt(order) || 0
            }
        });
        
        req.flash('success_msg', '首頁分類已成功建立');
        res.redirect('/admin/frontpage/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FRONTPAGE_ERROR_CODES.CATEGORY_CREATE_FAILED,
            '建立首頁分類失敗',
            '/admin/frontpage/categories'
        );
    }
};

exports.updateCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const { name_tw, order } = req.body;
        
        await prisma.frontpageCategory.update({
            where: { id: parseInt(id) },
            data: {
                name_tw,
                order: parseInt(order) || 0
            }
        });
        
        req.flash('success_msg', '首頁分類已成功更新');
        res.redirect('/admin/frontpage/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FRONTPAGE_ERROR_CODES.CATEGORY_UPDATE_FAILED,
            '更新首頁分類失敗',
            '/admin/frontpage/categories'
        );
    }
};

exports.deleteCategory = async (req, res) => {
    try {
        const { id } = req.params;
        
        // Check if there are any items using this category
        const itemsCount = await prisma.frontpageItem.count({
            where: { categoryId: parseInt(id) }
        });
        
        if (itemsCount > 0) {
            return handleControllerError(
                new Error(`Cannot delete category with ID ${id} because it has ${itemsCount} items using it`),
                req,
                res,
                FRONTPAGE_ERROR_CODES.CATEGORY_IN_USE,
                '無法刪除此分類，因為有項目正在使用它',
                '/admin/frontpage/categories'
            );
        }
        
        await prisma.frontpageCategory.delete({
            where: { id: parseInt(id) }
        });
        
        req.flash('success_msg', '首頁分類已成功刪除');
        res.redirect('/admin/frontpage/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FRONTPAGE_ERROR_CODES.CATEGORY_DELETE_FAILED,
            '刪除首頁分類失敗',
            '/admin/frontpage/categories'
        );
    }
};

// Middleware for handling file uploads
exports.uploadImages = upload.any(); 