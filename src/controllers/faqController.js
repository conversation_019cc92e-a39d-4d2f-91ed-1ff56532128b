const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const slugify = require('slugify');
const { FAQ_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

// List all FAQ categories
exports.listCategories = async (req, res) => {
    try {
        const categories = await prisma.faqCategory.findMany({
            where: {
                deletedAt: null
            },
            include: {
                _count: {
                    select: {
                        faqItems: {
                            where: {
                                deletedAt: null
                            }
                        }
                    }
                }
            },
            orderBy: {
                order: 'asc'
            }
        });
        
        res.render('admin/faq/categories/index', {
            title: 'FAQ Categories',
            categories
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.LIST_CATEGORIES_FAILED,
            '載入常見問題分類失敗',
            '/admin/dashboard'
        );
    }
};

// Render create FAQ category form
exports.renderCreateCategory = (req, res) => {
    try {
        res.render('admin/faq/categories/create', {
            title: 'Create FAQ Category'
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.RENDER_FORM_FAILED,
            '載入建立常見問題分類表單失敗',
            '/admin/faq/categories'
        );
    }
};

// Create a new FAQ category
exports.createCategory = async (req, res) => {
    try {
        const { name_en, name_tw, description_en, description_tw, order } = req.body;
        
        // Generate slug from English name
        const slug = slugify(name_en, {
            lower: true,
            strict: true
        });
        
        // Check if slug already exists
        const existingCategory = await prisma.faqCategory.findUnique({
            where: { slug }
        });
        
        if (existingCategory) {
            return handleControllerError(
                new Error('Category with this name already exists'),
                req,
                res,
                FAQ_ERROR_CODES.CATEGORY_EXISTS,
                '已存在相同名稱的分類',
                '/admin/faq/categories/create'
            );
        }
        
        // Create the category
        await prisma.faqCategory.create({
            data: {
                name_en,
                name_tw,
                slug,
                description_en,
                description_tw,
                order: order ? parseInt(order) : 0
            }
        });
        
        req.flash('success_msg', '常見問題分類已成功建立');
        res.redirect('/admin/faq/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.CREATE_CATEGORY_FAILED,
            '建立常見問題分類失敗',
            '/admin/faq/categories/create'
        );
    }
};

// Render edit FAQ category form
exports.renderEditCategory = async (req, res) => {
    try {
        const { id } = req.params;
        
        const category = await prisma.faqCategory.findUnique({
            where: { id: parseInt(id) }
        });
        
        if (!category) {
            return handleControllerError(
                new Error('FAQ category not found'),
                req,
                res,
                FAQ_ERROR_CODES.CATEGORY_NOT_FOUND,
                '找不到常見問題分類',
                '/admin/faq/categories'
            );
        }
        
        res.render('admin/faq/categories/edit', {
            title: `Edit FAQ Category: ${category.name_en}`,
            category
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.RENDER_FORM_FAILED,
            '載入常見問題分類失敗',
            '/admin/faq/categories'
        );
    }
};

// Update a FAQ category
exports.updateCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const { name_en, name_tw, description_en, description_tw, order } = req.body;
        
        // Generate slug from English name
        const slug = slugify(name_en, {
            lower: true,
            strict: true
        });
        
        // Check if slug already exists on a different category
        const existingCategory = await prisma.faqCategory.findFirst({
            where: {
                slug,
                id: {
                    not: parseInt(id)
                }
            }
        });
        
        if (existingCategory) {
            return handleControllerError(
                new Error('Category with this name already exists'),
                req,
                res,
                FAQ_ERROR_CODES.CATEGORY_EXISTS,
                '已存在相同名稱的分類',
                `/admin/faq/categories/edit/${id}`
            );
        }
        
        // Update the category
        await prisma.faqCategory.update({
            where: { id: parseInt(id) },
            data: {
                name_en,
                name_tw,
                slug,
                description_en,
                description_tw,
                order: order ? parseInt(order) : 0,
                updatedAt: new Date()
            }
        });
        
        req.flash('success_msg', '常見問題分類已成功更新');
        res.redirect('/admin/faq/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.UPDATE_CATEGORY_FAILED,
            '更新常見問題分類失敗',
            `/admin/faq/categories/edit/${req.params.id}`
        );
    }
};

// Delete a FAQ category
exports.deleteCategory = async (req, res) => {
    try {
        const { id } = req.params;
        
        // Soft delete the category
        await prisma.faqCategory.update({
            where: { id: parseInt(id) },
            data: {
                deletedAt: new Date()
            }
        });
        
        // Soft delete all items in this category
        await prisma.faqItem.updateMany({
            where: { categoryId: parseInt(id) },
            data: {
                deletedAt: new Date()
            }
        });
        
        req.flash('success_msg', '常見問題分類已成功刪除');
        res.redirect('/admin/faq/categories');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.DELETE_CATEGORY_FAILED,
            '刪除常見問題分類失敗',
            '/admin/faq/categories'
        );
    }
};

// List all FAQ items
exports.listItems = async (req, res) => {
    try {
        const { search, category, status, page = 1 } = req.query;
        const perPage = 10;
        const skip = (parseInt(page) - 1) * perPage;
        
        // Build filters
        const filters = {
            deletedAt: null,
        };
        
        if (search) {
            filters.OR = [
                { title_en: { contains: search } },
                { title_tw: { contains: search } }
            ];
        }
        
        if (category) {
            filters.categoryId = parseInt(category);
        }
        
        if (status) {
            filters.status = status;
        }
        
        // Get total count for pagination
        const totalItems = await prisma.faqItem.count({ where: filters });
        const totalPages = Math.ceil(totalItems / perPage);
        const currentPage = parseInt(page);
        
        // Get categories for the filter
        const categories = await prisma.faqCategory.findMany({
            where: { deletedAt: null },
            orderBy: { name_en: 'asc' }
        });

        // Get items with pagination
        const items = await prisma.faqItem.findMany({
            where: filters,
            skip,
            take: perPage,
            include: {
                category: true,
                author: {
                    select: {
                        username: true
                    }
                }
            },
            orderBy: [
                {
                    categoryId: 'asc'
                },
                {
                    order: 'asc'
                }
            ]
        });
        
        res.render('admin/faq/items/index', {
            title: 'FAQ Items',
            items,
            categories,
            currentPage,
            totalPages,
            totalItems,
            perPage,
            search: search || '',
            selectedCategory: category ? parseInt(category) : '',
            status: status || ''
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.LIST_ITEMS_FAILED,
            '載入常見問題項目失敗',
            '/admin/dashboard'
        );
    }
};

// Render create FAQ item form
exports.renderCreateItem = async (req, res) => {
    try {
        const categories = await prisma.faqCategory.findMany({
            where: {
                deletedAt: null
            },
            orderBy: {
                name_en: 'asc'
            }
        });
        
        res.render('admin/faq/items/create', {
            title: 'Create FAQ Item',
            categories
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.RENDER_FORM_FAILED,
            '載入常見問題分類失敗',
            '/admin/faq/items'
        );
    }
};

// Create a new FAQ item
exports.createItem = async (req, res) => {
    try {
        const { title_en, title_tw, content_en, content_tw, categoryId, order, status } = req.body;
        
        // Parse the Quill Delta JSON for English content
        let processedContentEn = content_en;
        try {
            // Check if the content is a valid Quill Delta JSON
            const deltaObjEn = JSON.parse(content_en);
            // Store the Delta JSON as is - we'll render it properly on the frontend
            processedContentEn = content_en;
        } catch (e) {
            // If parsing fails, it's not JSON, so use as is
            processedContentEn = content_en;
            logger.warn(`${FAQ_ERROR_CODES.CONTENT_PROCESSING_ERROR}: English content is not in Delta JSON format: ${e.message}`);
        }
        
        // Parse the Quill Delta JSON for Traditional Chinese content
        let processedContentTw = content_tw;
        try {
            // Check if the content is a valid Quill Delta JSON
            const deltaObjTw = JSON.parse(content_tw);
            // Store the Delta JSON as is - we'll render it properly on the frontend
            processedContentTw = content_tw;
        } catch (e) {
            // If parsing fails, it's not JSON, so use as is
            processedContentTw = content_tw;
            logger.warn(`${FAQ_ERROR_CODES.CONTENT_PROCESSING_ERROR}: Traditional Chinese content is not in Delta JSON format: ${e.message}`);
        }
        
        // Create the item
        await prisma.faqItem.create({
            data: {
                title_en,
                title_tw,
                content_en: processedContentEn,
                content_tw: processedContentTw,
                order: order ? parseInt(order) : 0,
                status: status || 'draft',
                category: {
                    connect: { id: parseInt(categoryId) }
                },
                author: {
                    connect: { id: req.session.user.id }
                }
            }
        });
        
        req.flash('success_msg', '常見問題項目已成功建立');
        res.redirect('/admin/faq/items');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.CREATE_ITEM_FAILED,
            '建立常見問題項目失敗',
            '/admin/faq/items/create'
        );
    }
};

// Render edit FAQ item form
exports.renderEditItem = async (req, res) => {
    try {
        const { id } = req.params;
        
        const [item, categories] = await Promise.all([
            prisma.faqItem.findUnique({
                where: { id: parseInt(id) },
                include: {
                    category: true
                }
            }),
            prisma.faqCategory.findMany({
                where: {
                    deletedAt: null
                },
                orderBy: {
                    name_en: 'asc'
                }
            })
        ]);
        
        if (!item) {
            return handleControllerError(
                new Error('FAQ item not found'),
                req,
                res,
                FAQ_ERROR_CODES.ITEM_NOT_FOUND,
                '找不到常見問題項目',
                '/admin/faq/items'
            );
        }
        
        res.render('admin/faq/items/edit', {
            title: `Edit FAQ Item: ${item.title_en}`,
            item,
            categories
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.RENDER_FORM_FAILED,
            '載入常見問題項目失敗',
            '/admin/faq/items'
        );
    }
};

// Update a FAQ item
exports.updateItem = async (req, res) => {
    try {
        const { id } = req.params;
        const { title_en, title_tw, content_en, content_tw, categoryId, order, status } = req.body;
        
        // Parse the Quill Delta JSON for English content
        let processedContentEn = content_en;
        try {
            // Check if the content is a valid Quill Delta JSON
            const deltaObjEn = JSON.parse(content_en);
            // Store the Delta JSON as is - we'll render it properly on the frontend
            processedContentEn = content_en;
        } catch (e) {
            // If parsing fails, it's not JSON, so use as is
            processedContentEn = content_en;
            logger.warn(`${FAQ_ERROR_CODES.CONTENT_PROCESSING_ERROR}: English content is not in Delta JSON format: ${e.message}`);
        }
        
        // Parse the Quill Delta JSON for Traditional Chinese content
        let processedContentTw = content_tw;
        try {
            // Check if the content is a valid Quill Delta JSON
            const deltaObjTw = JSON.parse(content_tw);
            // Store the Delta JSON as is - we'll render it properly on the frontend
            processedContentTw = content_tw;
        } catch (e) {
            // If parsing fails, it's not JSON, so use as is
            processedContentTw = content_tw;
            logger.warn(`${FAQ_ERROR_CODES.CONTENT_PROCESSING_ERROR}: Traditional Chinese content is not in Delta JSON format: ${e.message}`);
        }
        
        // Validate the status value
        const validStatuses = ['draft', 'published', 'archived'];
        if (!validStatuses.includes(status)) {
            return handleControllerError(
                new Error(`Invalid status value: ${status}`),
                req,
                res,
                FAQ_ERROR_CODES.INVALID_STATUS,
                `無效的狀態值: ${status}`,
                `/admin/faq/items/edit/${id}`
            );
        }
        
        // Update the item
        await prisma.faqItem.update({
            where: { id: parseInt(id) },
            data: {
                title_en,
                title_tw,
                content_en: processedContentEn,
                content_tw: processedContentTw,
                order: order ? parseInt(order) : 0,
                status,
                categoryId: parseInt(categoryId),
                updatedAt: new Date()
            }
        });
        
        req.flash('success_msg', '常見問題項目已成功更新');
        res.redirect('/admin/faq/items');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.UPDATE_ITEM_FAILED,
            '更新常見問題項目失敗',
            `/admin/faq/items/edit/${req.params.id}`
        );
    }
};

// Delete a FAQ item
exports.deleteItem = async (req, res) => {
    try {
        const { id } = req.params;
        
        // Soft delete the item
        await prisma.faqItem.update({
            where: { id: parseInt(id) },
            data: {
                deletedAt: new Date()
            }
        });
        
        req.flash('success_msg', '常見問題項目已成功刪除');
        res.redirect('/admin/faq/items');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            FAQ_ERROR_CODES.DELETE_ITEM_FAILED,
            '刪除常見問題項目失敗',
            '/admin/faq/items'
        );
    }
};