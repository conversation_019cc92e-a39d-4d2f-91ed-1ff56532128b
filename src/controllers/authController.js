const bcrypt = require('bcryptjs');
const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const { AUTH_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

exports.renderLoginForm = (req, res) => {
    try {
        if (req.session.user) {
            return res.redirect('/admin/dashboard');
        }
        res.render('admin/login', {
            title: 'Login',
            layout: 'layouts/auth'
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            AUTH_ERROR_CODES.LOGIN_FAILED,
            '載入登入頁面時發生錯誤',
            '/admin'
        );
    }
};

exports.login = async (req, res) => {
    try {
        const { username, password, captcha } = req.body;

        // Find user with username only
        const user = await prisma.user.findUnique({
            where: { username },
            select: {
                id: true,
                username: true,
                email: true,
                password: true,
                isActive: true,
                contactName_zh: true,
                contactName_en: true,
                contactPhone: true,
                organizationName_zh: true,
                organizationName_en: true,
                role: {
                    select: {
                        id: true,
                        name: true,
                        permissions: true
                    }
                }
            }
        });

        // Check if user exists and is active
        if (!user || !user.isActive) {
            return handleControllerError(
                new Error('Invalid credentials or inactive account'),
                req,
                res,
                AUTH_ERROR_CODES.INVALID_CREDENTIALS,
                '使用者名稱或密碼無效',
                '/admin/login',
                false  // Don't log stack trace for security reasons
            );
        }

        // Check if user has guest role - guests should use guest login page
        if (user.role.name === 'guest') {
            return handleControllerError(
                new Error('Guest user attempted regular login'),
                req,
                res,
                AUTH_ERROR_CODES.WRONG_LOGIN_PAGE,
                '訪客用戶請使用訪客登入頁面',
                '/admin/guest-login',
                false  // Don't log stack trace for security reasons
            );
        }

        // Verify password
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            return handleControllerError(
                new Error('Invalid password'),
                req,
                res,
                AUTH_ERROR_CODES.INVALID_CREDENTIALS,
                '使用者名稱或密碼無效',
                '/admin/login',
                false  // Don't log stack trace for security reasons
            );
        }

        // Update last login
        await prisma.user.update({
            where: { id: user.id },
            data: { lastLogin: new Date() }
        });

        // Set session data - use actual user role
        req.session.user = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role.name,
            permissions: user.role.permissions,
            contactName_zh: user.contactName_zh || null,
            contactName_en: user.contactName_en || null,
            contactPhone: user.contactPhone || null,
            organizationName_zh: user.organizationName_zh || null,
            organizationName_en: user.organizationName_en || null
        };

        logger.info(`User ${user.username} (${user.role.name}) logged in`);
        res.redirect('/admin/dashboard');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            AUTH_ERROR_CODES.LOGIN_FAILED,
            '登入時發生錯誤',
            '/admin/login'
        );
    }
};

// Helper function to get role display name
function getRoleDisplayName(role) {
    const roleNames = {
        'super_admin': '超級管理員',
        'admin': '管理員',
        'editor': '編輯者',
        'guest': '訪客',
        'visitor': '僅限查看訪客'
    };
    return roleNames[role] || role;
}

exports.logout = (req, res) => {
    try {
        req.session.destroy((err) => {
            if (err) {
                throw err;
            }
            res.redirect('/admin/login');
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            AUTH_ERROR_CODES.LOGOUT_FAILED,
            '登出時發生錯誤',
            '/admin/dashboard'
        );
    }
};

exports.renderGuestLoginForm = (req, res) => {
    try {
        if (req.session.user) {
            return res.redirect('/admin/dashboard');
        }
        res.render('admin/guest-login', {
            title: '訪客登入',
            layout: 'layouts/auth'
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            AUTH_ERROR_CODES.GUEST_LOGIN_FAILED,
            '載入訪客登入頁面時發生錯誤',
            '/admin'
        );
    }
};

// Guest login functionality
exports.guestLogin = async (req, res) => {
    try {
        const { username, password, captcha } = req.body;

        // Find user with username only
        const user = await prisma.user.findUnique({
            where: { username },
            select: {
                id: true,
                username: true,
                email: true,
                password: true,
                isActive: true,
                contactName_zh: true,
                contactName_en: true,
                contactPhone: true,
                organizationName_zh: true,
                organizationName_en: true,
                role: {
                    select: {
                        id: true,
                        name: true,
                        permissions: true
                    }
                }
            }
        });

        // Check if user exists and is active
        if (!user || !user.isActive) {
            return handleControllerError(
                new Error('Invalid credentials or inactive account'),
                req,
                res,
                AUTH_ERROR_CODES.INVALID_CREDENTIALS,
                '無效的訪客使用者名稱或密碼',
                '/admin/guest-login',
                false  // Don't log stack trace for security reasons
            );
        }

        // Check if user has the appropriate role (either guest or visitor)
        if (user.role.name !== 'guest' && user.role.name !== 'visitor') {
            return handleControllerError(
                new Error('Account is not a guest or visitor account'),
                req,
                res,
                AUTH_ERROR_CODES.INVALID_CREDENTIALS,
                '無效的訪客使用者名稱或密碼',
                '/admin/guest-login',
                false  // Don't log stack trace for security reasons
            );
        }

        // Verify password
        const isMatch = await bcrypt.compare(password, user.password);
        if (!isMatch) {
            return handleControllerError(
                new Error('Invalid password'),
                req,
                res,
                AUTH_ERROR_CODES.INVALID_CREDENTIALS,
                '無效的訪客使用者名稱或密碼',
                '/admin/guest-login',
                false  // Don't log stack trace for security reasons
            );
        }

        // Check terms agreement
        if (!req.body.terms) {
            return handleControllerError(
                new Error('Terms not accepted'),
                req,
                res,
                AUTH_ERROR_CODES.LOGIN_FAILED,
                '您必須同意保密條款',
                '/admin/guest-login',
                false  // Don't log stack trace for security reasons
            );
        }

        // Update last login
        await prisma.user.update({
            where: { id: user.id },
            data: { lastLogin: new Date() }
        });

        // Set session data using the user's actual role
        req.session.user = {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role.name,
            permissions: user.role.permissions,
            contactName_zh: user.contactName_zh || null,
            contactName_en: user.contactName_en || null,
            contactPhone: user.contactPhone || null,
            organizationName_zh: user.organizationName_zh || null,
            organizationName_en: user.organizationName_en || null
        };

        logger.info(`${user.role.name === 'visitor' ? 'Visitor' : 'Guest'} user ${user.username} logged in`);

        if (user.role.name === 'visitor') {
            res.redirect('/admin/visits');
        } else {
            res.redirect('/admin/dashboard');
        }
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            AUTH_ERROR_CODES.GUEST_LOGIN_FAILED,
            '訪客登入時發生錯誤',
            '/admin/guest-login'
        );
    }
};
