const express = require('express');
const prisma = require('../lib/prisma');
const path = require('path');
const fs = require('fs');
const logger = require('../config/logger');
const { SITE_SETTINGS_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError, handleApiError } = require('../utils/errorHandler');

// Render the site settings form
exports.renderSiteSettingsForm = async (req, res) => {
  try {
    // Get the current settings if they exist
    const settings = await prisma.siteSettings.findFirst();
    
    res.render('admin/site-settings/form', {
      title: 'Site Settings',
      layout: 'layouts/admin',
      settings,
      error_msg: req.flash('error_msg'),
      success_msg: req.flash('success_msg')
    });
  } catch (error) {
    handleControllerError(
      error, 
      req, 
      res, 
      SITE_SETTINGS_ERROR_CODES.RENDER_FAILED, 
      '載入網站設定失敗', 
      '/admin/dashboard'
    );
  }
};

// Save site settings
exports.saveSiteSettings = async (req, res) => {
  try {
    const { 
      site_name_en, 
      site_name_tw, 
      site_url,
      logo_alt_en,
      logo_alt_tw
    } = req.body;

    // Prepare data object
    const data = {
      site_name_en,
      site_name_tw,
      site_url,
      logo_alt_en,
      logo_alt_tw
    };

    // Process logo uploads if they exist
    if (req.files) {
      try {
        if (req.files.logo_desktop) {
          // Store path relative to public directory
          data.logo_desktop_path = req.files.logo_desktop[0].path.replace(/^public\//, '');
        }

        if (req.files.logo_tablet) {
          // Store path relative to public directory
          data.logo_tablet_path = req.files.logo_tablet[0].path.replace(/^public\//, '');
        }

        if (req.files.logo_mobile) {
          // Store path relative to public directory
          data.logo_mobile_path = req.files.logo_mobile[0].path.replace(/^public\//, '');
        }
      } catch (uploadError) {
        logger.error(`${SITE_SETTINGS_ERROR_CODES.UPLOAD_FAILED}: Error processing file uploads:`, { 
          error: uploadError.message,
          files: req.files 
        });
        // Continue with other settings even if uploads fail
      }
    }

    // Check if settings already exist
    const existingSettings = await prisma.siteSettings.findFirst();

    if (existingSettings) {
      // Update existing settings
      await prisma.siteSettings.update({
        where: { id: existingSettings.id },
        data
      });
    } else {
      // Create new settings
      await prisma.siteSettings.create({ data });
    }

    req.flash('success_msg', '網站設定已成功儲存');
    res.redirect('/admin/site-settings');
  } catch (error) {
    handleControllerError(
      error, 
      req, 
      res, 
      SITE_SETTINGS_ERROR_CODES.SAVE_FAILED, 
      '儲存網站設定失敗', 
      '/admin/site-settings',
      { requestBody: req.body }
    );
  }
};

// API endpoint to get site settings
exports.getSiteSettings = async (req, res) => {
  try {
    const settings = await prisma.siteSettings.findFirst();
    res.json(settings || {});
  } catch (error) {
    handleApiError(
      error,
      req,
      res,
      SITE_SETTINGS_ERROR_CODES.FETCH_FAILED,
      '載入網站設定失敗',
      500
    );
  }
}; 