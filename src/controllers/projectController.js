const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
const logger = require('../config/logger');
const { PROJECT_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError, handleApiError } = require('../utils/errorHandler');

// List all projects
exports.listProjects = async (req, res) => {
    try {
        const projects = await prisma.project.findMany({
            orderBy: { name_zh: 'asc' }
        });
        
        res.render('admin/projects/index', {
            projects,
            title: '計畫管理',
            success_msg: req.flash('success_msg'),
            error_msg: req.flash('error_msg')
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            PROJECT_ERROR_CODES.LIST_FAILED,
            '載入計畫列表時發生錯誤',
            '/admin/dashboard'
        );
    }
};

// Render create project form
exports.renderCreateForm = async (req, res) => {
    try {
        res.render('admin/projects/create', {
            title: '新增計畫',
            success_msg: req.flash('success_msg'),
            error_msg: req.flash('error_msg')
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            PROJECT_ERROR_CODES.CREATE_FORM_FAILED,
            '載入建立計畫表單時發生錯誤',
            '/admin/projects'
        );
    }
};

// Create a new project
exports.createProject = async (req, res) => {
    try {
        const { name_zh, name_en, description, isActive } = req.body;
        
        // Create project
        await prisma.project.create({
            data: {
                name_zh,
                name_en: name_en || null,
                description: description || null,
                isActive: isActive === 'true' ? true : false
            }
        });
        
        req.flash('success_msg', '計畫已成功建立');
        res.redirect('/admin/projects');
    } catch (error) {
        let errorMessage = '建立計畫時發生錯誤';
        let errorCode = PROJECT_ERROR_CODES.CREATE_FAILED;
        
        // Check for unique constraint violation
        if (error.code === 'P2002') {
            errorMessage = '此計畫名稱已存在，請使用其他名稱';
            errorCode = PROJECT_ERROR_CODES.NAME_EXISTS;
        }
        
        handleControllerError(
            error,
            req,
            res,
            errorCode,
            errorMessage,
            '/admin/projects/create',
            { requestBody: req.body }
        );
    }
};

// Render edit project form
exports.renderEditForm = async (req, res) => {
    try {
        const projectId = parseInt(req.params.id);
        
        const project = await prisma.project.findUnique({
            where: { id: projectId }
        });
        
        if (!project) {
            req.flash('error_msg', `找不到指定的計畫 [${PROJECT_ERROR_CODES.NOT_FOUND}]`);
            return res.redirect('/admin/projects');
        }
        
        res.render('admin/projects/edit', {
            project,
            title: '編輯計畫',
            success_msg: req.flash('success_msg'),
            error_msg: req.flash('error_msg')
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            PROJECT_ERROR_CODES.EDIT_FORM_FAILED,
            '載入編輯計畫表單時發生錯誤',
            '/admin/projects',
            { projectId: req.params.id }
        );
    }
};

// Update a project
exports.updateProject = async (req, res) => {
    try {
        const projectId = parseInt(req.params.id);
        const { name_zh, name_en, description, isActive } = req.body;
        
        // Update project
        await prisma.project.update({
            where: { id: projectId },
            data: {
                name_zh,
                name_en: name_en || null,
                description: description || null,
                isActive: isActive === 'true' ? true : false
            }
        });
        
        req.flash('success_msg', '計畫已成功更新');
        res.redirect('/admin/projects');
    } catch (error) {
        let errorMessage = '更新計畫時發生錯誤';
        let errorCode = PROJECT_ERROR_CODES.UPDATE_FAILED;
        
        // Check for unique constraint violation
        if (error.code === 'P2002') {
            errorMessage = '此計畫名稱已存在，請使用其他名稱';
            errorCode = PROJECT_ERROR_CODES.NAME_EXISTS;
        }
        
        handleControllerError(
            error,
            req,
            res,
            errorCode,
            errorMessage,
            `/admin/projects/edit/${req.params.id}`,
            { projectId: req.params.id, requestBody: req.body }
        );
    }
};

// Delete a project
exports.deleteProject = async (req, res) => {
    try {
        const projectId = parseInt(req.params.id);
        
        // Check if project has associated users or visits
        const usersCount = await prisma.user.count({
            where: { projectId }
        });
        
        const visitsCount = await prisma.visit.count({
            where: { projectId }
        });
        
        if (usersCount > 0 || visitsCount > 0) {
            req.flash('error_msg', `此計畫已有關聯的用戶或訪廠記錄，無法刪除 [${PROJECT_ERROR_CODES.HAS_DEPENDENCIES}]`);
            return res.redirect('/admin/projects');
        }
        
        // Delete project
        await prisma.project.delete({
            where: { id: projectId }
        });
        
        req.flash('success_msg', '計畫已成功刪除');
        res.redirect('/admin/projects');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            PROJECT_ERROR_CODES.DELETE_FAILED,
            '刪除計畫時發生錯誤',
            '/admin/projects',
            { projectId: req.params.id }
        );
    }
};

// API endpoints for getting projects data
exports.getProjects = async (req, res) => {
    try {
        const projects = await prisma.project.findMany({
            where: {
                isActive: true
            },
            orderBy: {
                name_zh: 'asc'
            }
        });

        res.json(projects);
    } catch (error) {
        handleApiError(
            error,
            req,
            res,
            PROJECT_ERROR_CODES.API_FETCH_FAILED,
            'Failed to fetch projects',
            500
        );
    }
};
