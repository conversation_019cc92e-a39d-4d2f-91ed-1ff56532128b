const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const fs = require('fs');
const path = require('path');
const { PROMOTION_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

// List all promotion attachments
exports.listAttachments = async (req, res) => {
  try {
    const attachments = await prisma.promotionAttachment.findMany({
      where: { deletedAt: null },
      orderBy: [
        { groupId: 'asc' },
        { createdAt: 'desc' }
      ],
      include: {
        group: {
          include: {
            category: true
          }
        }
      }
    });

    res.render('admin/promotions/attachments/list', {
      title: 'Promotion Attachments',
      layout: 'layouts/admin',
      attachments,
      success_msg: req.flash('success_msg'),
      error_msg: req.flash('error_msg')
    });
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.ATTACHMENT_LIST_FAILED || 'PA001',
      '載入推動方案附件失敗',
      '/admin/promotions'
    );
  }
};

// Render create attachment form
exports.renderCreateAttachment = async (req, res) => {
  try {
    const [categories, groups] = await Promise.all([
      prisma.promotionCategory.findMany({
        where: { deletedAt: null },
        orderBy: { order: 'asc' }
      }),
      prisma.promotionGroup.findMany({
        where: { deletedAt: null },
        include: { category: true },
        orderBy: [
          { categoryId: 'asc' },
          { order: 'asc' }
        ]
      })
    ]);

    res.render('admin/promotions/attachments/create', {
      title: 'Create Promotion Attachment',
      layout: 'layouts/admin',
      categories,
      groups
    });
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.ATTACHMENT_CREATE_FORM_FAILED || 'PA002',
      '載入建立附件表單失敗',
      '/admin/promotions/attachments'
    );
  }
};

// Create a new attachment
exports.createAttachment = async (req, res) => {
  try {
    const {
      title_en, title_tw,
      description_en, description_tw,
      attachment_name_en, attachment_name_tw,
      groupId
    } = req.body;

    if (!req.file) {
      req.flash('error_msg', '請選擇要上傳的檔案');
      return res.redirect('/admin/promotions/attachments/create');
    }

    const { filename, originalname, mimetype, size } = req.file;
    const decodedOriginalName = Buffer.from(originalname, 'latin1').toString('utf8');

    await prisma.promotionAttachment.create({
      data: {
        title_en,
        title_tw,
        description_en,
        description_tw,
        attachment_name_en,
        attachment_name_tw,
        groupId: parseInt(groupId),
        filename,
        originalName: decodedOriginalName,
        mimeType: mimetype,
        size,
        path: req.file.relativePath
      }
    });

    req.flash('success_msg', '推動方案附件已成功建立');
    return res.redirect('/admin/promotions/attachments');
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.ATTACHMENT_CREATE_FAILED || 'PA003',
      '建立推動方案附件失敗',
      '/admin/promotions/attachments/create'
    );
  }
};

// Render edit attachment form
exports.renderEditAttachment = async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return handleControllerError(
        new Error('Invalid attachment ID'),
        req,
        res,
        PROMOTION_ERROR_CODES?.INVALID_ATTACHMENT_ID || 'PA004',
        '無效的附件 ID',
        '/admin/promotions/attachments'
      );
    }

    const [attachment, categories, groups] = await Promise.all([
      prisma.promotionAttachment.findUnique({
        where: { id },
        include: {
          group: {
            include: {
              category: true
            }
          }
        }
      }),
      prisma.promotionCategory.findMany({
        where: { deletedAt: null },
        orderBy: { order: 'asc' }
      }),
      prisma.promotionGroup.findMany({
        where: { deletedAt: null },
        include: { category: true },
        orderBy: [
          { categoryId: 'asc' },
          { order: 'asc' }
        ]
      })
    ]);

    if (!attachment) {
      return handleControllerError(
        new Error('Attachment not found'),
        req,
        res,
        PROMOTION_ERROR_CODES?.ATTACHMENT_NOT_FOUND || 'PA005',
        '找不到附件',
        '/admin/promotions/attachments'
      );
    }

    res.render('admin/promotions/attachments/edit', {
      title: 'Edit Promotion Attachment',
      layout: 'layouts/admin',
      attachment,
      categories,
      groups
    });
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.ATTACHMENT_EDIT_FORM_FAILED || 'PA006',
      '載入推動方案附件失敗',
      '/admin/promotions/attachments'
    );
  }
};

// Update an attachment
exports.updateAttachment = async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return handleControllerError(
        new Error('Invalid attachment ID'),
        req,
        res,
        PROMOTION_ERROR_CODES?.INVALID_ATTACHMENT_ID || 'PA007',
        '無效的附件 ID',
        '/admin/promotions/attachments'
      );
    }

    const {
      title_en, title_tw,
      description_en, description_tw,
      attachment_name_en, attachment_name_tw,
      groupId
    } = req.body;

    const updateData = {
      title_en,
      title_tw,
      description_en,
      description_tw,
      attachment_name_en,
      attachment_name_tw,
      groupId: parseInt(groupId),
      updatedAt: new Date()
    };

    // If new file uploaded, update file info
    if (req.file) {
      const { filename, originalname, mimetype, size } = req.file;
      const decodedOriginalName = Buffer.from(originalname, 'latin1').toString('utf8');
      
      updateData.filename = filename;
      updateData.originalName = decodedOriginalName;
      updateData.mimeType = mimetype;
      updateData.size = size;
      updateData.path = req.file.relativePath;
    }

    await prisma.promotionAttachment.update({
      where: { id },
      data: updateData
    });

    req.flash('success_msg', '推動方案附件已成功更新');
    return res.redirect('/admin/promotions/attachments');
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.ATTACHMENT_UPDATE_FAILED || 'PA008',
      '更新推動方案附件失敗',
      '/admin/promotions/attachments'
    );
  }
};

// Delete an attachment
exports.deleteAttachment = async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return handleControllerError(
        new Error('Invalid attachment ID'),
        req,
        res,
        PROMOTION_ERROR_CODES?.INVALID_ATTACHMENT_ID || 'PA009',
        '無效的附件 ID',
        '/admin/promotions/attachments'
      );
    }

    await prisma.promotionAttachment.update({
      where: { id },
      data: { deletedAt: new Date() }
    });

    req.flash('success_msg', '推動方案附件已成功刪除');
    return res.redirect('/admin/promotions/attachments');
  } catch (error) {
    handleControllerError(
      error,
      req,
      res,
      PROMOTION_ERROR_CODES?.ATTACHMENT_DELETE_FAILED || 'PA010',
      '刪除推動方案附件失敗',
      '/admin/promotions/attachments'
    );
  }
};

// Download attachment
exports.downloadAttachment = async (req, res) => {
  try {
    const id = parseInt(req.params.id, 10);

    if (isNaN(id)) {
      return res.status(400).send('Invalid attachment ID');
    }

    const attachment = await prisma.promotionAttachment.findUnique({
      where: { id }
    });

    if (!attachment) {
      return res.status(404).send('Attachment not found');
    }

    const filePath = path.join(process.cwd(), 'public', attachment.path);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).send('File not found');
    }

    res.download(filePath, attachment.originalName);
  } catch (error) {
    logger.error('Error downloading attachment:', error);
    res.status(500).send('Error downloading file');
  }
};
