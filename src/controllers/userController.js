const bcrypt = require('bcryptjs');
const logger = require('../config/logger');
const prisma = require('../lib/prisma');
const { USER_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

exports.listUsers = async (req, res) => {
    try {
        let users;
        try {
            users = await prisma.user.findMany({
                include: {
                    role: true,
                    project: true
                },
                orderBy: {
                    createdAt: 'desc'
                }
            });
        } catch (e) {
            // If the project relation doesn't exist yet, fall back to just role
            logger.error(`${USER_ERROR_CODES.PROJECT_ERROR}: Error including project relation (table may not exist yet):`, e);
            users = await prisma.user.findMany({
                include: {
                    role: true
                },
                orderBy: {
                    createdAt: 'desc'
                }
            });
        }

        // Filter users based on current user's role
        if (req.session.user.role === 'admin') {
            // Admin can only see admin and editor roles
            users = users.filter(user => user.role.name !== 'super_admin');
        } else if (req.session.user.role === 'editor') {
            // Editor can only see themselves
            users = users.filter(user => user.id === req.session.user.id);
        }

        res.render('admin/users/list', {
            title: 'Users',
            layout: 'layouts/admin',
            users
        });
    } catch (error) {
        handleControllerError(
            error, 
            req, 
            res, 
            USER_ERROR_CODES.LIST_FAILED, 
            '載入使用者失敗', 
            '/admin/dashboard'
        );
    }
};

exports.renderCreateUser = async (req, res) => {
    try {
        // Get roles
        let roles = await prisma.role.findMany();
        
        // Filter roles based on user's own role
        if (req.session.user.role !== 'super_admin') {
            // Hide super_admin role for non-super_admin users
            roles = roles.filter(role => role.name !== 'super_admin');
            
            // Editor can't create users
            if (req.session.user.role === 'editor') {
                req.flash('error_msg', `您沒有權限建立使用者 [${USER_ERROR_CODES.UNAUTHORIZED_ROLE_CREATE}]`);
                return res.redirect('/admin/users');
            }
        }
        
        // Default projects in case the table doesn't exist yet
        let projects = [];
        try {
            projects = await prisma.project.findMany({
                where: { isActive: true },
                orderBy: { name_zh: 'asc' }
            });
        } catch (e) {
            logger.error(`${USER_ERROR_CODES.PROJECT_ERROR}: Error fetching projects (table may not exist yet):`, e);
            // Provide default projects in case the table doesn't exist
            projects = [
                { id: 1, name_zh: '台灣智慧系統整合製造平台' },
                { id: 2, name_zh: '智慧雨林產業創生' }
            ];
        }
        
        res.render('admin/users/create', {
            title: 'Create User',
            layout: 'layouts/admin',
            roles,
            projects
        });
    } catch (error) {
        logger.error(`${USER_ERROR_CODES.CREATE_FORM_FAILED}: Error rendering create user form:`, error);
        req.flash('error_msg', `載入角色失敗 [${USER_ERROR_CODES.CREATE_FORM_FAILED}]`);
        res.redirect('/admin/users');
    }
};

// Helper function to ensure default projects exist
async function ensureDefaultProjects() {
    try {
        // Check if project 1 exists
        const project1 = await prisma.project.findUnique({
            where: { id: 1 }
        });
        
        // If not, create it
        if (!project1) {
            await prisma.project.create({
                data: {
                    id: 1,
                    name_zh: '台灣智慧系統整合製造平台',
                    name_en: 'Taiwan Smart Manufacturing Platform',
                    isActive: true
                }
            });
            logger.info('Created default project ID 1');
        }
        
        // Check if project 2 exists
        const project2 = await prisma.project.findUnique({
            where: { id: 2 }
        });
        
        // If not, create it
        if (!project2) {
            await prisma.project.create({
                data: {
                    id: 2,
                    name_zh: '智慧雨林產業創生',
                    name_en: 'Smart Rainforest Industry',
                    isActive: true
                }
            });
            logger.info('Created default project ID 2');
        }
        
        return true;
    } catch (error) {
        logger.error(`${USER_ERROR_CODES.PROJECT_ERROR}: Error ensuring default projects:`, error);
        return false;
    }
}

exports.createUser = async (req, res) => {
    try {
        const { 
            username, 
            email, 
            password, 
            roleId, 
            isActive,
            contactName_zh,
            contactName_en,
            contactPhone,
            organizationName_zh,
            organizationName_en,
            businessId,
            projectId
        } = req.body;

        logger.info('Attempting to create user with fields:', { 
            username, 
            email, 
            roleId,
            hasPassword: !!password,
            isActive: !!isActive,
            hasContactDetails: !!(contactName_zh || contactName_en || contactPhone),
            hasOrgDetails: !!(organizationName_zh || organizationName_en || businessId),
            projectId: projectId || null
        });

        // Check if user already exists
        const existingUser = await prisma.user.findFirst({
            where: {
                OR: [
                    { username },
                    { email }
                ]
            }
        });

        if (existingUser) {
            logger.warn(`${USER_ERROR_CODES.USER_EXISTS}: User creation failed: Username or email already exists`, { username, email });
            req.flash('error_msg', `使用者名稱或電子郵件已存在 [${USER_ERROR_CODES.USER_EXISTS}]`);
            return res.redirect('/admin/users/new');
        }

        // Get role details to validate
        const role = await prisma.role.findUnique({
            where: { id: parseInt(roleId) }
        });

        if (!role) {
            logger.warn(`${USER_ERROR_CODES.INVALID_ROLE}: User creation failed: Invalid role selection`, { roleId });
            req.flash('error_msg', `無效的角色選擇 [${USER_ERROR_CODES.INVALID_ROLE}]`);
            return res.redirect('/admin/users/new');
        }

        // Prevent creating super_admin if current user is not super_admin
        if (role.name === 'super_admin' && req.session.user.role !== 'super_admin') {
            logger.warn(`${USER_ERROR_CODES.UNAUTHORIZED_ROLE_CREATE}: User creation failed: Unauthorized attempt to create super_admin`, { 
                attemptedBy: req.session.user.username, 
                userRole: req.session.user.role 
            });
            req.flash('error_msg', `您沒有權限建立超級管理員 [${USER_ERROR_CODES.UNAUTHORIZED_ROLE_CREATE}]`);
            return res.redirect('/admin/users/new');
        }

        // Handle project ID
        let validatedProjectId = null;
        if (projectId && projectId !== '') {
            const projectIdNum = parseInt(projectId);
            
            // Special handling for hardcoded project IDs (1 and 2)
            if (projectIdNum === 1 || projectIdNum === 2) {
                // Ensure these default projects exist in database
                await ensureDefaultProjects();
                validatedProjectId = projectIdNum;
            } else {
                // For other project IDs, validate as usual
                try {
                    const project = await prisma.project.findUnique({
                        where: { id: projectIdNum }
                    });
                    
                    if (project) {
                        validatedProjectId = projectIdNum;
                    } else {
                        logger.warn(`${USER_ERROR_CODES.PROJECT_ERROR}: Project ID not found in database:`, { projectId });
                    }
                } catch (error) {
                    logger.warn(`${USER_ERROR_CODES.PROJECT_ERROR}: Error validating project ID:`, { projectId, error: error.message });
                }
            }
        }

        // Hash password
        const hashedPassword = await bcrypt.hash(password, 10);

        // Create user with all available fields
        const userData = {
            username,
            email,
            password: hashedPassword,
            roleId: parseInt(roleId),
            isActive: isActive === 'on' || isActive === true,
            contactName_zh: contactName_zh || null,
            contactName_en: contactName_en || null,
            contactPhone: contactPhone || null,
            organizationName_zh: organizationName_zh || null,
            organizationName_en: organizationName_en || null,
            businessId: businessId || null
        };
        
        // Add project ID if we have a valid one
        if (validatedProjectId) {
            userData.projectId = validatedProjectId;
        }

        logger.info('Creating user with data:', { 
            ...userData, 
            password: '[REDACTED]' 
        });

        // Create user
        const newUser = await prisma.user.create({
            data: userData
        });

        logger.info('使用者已成功建立', { id: newUser.id, username });
        req.flash('success_msg', '使用者已成功建立');
        res.redirect('/admin/users');
    } catch (error) {
        logger.error(`${USER_ERROR_CODES.CREATE_FAILED}: 使用者創建失敗`, { 
            error: error.message, 
            stack: error.stack,
            requestBody: req.body 
        });
        req.flash('error_msg', `建立使用者失敗 [${USER_ERROR_CODES.CREATE_FAILED}]`);
        res.redirect('/admin/users/new');
    }
};

exports.renderEditUser = async (req, res) => {
    try {
        const { id } = req.params;

        // Get all data needed for the form
        const [
            user,
            allRoles,
            categories,
            categoryPermissions
        ] = await Promise.all([
            prisma.user.findUnique({
                where: { id: parseInt(id) },
                include: { role: true }
            }),
            prisma.role.findMany({
                orderBy: { name: 'asc' }
            }),
            prisma.category.findMany({
                where: { deletedAt: null },
                orderBy: { name_en: 'asc' }
            }),
            prisma.categoryPermission.findMany({
                where: { userId: parseInt(id) },
                include: { category: true }
            })
        ]);

        if (!user) {
            req.flash('error_msg', `找不到使用者 [${USER_ERROR_CODES.NOT_FOUND}]`);
            return res.redirect('/admin/users');
        }

        // Check permissions based on roles
        const currentUserRole = req.session.user.role;
        const targetUserRole = user.role.name;

        // Editors can only edit themselves
        if (currentUserRole === 'editor' && req.session.user.id !== parseInt(id)) {
            req.flash('error_msg', `您只能編輯自己的資料 [${USER_ERROR_CODES.SELF_EDIT_ONLY}]`);
            return res.redirect('/admin/users');
        }

        // Admin cannot edit super_admin
        if (currentUserRole === 'admin' && targetUserRole === 'super_admin') {
            req.flash('error_msg', `您沒有權限編輯超級管理員 [${USER_ERROR_CODES.ADMIN_CANNOT_EDIT_SUPER}]`);
            return res.redirect('/admin/users');
        }

        // Filter roles based on user permissions
        let roles = allRoles;
        if (currentUserRole !== 'super_admin') {
            // Hide super_admin role
            roles = roles.filter(role => role.name !== 'super_admin');
        }

        // For editor users, they shouldn't be able to change their role
        const canChangeRole = !(currentUserRole === 'editor');

        // Default projects in case the table doesn't exist yet
        let projects = [];
        try {
            projects = await prisma.project.findMany({
                where: { isActive: true },
                orderBy: { name_zh: 'asc' }
            });
        } catch (e) {
            logger.error(`${USER_ERROR_CODES.PROJECT_ERROR}: Error fetching projects (table may not exist yet):`, e);
            // Provide default projects in case the table doesn't exist
            projects = [
                { id: 1, name_zh: '台灣智慧系統整合製造平台' },
                { id: 2, name_zh: '智慧雨林產業創生' }
            ];
        }

        // Determine if delete button should be shown based on permissions
        const canDelete = (
            // Not viewing self
            req.session.user.id !== parseInt(id) &&
            // super_admin can delete anyone except last super_admin
            (currentUserRole === 'super_admin' || 
            // admin can only delete editors
            (currentUserRole === 'admin' && targetUserRole === 'editor')) &&
            // guests cannot delete accounts
            currentUserRole !== 'guest'
        );

        res.render('admin/users/edit', {
            title: 'Edit User',
            layout: 'layouts/admin',
            user: req.session.user,
            editUser: user,
            roles,
            categories,
            categoryPermissions,
            projects,
            canChangeRole,
            canDelete,
            currentUser: req.session.user,
            success_msg: req.flash('success_msg'),
            error_msg: req.flash('error_msg')
        });
    } catch (error) {
        logger.error(`${USER_ERROR_CODES.LOAD_FAILED}: Error rendering edit user form:`, error);
        req.flash('error_msg', `載入使用者資料失敗 [${USER_ERROR_CODES.LOAD_FAILED}]`);
        res.redirect('/admin/users');
    }
};

exports.updateUser = async (req, res) => {
    try {
        const { id } = req.params;
        
        // Create a copy of req.body and remove any layout parameter
        const formData = {...req.body};
        if (formData._layout) {
            delete formData._layout;
        }
        
        const { 
            username, 
            email, 
            password, 
            roleId, 
            isActive,
            contactName_zh,
            contactName_en,
            contactPhone,
            organizationName_zh,
            organizationName_en,
            businessId,
            projectId
        } = formData;

        // Get current user data
        const currentUser = await prisma.user.findUnique({
            where: { id: parseInt(id) },
            include: { role: true }
        });

        if (!currentUser) {
            req.flash('error_msg', `找不到使用者 [${USER_ERROR_CODES.NOT_FOUND}]`);
            return res.redirect('/admin/users');
        }

        // Check permissions based on roles
        const currentUserRole = req.session.user.role;
        const targetUserRole = currentUser.role.name;

        // Editors can only edit themselves
        if (currentUserRole === 'editor' && req.session.user.id !== parseInt(id)) {
            req.flash('error_msg', `您只能編輯自己的資料 [${USER_ERROR_CODES.SELF_EDIT_ONLY}]`);
            return res.redirect('/admin/users');
        }

        // Admin cannot edit super_admin
        if (currentUserRole === 'admin' && targetUserRole === 'super_admin') {
            req.flash('error_msg', `您沒有權限編輯超級管理員 [${USER_ERROR_CODES.ADMIN_CANNOT_EDIT_SUPER}]`);
            return res.redirect('/admin/users');
        }

        // Use current roleId if not provided in the form or if editor trying to change role
        let roleIdToUse;
        if (currentUserRole === 'editor' || currentUserRole === 'guest') {
            // Editors and Guests cannot change their role
            roleIdToUse = currentUser.roleId;
        } else {
            roleIdToUse = roleId ? parseInt(roleId) : currentUser.roleId;
        }

        // Get new role details
        const newRole = await prisma.role.findUnique({
            where: { id: roleIdToUse }
        });

        if (!newRole) {
            req.flash('error_msg', `無效的角色選擇 [${USER_ERROR_CODES.INVALID_ROLE}]`);
            return res.redirect(`/admin/users/edit/${id}`);
        }

        // Special checks for role changes
        if (newRole.name === 'super_admin' && currentUserRole !== 'super_admin') {
            req.flash('error_msg', `您沒有權限將使用者升級為超級管理員 [${USER_ERROR_CODES.UNAUTHORIZED_ROLE_CREATE}]`);
            return res.redirect(`/admin/users/edit/${id}`);
        }

        // Prepare update data
        const updateData = {
            username,
            email,
            roleId: roleIdToUse,
            contactName_zh,
            contactName_en,
            contactPhone,
            organizationName_zh,
            organizationName_en,
            businessId,
        };

        // Handle projectId with validation
        if (projectId) {
            // Check if the project exists
            const projectExists = await prisma.project.findUnique({
                where: { id: parseInt(projectId) }
            });

            if (projectExists) {
                updateData.projectId = parseInt(projectId);
            } else {
                // Project doesn't exist, don't set the projectId
                updateData.projectId = null;
                logger.warn(`${USER_ERROR_CODES.PROJECT_ERROR}: Attempted to set non-existent projectId: ${projectId}`);
            }
        } else {
            updateData.projectId = null;
        }

        // Only super_admin and admin can change active status
        if (currentUserRole === 'super_admin' || currentUserRole === 'admin') {
            updateData.isActive = isActive === 'on' || isActive === true;
        } else if (currentUserRole === 'editor' && parseInt(id) === req.session.user.id) {
            // Editors cannot disable their own account
            updateData.isActive = true;
        } else {
            // For any other user types
            updateData.isActive = isActive === 'on' || isActive === true;
        }

        // Only update password if provided
        if (password) {
            updateData.password = await bcrypt.hash(password, 10);
        }

        // Update user
        await prisma.user.update({
            where: { id: parseInt(id) },
            data: updateData
        });

        // req.flash('success_msg', '使用者已成功更新');
        
        // Different redirect based on user role
        if (currentUserRole === 'editor' || currentUserRole === 'guest') {
            req.flash('success_msg', '使用者已成功更新');
            
            // For editors and guests, do a full page render with correct layout instead of redirect
            return exports.renderEditUser(req, res);
        } else {
            return res.redirect('/admin/users');
        }
    } catch (error) {
        logger.error(`${USER_ERROR_CODES.UPDATE_FAILED}: Error updating user:`, error);
        req.flash('error_msg', `更新使用者失敗 [${USER_ERROR_CODES.UPDATE_FAILED}]`);
        res.redirect(`/admin/users/edit/${req.params.id}`);
    }
};

exports.deleteUser = async (req, res) => {
    try {
        const { id } = req.params;
        const userId = parseInt(id);

        // Check if user exists
        const user = await prisma.user.findUnique({
            where: { id: userId },
            include: { role: true }
        });

        if (!user) {
            req.flash('error_msg', `找不到使用者 [${USER_ERROR_CODES.NOT_FOUND}]`);
            return res.redirect('/admin/users');
        }

        // Check permissions based on roles
        const currentUserRole = req.session.user.role;
        const targetUserRole = user.role.name;
        const isSelf = req.session.user.id === userId;

        // Users cannot delete themselves
        if (isSelf) {
            req.flash('error_msg', `使用者不能刪除自己的帳號 [${USER_ERROR_CODES.SELF_DELETE}]`);
            return res.redirect('/admin/users');
        }

        // Editors cannot delete users
        if (currentUserRole === 'editor') {
            req.flash('error_msg', `您沒有權限刪除使用者 [${USER_ERROR_CODES.DELETE_PERMISSION}]`);
            return res.redirect('/admin/users');
        }

        // Admin can only delete editors, cannot delete other admins
        if (currentUserRole === 'admin') {
            if (targetUserRole === 'super_admin' || targetUserRole === 'admin') {
                req.flash('error_msg', `您沒有權限刪除管理員或超級管理員 [${USER_ERROR_CODES.ADMIN_DELETE_RESTRICTION}]`);
                return res.redirect('/admin/users');
            }
        }

        // Only super_admin can delete admin users
        if (targetUserRole === 'admin' && currentUserRole !== 'super_admin') {
            req.flash('error_msg', `只有超級管理員可以刪除管理員用戶 [${USER_ERROR_CODES.SUPER_ADMIN_ONLY}]`);
            return res.redirect('/admin/users');
        }

        // Don't allow deleting the last super admin
        if (targetUserRole === 'super_admin') {
            const superAdminCount = await prisma.user.count({
                where: {
                    role: {
                        name: 'super_admin'
                    }
                }
            });

            if (superAdminCount <= 1) {
                req.flash('error_msg', `無法刪除最後一位超級管理員 [${USER_ERROR_CODES.LAST_SUPER_ADMIN}]`);
                return res.redirect('/admin/users');
            }
        }

        // Delete related category permissions first
        await prisma.categoryPermission.deleteMany({
            where: { userId }
        });

        // Delete user
        await prisma.user.delete({
            where: { id: userId }
        });

        req.flash('success_msg', '使用者已成功刪除');
        res.redirect('/admin/users');
    } catch (error) {
        logger.error(`${USER_ERROR_CODES.DELETE_FAILED}: Error deleting user:`, error);
        req.flash('error_msg', `刪除使用者失敗 [${USER_ERROR_CODES.DELETE_FAILED}]`);
        res.redirect('/admin/users');
    }
};
