const prisma = require('../lib/prisma');
const logger = require('../config/logger');

// List all promotion categories
exports.listCategories = async (req, res) => {
    try {
        const categories = await prisma.promotionCategory.findMany({
            where: {
                deletedAt: null
            },
            orderBy: {
                order: 'asc'
            },
            include: {
                _count: {
                    select: {
                        groups: {
                            where: {
                                deletedAt: null
                            }
                        }
                    }
                }
            }
        });

        res.render('admin/promotions/categories/list', {
            title: '推動方案分類管理',
            categories
        });
    } catch (error) {
        logger.error('Error listing promotion categories:', error);
        req.flash('error_msg', `載入推動方案分類失敗：${error.message}`);
        res.redirect('/admin/dashboard');
    }
};

// Render create category form
exports.renderCreateCategory = async (req, res) => {
    try {
        res.render('admin/promotions/categories/create', {
            title: '新增推動方案分類'
        });
    } catch (error) {
        logger.error('Error rendering create category form:', error);
        req.flash('error_msg', `載入新增分類頁面失敗：${error.message}`);
        res.redirect('/admin/promotions/categories');
    }
};

// Create a new category
exports.createCategory = async (req, res) => {
    try {
        const { name_en, name_tw, description_en, description_tw, order } = req.body;

        // Validation
        if (!name_en || !name_tw) {
            req.flash('error_msg', '分類名稱（中英文）為必填項目');
            return res.redirect('/admin/promotions/categories/create');
        }

        await prisma.promotionCategory.create({
            data: {
                name_en,
                name_tw,
                description_en: description_en || null,
                description_tw: description_tw || null,
                order: order ? parseInt(order) : 0
            }
        });

        req.flash('success_msg', '推動方案分類已成功建立');
        res.redirect('/admin/promotions/categories');
    } catch (error) {
        logger.error('Error creating promotion category:', error);
        req.flash('error_msg', `建立推動方案分類失敗：${error.message}`);
        res.redirect('/admin/promotions/categories/create');
    }
};

// Render edit category form
exports.renderEditCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const parsedId = parseInt(id);

        if (isNaN(parsedId)) {
            req.flash('error_msg', '無效的分類 ID');
            return res.redirect('/admin/promotions/categories');
        }

        const category = await prisma.promotionCategory.findFirst({
            where: {
                id: parsedId,
                deletedAt: null
            }
        });

        if (!category) {
            req.flash('error_msg', '找不到推動方案分類');
            return res.redirect('/admin/promotions/categories');
        }

        res.render('admin/promotions/categories/edit', {
            title: '編輯推動方案分類',
            category
        });
    } catch (error) {
        logger.error('Error rendering edit category form:', error);
        req.flash('error_msg', `載入編輯分類頁面失敗：${error.message}`);
        res.redirect('/admin/promotions/categories');
    }
};

// Update a category
exports.updateCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const { name_en, name_tw, description_en, description_tw, order } = req.body;
        const parsedId = parseInt(id);

        if (isNaN(parsedId)) {
            req.flash('error_msg', '無效的分類 ID');
            return res.redirect('/admin/promotions/categories');
        }

        // Validation
        if (!name_en || !name_tw) {
            req.flash('error_msg', '分類名稱（中英文）為必填項目');
            return res.redirect(`/admin/promotions/categories/edit/${id}`);
        }

        const existingCategory = await prisma.promotionCategory.findFirst({
            where: {
                id: parsedId,
                deletedAt: null
            }
        });

        if (!existingCategory) {
            req.flash('error_msg', '找不到推動方案分類');
            return res.redirect('/admin/promotions/categories');
        }

        await prisma.promotionCategory.update({
            where: { id: parsedId },
            data: {
                name_en,
                name_tw,
                description_en: description_en || null,
                description_tw: description_tw || null,
                order: order ? parseInt(order) : 0,
                updatedAt: new Date()
            }
        });

        req.flash('success_msg', '推動方案分類已成功更新');
        res.redirect('/admin/promotions/categories');
    } catch (error) {
        logger.error('Error updating promotion category:', error);
        req.flash('error_msg', `更新推動方案分類失敗：${error.message}`);
        res.redirect(`/admin/promotions/categories/edit/${req.params.id}`);
    }
};

// Delete a category
exports.deleteCategory = async (req, res) => {
    try {
        const { id } = req.params;
        const parsedId = parseInt(id);

        if (isNaN(parsedId)) {
            req.flash('error_msg', '無效的分類 ID');
            return res.redirect('/admin/promotions/categories');
        }

        // Check if category has groups
        const category = await prisma.promotionCategory.findFirst({
            where: {
                id: parsedId,
                deletedAt: null
            },
            include: {
                groups: {
                    where: {
                        deletedAt: null
                    }
                }
            }
        });

        if (!category) {
            req.flash('error_msg', '找不到推動方案分類');
            return res.redirect('/admin/promotions/categories');
        }

        if (category.groups.length > 0) {
            req.flash('error_msg', '無法刪除包含分組的分類');
            return res.redirect('/admin/promotions/categories');
        }

        // Soft delete the category
        await prisma.promotionCategory.update({
            where: { id: parsedId },
            data: {
                deletedAt: new Date()
            }
        });

        req.flash('success_msg', '推動方案分類已成功刪除');
        res.redirect('/admin/promotions/categories');
    } catch (error) {
        logger.error('Error deleting promotion category:', error);
        req.flash('error_msg', `刪除推動方案分類失敗：${error.message}`);
        res.redirect('/admin/promotions/categories');
    }
};
