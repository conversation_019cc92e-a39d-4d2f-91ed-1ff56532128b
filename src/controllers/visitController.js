const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const { VISIT_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError } = require('../utils/errorHandler');

// List all visits
exports.listVisits = async (req, res) => {
    try {
        // Extract query parameters
        const search = req.query.search || '';
        const status = req.query.status || 'all';
        const mouStatus = req.query.mouStatus || 'all';
        const projectId = req.query.project || 'all';

        // Build the where clause for filtering
        let whereClause = {};

        // Search by company name
        if (search) {
            whereClause.companyName = {
                contains: search
            };
        }

        // Filter by status
        if (status !== 'all') {
            whereClause.status = status;
        }

        // Filter by MOU status
        if (mouStatus !== 'all') {
            whereClause.mouStatus = parseInt(mouStatus);
        }

        // Filter by project
        if (projectId !== 'all') {
            whereClause.projectId = parseInt(projectId);
        }

        // Get all active projects for filtering
        const projects = await prisma.project.findMany({
            where: {
                isActive: true
            },
            orderBy: {
                name_zh: 'asc'
            }
        });

        // For all users (including guests), show filtered visits
        const visits = await prisma.visit.findMany({
            where: whereClause,
            include: {
                project: true
            },
            orderBy: {
                createdAt: 'desc'
            }
        });

        res.render('admin/visits/index', {
            title: '訪廠資訊管理',
            visits,
            projects,
            search,
            status,
            mouStatus,
            selectedProject: projectId,
            success_msg: req.flash('success_msg'),
            error_msg: req.flash('error_msg')
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            VISIT_ERROR_CODES.LIST_FAILED,
            '列出訪廠紀錄時發生錯誤',
            '/admin/dashboard'
        );
    }
};

// Render create form
exports.renderCreateForm = async (req, res) => {
    try {
        // Get the projects for the dropdown
        const projects = await prisma.project.findMany({
            where: {
                isActive: true
            },
            orderBy: {
                name_zh: 'asc'
            }
        });

        // If user is a guest, get their project name and company info
        let projectName = '';
        let companyInfo = null;

        if (req.session.user.role === 'guest') {
            const userDetails = await prisma.user.findUnique({
                where: {
                    id: req.session.user.id
                },
                include: {
                    project: true
                }
            });

            if (userDetails) {
                if (userDetails.projectId && userDetails.project) {
                    projectName = userDetails.project.name_zh;
                }

                // Prepare company info for auto-population (without contact name)
                companyInfo = {
                    businessId: userDetails.businessId || '',
                    companyName: userDetails.organizationName_zh || '',
                    contactPhone: userDetails.contactPhone || ''
                };
            }
        }

        res.render('admin/visits/create', {
            title: '新增訪廠紀錄',
            projects,
            projectName,
            companyInfo,
            user: req.session.user
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            VISIT_ERROR_CODES.CREATE_FAILED,
            '顯示建立表單時發生錯誤',
            '/admin/visits'
        );
    }
};

// Create a new visit
exports.createVisit = async (req, res) => {
    try {
        console.log('Create Visit - Full request body:', JSON.stringify(req.body, null, 2));

        const {
            businessId, companyName, contactPerson, address, visitDate, visitTime,
            attendees, notes, projectId, status, mouStatus, projectManager, projectManagerPhone
        } = req.body;

        // Validate visit date
        const visitDateObj = new Date(visitDate);
        if (isNaN(visitDateObj.getTime())) {
            logger.error(`${VISIT_ERROR_CODES.DATE_INVALID}: Invalid visit date: ${visitDate}`);
            req.flash('error_msg', `參訪日期無效 [${VISIT_ERROR_CODES.DATE_INVALID}]`);
            return res.redirect('/admin/visits/create');
        }

        // Allow all users to set status and mouStatus
        const visitStatus = status || '待安排';

        // Convert mouStatus to integer (0 = '未簽約'/'未用印', 1 = '已簽約'/'已用印')
        let visitMouStatus = 0; // Default to 0 (未用印)
        if (mouStatus) {
            // Handle both string and number inputs
            if (mouStatus === '1' || mouStatus === 1 || mouStatus === '已簽約' || mouStatus === '已用印') {
                visitMouStatus = 1;
            }
        }

        // Create the visit record
        await prisma.visit.create({
            data: {
                businessId,
                companyName,
                contactPerson,
                address,
                visitDate: visitDateObj,
                visitTime,
                attendees,
                notes,
                projectManager,
                projectManagerPhone,
                status: visitStatus,
                createdById: req.session.user.id,
                projectId: projectId ? parseInt(projectId) : null,
                mouStatus: visitMouStatus
            }
        });

        req.flash('success_msg', '訪廠紀錄已成功建立');
        res.redirect('/admin/visits');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            VISIT_ERROR_CODES.CREATE_FAILED,
            '建立訪廠紀錄時發生錯誤',
            '/admin/visits/create',
            { requestBody: req.body }
        );
    }
};

// View a visit
exports.viewVisit = async (req, res) => {
    try {
        const visit = await prisma.visit.findUnique({
            where: {
                id: parseInt(req.params.id)
            },
            include: {
                project: true,
                createdBy: true
            }
        });

        if (!visit) {
            req.flash('error_msg', `找不到該訪廠紀錄 [${VISIT_ERROR_CODES.NOT_FOUND}]`);
            return res.redirect('/admin/visits');
        }

        res.render('admin/visits/view', {
            title: '查看訪廠紀錄',
            visit
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            VISIT_ERROR_CODES.VIEW_FAILED,
            '查看訪廠紀錄時發生錯誤',
            '/admin/visits',
            { visitId: req.params.id }
        );
    }
};

// Render edit form
exports.renderEditForm = async (req, res) => {
    try {
        const visit = await prisma.visit.findUnique({
            where: {
                id: parseInt(req.params.id)
            },
            include: {
                project: true
            }
        });

        if (!visit) {
            req.flash('error_msg', `找不到該訪廠紀錄 [${VISIT_ERROR_CODES.NOT_FOUND}]`);
            return res.redirect('/admin/visits');
        }

        // Check if user has permission to edit this visit
        if (req.session.user.role === 'guest' && visit.createdById !== req.session.user.id) {
            req.flash('error_msg', `您沒有權限編輯此訪廠紀錄 [${VISIT_ERROR_CODES.PERMISSION_ERROR}]`);
            return res.redirect('/admin/visits');
        }

        // Get the projects for the dropdown
        const projects = await prisma.project.findMany({
            where: {
                isActive: true
            },
            orderBy: {
                name_zh: 'asc'
            }
        });

        // If user is a guest, get their project name
        let projectName = '';
        let companyInfo = null;

        if (req.session.user.role === 'guest') {
            const userDetails = await prisma.user.findUnique({
                where: {
                    id: req.session.user.id
                },
                include: {
                    project: true
                }
            });

            if (userDetails) {
                if (userDetails.projectId && userDetails.project) {
                    projectName = userDetails.project.name_zh;
                }

                // Company info is already in the visit record, but we prepare it
                // in case some fields need to be populated from user profile
                companyInfo = {
                    businessId: visit.businessId || userDetails.businessId || '',
                    companyName: visit.companyName || userDetails.organizationName_zh || '',
                    contactPhone: userDetails.contactPhone || ''
                };
            }
        }

        res.render('admin/visits/edit', {
            title: '編輯訪廠紀錄',
            visit,
            projects,
            projectName,
            companyInfo
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            VISIT_ERROR_CODES.LOAD_FAILED,
            '顯示編輯表單時發生錯誤',
            '/admin/visits',
            { visitId: req.params.id }
        );
    }
};

// Update a visit
exports.updateVisit = async (req, res) => {
    try {
        // Log the entire request body for debugging
        console.log('Update Visit - Full request body:', JSON.stringify(req.body, null, 2));

        const {
            businessId, companyName, contactPerson, address, visitDate, visitTime,
            attendees, notes, projectId, status, mouStatus, direct_status, direct_mouStatus,
            projectManager, projectManagerPhone
        } = req.body;

        // Log received data for debugging
        console.log('Update Visit - Extracted data:', {
            status,
            mouStatus,
            direct_status,
            direct_mouStatus,
            userRole: req.session.user.role
        });

        const visitId = parseInt(req.params.id);

        // Get the current visit
        const currentVisit = await prisma.visit.findUnique({
            where: { id: visitId }
        });

        if (!currentVisit) {
            req.flash('error_msg', `找不到該訪廠紀錄 [${VISIT_ERROR_CODES.NOT_FOUND}]`);
            return res.redirect('/admin/visits');
        }

        // Check if user has permission to edit this visit
        if (req.session.user.role === 'guest' && currentVisit.createdById !== req.session.user.id) {
            req.flash('error_msg', `您沒有權限編輯此訪廠紀錄 [${VISIT_ERROR_CODES.PERMISSION_ERROR}]`);
            return res.redirect('/admin/visits');
        }

        // Editors can only edit their own records
        if (req.session.user.role === 'editor' && currentVisit.createdById !== req.session.user.id) {
            req.flash('error_msg', `編輯者只能編輯自己建立的紀錄 [${VISIT_ERROR_CODES.PERMISSION_ERROR}]`);
            return res.redirect('/admin/visits');
        }

        // Validate visit date
        const visitDateObj = new Date(visitDate);
        if (isNaN(visitDateObj.getTime())) {
            logger.error(`${VISIT_ERROR_CODES.DATE_INVALID}: Invalid visit date: ${visitDate}`);
            req.flash('error_msg', `參訪日期無效 [${VISIT_ERROR_CODES.DATE_INVALID}]`);
            return res.redirect(`/admin/visits/edit/${visitId}`);
        }

        // Update fields based on user role
        const updateData = {
            businessId,
            companyName,
            contactPerson,
            address,
            visitDate: visitDateObj,
            visitTime,
            attendees,
            notes,
            projectManager,
            projectManagerPhone,
            projectId: projectId ? parseInt(projectId) : null
        };

        // Handle status update
        updateData.status = status || direct_status || currentVisit.status || '待安排';

        // Convert mouStatus to integer (0 = '未簽約'/'未用印', 1 = '已簽約'/'已用印')
        let visitMouStatus = currentVisit.mouStatus || 0;

        // Try to get mouStatus from either mouStatus or direct_mouStatus
        const mouStatusValue = mouStatus || direct_mouStatus;

        if (mouStatusValue) {
            // Handle both string and number inputs
            if (mouStatusValue === '1' || mouStatusValue === 1 ||
                mouStatusValue === '已簽約' || mouStatusValue === '已用印') {
                visitMouStatus = 1;
            } else if (mouStatusValue === '0' || mouStatusValue === 0 ||
                mouStatusValue === '未簽約' || mouStatusValue === '未用印') {
                visitMouStatus = 0;
            }
        }

        updateData.mouStatus = visitMouStatus;

        console.log('Final update data:', updateData);

        // Update the visit
        await prisma.visit.update({
            where: { id: visitId },
            data: updateData
        });

        req.flash('success_msg', '訪廠紀錄已成功更新');
        res.redirect('/admin/visits');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            VISIT_ERROR_CODES.UPDATE_FAILED,
            '更新訪廠紀錄時發生錯誤',
            `/admin/visits/edit/${req.params.id}`,
            { visitId: req.params.id, requestBody: req.body }
        );
    }
};

// Delete a visit
exports.deleteVisit = async (req, res) => {
    try {
        const visitId = parseInt(req.params.id);

        // Check if visit exists
        const visit = await prisma.visit.findUnique({
            where: { id: visitId }
        });

        if (!visit) {
            req.flash('error_msg', `找不到該訪廠紀錄 [${VISIT_ERROR_CODES.NOT_FOUND}]`);
            return res.redirect('/admin/visits');
        }

        // Only admins can delete visits
        if (req.session.user.role !== 'super_admin' && req.session.user.role !== 'admin') {
            // Editors can only delete their own records
            if (req.session.user.role === 'editor' && visit.createdById !== req.session.user.id) {
                req.flash('error_msg', `編輯者只能刪除自己建立的紀錄 [${VISIT_ERROR_CODES.PERMISSION_ERROR}]`);
                return res.redirect('/admin/visits');
            } else if (req.session.user.role !== 'editor') {
                req.flash('error_msg', `您沒有權限刪除訪廠紀錄 [${VISIT_ERROR_CODES.PERMISSION_ERROR}]`);
                return res.redirect('/admin/visits');
            }
        }

        // Delete the visit
        await prisma.visit.delete({
            where: { id: visitId }
        });

        req.flash('success_msg', '訪廠紀錄已成功刪除');
        res.redirect('/admin/visits');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            VISIT_ERROR_CODES.DELETE_FAILED,
            '刪除訪廠紀錄時發生錯誤',
            '/admin/visits',
            { visitId: req.params.id }
        );
    }
};

// Render status change form
exports.renderStatusForm = async (req, res) => {
    try {
        const visit = await prisma.visit.findUnique({
            where: {
                id: parseInt(req.params.id)
            },
            include: {
                project: true
            }
        });

        if (!visit) {
            req.flash('error_msg', `找不到該訪廠紀錄 [${VISIT_ERROR_CODES.NOT_FOUND}]`);
            return res.redirect('/admin/visits');
        }

        // Only admins or editors who created the record can change status
        if (req.session.user.role !== 'super_admin' && req.session.user.role !== 'admin') {
            if (req.session.user.role === 'editor' && visit.createdById !== req.session.user.id) {
                req.flash('error_msg', `編輯者只能更改自己建立的記錄狀態 [${VISIT_ERROR_CODES.PERMISSION_ERROR}]`);
                return res.redirect('/admin/visits');
            } else if (req.session.user.role !== 'editor') {
                req.flash('error_msg', `您沒有權限更改訪廠紀錄狀態 [${VISIT_ERROR_CODES.PERMISSION_ERROR}]`);
                return res.redirect('/admin/visits');
            }
        }

        res.render('admin/visits/status', {
            title: '更改訪廠狀態',
            visit
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            VISIT_ERROR_CODES.STATUS_UPDATE_ERROR,
            '顯示狀態更改表單時發生錯誤',
            '/admin/visits',
            { visitId: req.params.id }
        );
    }
};

// Update visit status
exports.updateStatus = async (req, res) => {
    try {
        const visitId = parseInt(req.params.id);

        // Get the current visit
        const visit = await prisma.visit.findUnique({
            where: { id: visitId }
        });

        if (!visit) {
            req.flash('error_msg', `找不到該訪廠紀錄 [${VISIT_ERROR_CODES.NOT_FOUND}]`);
            return res.redirect('/admin/visits');
        }

        // Only admins or editors who created the record can change status
        if (req.session.user.role !== 'super_admin' && req.session.user.role !== 'admin') {
            if (req.session.user.role === 'editor' && visit.createdById !== req.session.user.id) {
                req.flash('error_msg', `編輯者只能更改自己建立的記錄狀態 [${VISIT_ERROR_CODES.PERMISSION_ERROR}]`);
                return res.redirect('/admin/visits');
            } else if (req.session.user.role !== 'editor') {
                req.flash('error_msg', `您沒有權限更改訪廠紀錄狀態 [${VISIT_ERROR_CODES.PERMISSION_ERROR}]`);
                return res.redirect('/admin/visits');
            }
        }

        const { status, statusNotes, mouStatus } = req.body;

        // Convert mouStatus to integer if provided
        let mouStatusInt = visit.mouStatus; // Keep existing value by default

        if (mouStatus !== undefined) {
            // Handle both string and number inputs
            if (mouStatus === '1' || mouStatus === 1) {
                mouStatusInt = 1;
            } else if (mouStatus === '0' || mouStatus === 0) {
                mouStatusInt = 0;
            }
        }

        // Update the visit status
        await prisma.visit.update({
            where: { id: visitId },
            data: {
                status,
                statusNotes,
                mouStatus: mouStatusInt
            }
        });

        req.flash('success_msg', '訪廠紀錄狀態已成功更新');
        res.redirect('/admin/visits');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            VISIT_ERROR_CODES.STATUS_UPDATE_ERROR,
            '更新訪廠紀錄狀態時發生錯誤',
            `/admin/visits/${req.params.id}/status`,
            { visitId: req.params.id, requestBody: req.body }
        );
    }
};

// Update visit status and mouStatus directly
exports.updateVisitStatusDirect = async (req, res) => {
    try {
        const visitId = parseInt(req.params.id);
        const { newStatus, newMouStatus } = req.params;

        console.log(`Updating visit ${visitId} with status=${newStatus}, mouStatus=${newMouStatus}`);

        // Validate the status values
        if (!['待安排', '已安排', '完成拜訪'].includes(newStatus)) {
            req.flash('error_msg', `無效的狀態值 [${VISIT_ERROR_CODES.STATUS_UPDATE_ERROR}]`);
            return res.redirect('/admin/visits');
        }

        if (!['未簽約', '已簽約'].includes(newMouStatus)) {
            req.flash('error_msg', `無效的MOU簽約狀態值 [${VISIT_ERROR_CODES.MOU_ERROR}]`);
            return res.redirect('/admin/visits');
        }

        // Convert mouStatus string to integer (0 = '未簽約', 1 = '已簽約')
        const mouStatusInt = newMouStatus === '已簽約' ? 1 : 0;

        // Update the database directly
        await prisma.visit.update({
            where: { id: visitId },
            data: {
                status: newStatus,
                mouStatus: mouStatusInt
            }
        });

        req.flash('success_msg', '訪廠紀錄狀態已成功更新');
        res.redirect('/admin/visits');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            VISIT_ERROR_CODES.STATUS_UPDATE_ERROR,
            '更新訪廠紀錄狀態時發生錯誤',
            '/admin/visits',
            {
                visitId: req.params.id,
                newStatus: req.params.newStatus,
                newMouStatus: req.params.newMouStatus
            }
        );
    }
};

function applyFilters() {
    const status = statusFilter.value.toLowerCase();
    const projectId = projectFilter.value;

    state.statusFilter = status;
    state.projectFilter = projectId;

    state.filteredRows = state.rows.filter(row => {
        const statusCell = row.querySelector('td:nth-child(5)');  // Updated index for status
        const statusText = statusCell.textContent.trim().toLowerCase();

        const rowProjectId = row.getAttribute('data-project-id');

        const statusMatch = status === 'all' || statusText.includes(status);
        const projectMatch = projectId === 'all' || rowProjectId === projectId;

        return statusMatch && projectMatch;
    });
} 