const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const path = require('path');
const fs = require('fs').promises;
const { DOWNLOAD_ERROR_CODES, COMMON_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError, handleFrontendError } = require('../utils/errorHandler');

// 後台管理：列出所有下載項目
exports.listDownloads = async (req, res) => {
    try {
        const { search, category, status, dateFrom, dateTo } = req.query;

        // 建立過濾條件
        const whereConditions = {
            deletedAt: null,
            ...(search ? {
                OR: [
                    { title_en: { contains: search } },
                    { title_tw: { contains: search } },
                    { description_en: { contains: search } },
                    { description_tw: { contains: search } },
                    { keywords_en: { contains: search } },
                    { keywords_tw: { contains: search } },
                    { originalName: { contains: search } }
                ]
            } : {}),
            ...(category ? { categoryId: parseInt(category) } : {}),
            ...(status ? { status } : {})
        };

        // 如果提供了日期範圍則添加日期過濾
        if (dateFrom || dateTo) {
            whereConditions.createdAt = {};

            if (dateFrom) {
                whereConditions.createdAt.gte = new Date(dateFrom);
            }

            if (dateTo) {
                const endDate = new Date(dateTo);
                endDate.setHours(23, 59, 59, 999);
                whereConditions.createdAt.lte = endDate;
            }
        }

        // 根據過濾條件獲取所有下載項目
        const downloads = await prisma.download.findMany({
            where: whereConditions,
            orderBy: { createdAt: 'desc' },
            include: {
                author: {
                    select: { username: true }
                },
                category: true
            }
        });

        // 獲取分類用於過濾下拉選單
        const categories = await prisma.downloadCategory.findMany({
            where: { deletedAt: null },
            orderBy: { order: 'asc' }
        });

        res.render('admin/downloads/list', {
            title: 'Downloads',
            layout: 'layouts/admin',
            downloads,
            categories,
            filters: {
                search: search || '',
                category: category || '',
                status: status || '',
                dateFrom: dateFrom || '',
                dateTo: dateTo || ''
            }
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_ERROR_CODES.LIST_FAILED,
            '載入下載失敗',
            '/admin/dashboard'
        );
    }
};

// 後台管理：渲染建立下載表單
exports.renderCreateDownload = async (req, res) => {
    try {
        const [categories, groups] = await Promise.all([
            prisma.downloadCategory.findMany({
                where: { deletedAt: null },
                orderBy: { order: 'asc' }
            }),
            prisma.downloadGroup.findMany({
                where: { deletedAt: null },
                include: { category: true },
                orderBy: [
                    { categoryId: 'asc' },
                    { order: 'asc' }
                ]
            })
        ]);

        res.render('admin/downloads/create', {
            title: 'Create Download',
            layout: 'layouts/admin',
            categories,
            groups
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_ERROR_CODES.CATEGORY_LOAD_FAILED,
            '載入下載分類失敗',
            '/admin/downloads'
        );
    }
};

// 後台管理：建立新的下載項目
exports.createDownload = async (req, res) => {
    try {
        if (!req.file) {
            return handleControllerError(
                new Error('No file uploaded'),
                req,
                res,
                DOWNLOAD_ERROR_CODES.MISSING_FILE,
                '請選擇要上傳的檔案',
                '/admin/downloads/create'
            );
        }

        const {
            title_en, title_tw,
            description_en, description_tw,
            status,
            keywords_en, keywords_tw,
            group_name_en, group_name_tw,
            categoryId,
            groupId
        } = req.body;
        const { filename, originalname, mimetype, size } = req.file;
        // 確保檔案名稱使用 UTF-8 編碼
        const decodedOriginalName = Buffer.from(originalname, 'latin1').toString('utf8');
        // 儲存相對於 public 目錄的路徑
        const filePath = `/uploads/downloads/${filename}`;

        await prisma.download.create({
            data: {
                title_en,
                title_tw,
                description_en,
                description_tw,
                status: status || 'draft',
                keywords_en,
                keywords_tw,
                group_name_en,
                group_name_tw,
                categoryId: categoryId ? parseInt(categoryId) : null,
                groupId: groupId ? parseInt(groupId) : null,
                filename,
                originalName: decodedOriginalName,
                mimeType: mimetype,
                size,
                path: filePath,
                authorId: req.session.user.id
            }
        });

        req.flash('success_msg', '下載已成功建立');
        res.redirect('/admin/downloads');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_ERROR_CODES.CREATE_FAILED,
            '建立下載失敗',
            '/admin/downloads/create'
        );
    }
};

// 後台管理：渲染編輯下載表單
exports.renderEditDownload = async (req, res) => {
    try {
        // 從請求參數中解析 ID
        const id = parseInt(req.params.id, 10);

        // 檢查 ID 是否有效
        if (isNaN(id)) {
            return handleControllerError(
                new Error('Invalid download ID'),
                req,
                res,
                DOWNLOAD_ERROR_CODES.INVALID_ID,
                '無效的下載 ID',
                '/admin/downloads'
            );
        }

        const [download, categories, groups] = await Promise.all([
            prisma.download.findUnique({
                where: { id },
                include: {
                    category: true,
                    group: true
                }
            }),
            prisma.downloadCategory.findMany({
                where: { deletedAt: null },
                orderBy: { order: 'asc' }
            }),
            prisma.downloadGroup.findMany({
                where: { deletedAt: null },
                include: { category: true },
                orderBy: [
                    { categoryId: 'asc' },
                    { order: 'asc' }
                ]
            })
        ]);

        if (!download) {
            return handleControllerError(
                new Error('Download not found'),
                req,
                res,
                DOWNLOAD_ERROR_CODES.NOT_FOUND,
                '找不到下載',
                '/admin/downloads'
            );
        }

        res.render('admin/downloads/edit', {
            title: 'Edit Download',
            layout: 'layouts/admin',
            download,
            categories,
            groups
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_ERROR_CODES.EDIT_FORM_FAILED,
            '載入下載失敗',
            '/admin/downloads'
        );
    }
};

// 後台管理：更新下載項目
exports.updateDownload = async (req, res) => {
    try {
        // 從請求參數中解析 ID
        const id = parseInt(req.params.id, 10);

        // 檢查 ID 是否有效
        if (isNaN(id)) {
            return handleControllerError(
                new Error('Invalid download ID'),
                req,
                res,
                DOWNLOAD_ERROR_CODES.INVALID_ID,
                '無效的下載 ID',
                '/admin/downloads'
            );
        }

        const {
            title_en, title_tw,
            description_en, description_tw,
            status,
            keywords_en, keywords_tw,
            group_name_en, group_name_tw,
            categoryId,
            groupId
        } = req.body;

        // 查找要更新的下載項目
        const download = await prisma.download.findUnique({
            where: { id }
        });

        if (!download) {
            return handleControllerError(
                new Error('Download not found'),
                req,
                res,
                DOWNLOAD_ERROR_CODES.NOT_FOUND,
                '找不到下載',
                '/admin/downloads'
            );
        }

        // 準備更新數據
        const updateData = {
            title_en,
            title_tw,
            description_en,
            description_tw,
            status,
            keywords_en,
            keywords_tw,
            group_name_en,
            group_name_tw,
            categoryId: categoryId ? parseInt(categoryId) : null,
            updatedAt: new Date()
        };

        // 如果上傳了新文件，則更新文件信息
        if (req.file) {
            const { filename, originalname, mimetype, size } = req.file;
            // 確保檔案名稱使用 UTF-8 編碼
            const decodedOriginalName = Buffer.from(originalname, 'latin1').toString('utf8');
            const filePath = `/uploads/downloads/${filename}`;

            // 刪除舊文件
            try {
                await fs.unlink(path.join(__dirname, '../../public', download.path));
            } catch (error) {
                logger.warn(`${DOWNLOAD_ERROR_CODES.FILE_SYSTEM_ERROR}: Error deleting old file: ${error.message}`);
                // 即使舊文件刪除失敗也繼續
            }

            // 更新文件信息
            Object.assign(updateData, {
                filename,
                originalName: decodedOriginalName,
                mimeType: mimetype,
                size,
                path: filePath
            });
        }

        // Add groupId to updateData
        if (groupId) {
            updateData.groupId = parseInt(groupId);
        } else {
            updateData.groupId = null;
        }

        // 更新下載項目
        await prisma.download.update({
            where: { id },
            data: updateData
        });

        req.flash('success_msg', '下載已成功更新');
        res.redirect('/admin/downloads');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_ERROR_CODES.UPDATE_FAILED,
            '更新下載失敗',
            '/admin/downloads'
        );
    }
};

// 後台管理：刪除下載項目
exports.deleteDownload = async (req, res) => {
    try {
        // 從請求參數中解析 ID
        const id = parseInt(req.params.id, 10);

        // 檢查 ID 是否有效
        if (isNaN(id)) {
            return handleControllerError(
                new Error('Invalid download ID'),
                req,
                res,
                DOWNLOAD_ERROR_CODES.INVALID_ID,
                '無效的下載 ID',
                '/admin/downloads'
            );
        }

        // 查找要刪除的下載項目
        const download = await prisma.download.findUnique({
            where: { id }
        });

        if (!download) {
            return handleControllerError(
                new Error('Download not found'),
                req,
                res,
                DOWNLOAD_ERROR_CODES.NOT_FOUND,
                '找不到下載',
                '/admin/downloads'
            );
        }

        // 從文件系統中刪除文件
        try {
            const filePath = path.join(process.cwd(), 'public', download.path);
            await fs.unlink(filePath);
            logger.info(`已刪除文件: ${filePath}`);
        } catch (error) {
            logger.warn(`${DOWNLOAD_ERROR_CODES.FILE_SYSTEM_ERROR}: 無法刪除文件: ${error.message}`);
            // 即使文件刪除失敗也繼續
        }

        // 軟刪除下載項目
        await prisma.download.update({
            where: { id },
            data: { deletedAt: new Date() }
        });

        req.flash('success_msg', '下載已成功刪除');
        res.redirect('/admin/downloads');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_ERROR_CODES.DELETE_FAILED,
            '刪除下載失敗',
            '/admin/downloads'
        );
    }
};

// 前台：列出所有下載項目
exports.listDownloadsForFrontend = async (req, res) => {
    try {
        const { search, category, page = 1 } = req.query;
        const language = req.params.language || 'en'; // 從路由參數獲取語言
        const perPage = 120;
        const skip = (page - 1) * perPage;

        // 建立過濾條件
        const whereConditions = {
            status: 'published',
            deletedAt: null,
            ...(search ? {
                OR: [
                    { title_en: { contains: search } },
                    { title_tw: { contains: search } },
                    { description_en: { contains: search } },
                    { description_tw: { contains: search } },
                    { keywords_en: { contains: search } },
                    { keywords_tw: { contains: search } }
                ]
            } : {}),
            ...(category ? { categoryId: parseInt(category) } : {})
        };

        // 獲取分頁下載項目
        const [downloads, totalCount] = await Promise.all([
            prisma.download.findMany({
                where: whereConditions,
                orderBy: [
                    { categoryId: 'asc' },
                    { groupId: 'asc' },
                    { group_name_tw: 'asc' },
                    { createdAt: 'desc' }
                ],
                skip,
                take: perPage,
                include: {
                    category: true,
                    group: true
                }
            }),
            prisma.download.count({
                where: whereConditions
            })
        ]);

        // 獲取所有分類用於過濾
        const categories = await prisma.downloadCategory.findMany({
            where: { deletedAt: null },
            orderBy: { order: 'asc' }
        });

        // 處理下載項目以使用適當的語言內容
        const processedDownloads = downloads.map(download => {
            const titleField = `title_${language}`;
            const descriptionField = `description_${language}`;
            const keywordsField = `keywords_${language}`;
            const groupNameField = `group_name_${language}`;

            return {
                ...download,
                title: download[titleField] || download.title_en, // 若無對應語言則回退到英文
                description: download[descriptionField] || download.description_en,
                keywords: download[keywordsField] || download.keywords_en,
                groupName: download[groupNameField] || download.group_name_en
            };
        });

        // 處理分類以使用適當的語言內容
        const processedCategories = categories.map(category => {
            const nameField = `name_${language}`;
            const descriptionField = `description_${language}`;

            return {
                ...category,
                name: category[nameField] || category.name_en, // 若無對應語言則回退到英文
                description: category[descriptionField] || category.description_en
            };
        });

        // 計算分頁信息
        const totalPages = Math.ceil(totalCount / perPage);
        const hasNextPage = page < totalPages;
        const hasPrevPage = page > 1;

        res.render('frontend/downloads', {
            title: language === 'tw' ? '下載專區' : 'Downloads',
            layout: 'layouts/frontend',
            downloads: processedDownloads,
            categories: processedCategories,
            language,
            filters: {
                search: search || '',
                category: category || ''
            },
            pagination: {
                current: parseInt(page),
                perPage,
                total: totalCount,
                totalPages,
                hasNextPage,
                hasPrevPage
            },
            stats: {
                totalDownloads: totalCount
            }
        });
    } catch (error) {
        handleFrontendError(
            error,
            req,
            res,
            DOWNLOAD_ERROR_CODES.FRONTEND_LIST_FAILED,
            language === 'tw' ? '載入下載失敗' : 'Failed to load downloads',
            'frontend/error',
            { language }
        );
    }
};

// 前台：下載檔案
exports.downloadFile = async (req, res) => {
    try {
        const { id } = req.params;
        const language = req.params.language || 'en'; // 從路由參數獲取語言
        logger.info(`嘗試下載檔案，ID: ${id}，語言: ${language}`);

        // 查找下載項目
        const download = await prisma.download.findFirst({
            where: {
                id: parseInt(id),
                status: 'published',
                deletedAt: null
            },
            include: { category: true }
        });

        if (!download) {
            logger.warn(`${DOWNLOAD_ERROR_CODES.NOT_FOUND}: 找不到ID為 ${id} 的下載項目或未發布`);
            return handleFrontendError(
                new Error('Download not found'),
                req,
                res,
                DOWNLOAD_ERROR_CODES.NOT_FOUND,
                language === 'tw' ? '找不到您要求的檔案' : 'The requested file was not found',
                'frontend/error',
                { language }
            );
        }

        // 處理下載項目以使用適當的語言內容
        const titleField = `title_${language}`;
        const groupNameField = `group_name_${language}`;
        const processedDownload = {
            ...download,
            title: download[titleField] || download.title_en, // 若無對應語言則回退到英文
            groupName: download[groupNameField] || download.group_name_en
        };

        logger.info(`找到下載記錄: ${JSON.stringify(processedDownload, null, 2)}`);

        // 增加下載計數
        await prisma.download.update({
            where: { id: parseInt(id) },
            data: { downloadCount: { increment: 1 } }
        });

        // 移除路徑前導斜線並與公共目錄連接
        const relativePath = download.path.startsWith('/') ? download.path.slice(1) : download.path;
        const filePath = path.join(process.cwd(), 'public', relativePath);

        logger.info(`嘗試訪問路徑為 ${filePath} 的檔案`);

        // 檢查檔案是否存在
        try {
            await fs.access(filePath);
            logger.info('檔案存在，繼續下載');
        } catch (error) {
            logger.error(`${DOWNLOAD_ERROR_CODES.FILE_NOT_FOUND}: 在路徑 ${filePath} 找不到檔案:`, error);
            return handleFrontendError(
                error,
                req,
                res,
                DOWNLOAD_ERROR_CODES.FILE_NOT_FOUND,
                language === 'tw' ? '找不到您要求的檔案' : 'The requested file was not found',
                'frontend/error',
                { language }
            );
        }

        // 設置下載標頭
        // 為 Content-Disposition 標頭中的檔案名稱進行 UTF-8 編碼
        const encodedFilename = encodeURIComponent(download.originalName)
            .replace(/['()]/g, escape)  // RFC 5987 要求對括號和引號進行轉義
            .replace(/\*/g, '%2A');     // 星號也需要被轉義

        res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);
        res.setHeader('Content-Type', download.mimeType);

        // 發送檔案
        res.download(filePath, download.originalName, (err) => {
            if (err) {
                logger.error(`${DOWNLOAD_ERROR_CODES.DOWNLOAD_FAILED}: 下載檔案 ${download.id} 時出錯:`, err);
                handleFrontendError(
                    err,
                    req,
                    res,
                    DOWNLOAD_ERROR_CODES.DOWNLOAD_FAILED,
                    language === 'tw' ? '下載檔案失敗' : 'Failed to download file',
                    'frontend/error',
                    { language }
                );
            }
            logger.info(`成功下載檔案: ${download.originalName}`);
        });
    } catch (error) {
        logger.error(`${DOWNLOAD_ERROR_CODES.DOWNLOAD_FAILED}: 下載檔案時出錯:`, error);
        handleFrontendError(
            error,
            req,
            res,
            DOWNLOAD_ERROR_CODES.DOWNLOAD_FAILED,
            language === 'tw' ? '下載檔案失敗' : 'Failed to download file',
            'frontend/error',
            { language }
        );
    }
};

// 後台管理：下載檔案 (管理員專用)
exports.adminDownloadFile = async (req, res) => {
    try {
        const { id } = req.params;
        logger.info(`管理員嘗試下載檔案，ID: ${id}`);

        // 查找下載項目 (管理員可以下載未發布的檔案)
        const download = await prisma.download.findFirst({
            where: {
                id: parseInt(id),
                deletedAt: null
            },
            include: { category: true }
        });

        if (!download) {
            logger.warn(`${DOWNLOAD_ERROR_CODES.NOT_FOUND}: 找不到ID為 ${id} 的下載項目`);
            return handleControllerError(
                new Error('Download not found'),
                req,
                res,
                DOWNLOAD_ERROR_CODES.NOT_FOUND,
                '找不到檔案',
                '/admin/downloads'
            );
        }

        logger.info(`找到下載記錄: ${JSON.stringify(download, null, 2)}`);

        // 移除路徑前導斜線並與公共目錄連接
        const relativePath = download.path.startsWith('/') ? download.path.slice(1) : download.path;
        const filePath = path.join(process.cwd(), 'public', relativePath);

        logger.info(`嘗試訪問路徑為 ${filePath} 的檔案`);

        // 檢查檔案是否存在
        try {
            await fs.access(filePath);
            logger.info('檔案存在，繼續下載');
        } catch (error) {
            logger.error(`${DOWNLOAD_ERROR_CODES.FILE_NOT_FOUND}: 在路徑 ${filePath} 找不到檔案:`, error);
            return handleControllerError(
                error,
                req,
                res,
                DOWNLOAD_ERROR_CODES.FILE_NOT_FOUND,
                '找不到檔案',
                '/admin/downloads'
            );
        }

        // 設置下載標頭
        // 為 Content-Disposition 標頭中的檔案名稱進行 UTF-8 編碼
        const encodedFilename = encodeURIComponent(download.originalName)
            .replace(/['()]/g, escape)  // RFC 5987 要求對括號和引號進行轉義
            .replace(/\*/g, '%2A');     // 星號也需要被轉義

        res.setHeader('Content-Disposition', `attachment; filename*=UTF-8''${encodedFilename}`);
        res.setHeader('Content-Type', download.mimeType);

        // 發送檔案
        res.download(filePath, download.originalName, (err) => {
            if (err) {
                logger.error(`${DOWNLOAD_ERROR_CODES.DOWNLOAD_FAILED}: 下載檔案 ${download.id} 時出錯:`, err);
                return handleControllerError(
                    err,
                    req,
                    res,
                    DOWNLOAD_ERROR_CODES.DOWNLOAD_FAILED,
                    '下載檔案失敗',
                    '/admin/downloads'
                );
            }
            logger.info(`成功下載檔案: ${download.originalName}`);
        });
    } catch (error) {
        logger.error(`${DOWNLOAD_ERROR_CODES.DOWNLOAD_FAILED}: 下載檔案時出錯:`, error);
        return handleControllerError(
            error,
            req,
            res,
            DOWNLOAD_ERROR_CODES.DOWNLOAD_FAILED,
            '下載檔案失敗',
            '/admin/downloads'
        );
    }
};
