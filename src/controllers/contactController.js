const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const nodemailer = require('nodemailer');
const { CONTACT_ERROR_CODES, FRONTEND_ERROR_CODES } = require('../config/errorCodes');
const { handleControllerError, handleFrontendError } = require('../utils/errorHandler');

// Create nodemailer transporter
const transporter = nodemailer.createTransport({
    service: process.env.EMAIL_SERVICE || 'gmail',
    auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD
    },
});

// Frontend: Render contact form
exports.showContactForm = async (req, res) => {
    try {
        const language = req.params.language || 'en';
        
        // Get all active categories
        const categories = await prisma.contactCategory.findMany({
            where: {
                deletedAt: null
            },
            orderBy: {
                order: 'asc'
            }
        });
        
        // Get the contact agreement
        const agreement = await prisma.contactUsAgreement.findFirst();
        
        res.render('frontend/contact', {
            title: language === 'en' ? 'Contact Us' : '聯絡我們',
            categories,
            agreement,
            layout: 'layouts/frontend'
        });
    } catch (error) {
        handleFrontendError(
            error,
            req,
            res,
            FRONTEND_ERROR_CODES.RENDER_FAILED,
            language === 'en' ? 'Failed to load contact page' : '載入聯絡頁面失敗',
            'frontend/error'
        );
    }
};

// Frontend: Submit contact form
exports.submitContactForm = async (req, res) => {
    try {
        const language = req.params.language || 'en';
        const { name, email, company, phone, categoryId, message, agreeTerms } = req.body;
        
        // Validate required fields
        if (!name || !email || !message || !agreeTerms) {
            const [categories, agreement] = await Promise.all([
                prisma.contactCategory.findMany({
                    where: { deletedAt: null },
                    orderBy: { order: 'asc' }
                }),
                prisma.contactUsAgreement.findFirst()
            ]);
            
            return res.status(400).render('frontend/contact', {
                title: language === 'en' ? 'Contact Us' : '聯絡我們',
                categories,
                agreement,
                error: language === 'en' ? 'Please fill in all required fields and agree to the terms.' : '請填寫所有必填欄位並同意條款。',
                formData: { name, email, company, phone, categoryId, message },
                layout: 'layouts/frontend'
            });
        }
        
        // Get category name if categoryId is provided
        let categoryName = '';
        if (categoryId) {
            const category = await prisma.contactCategory.findUnique({
                where: { id: parseInt(categoryId) }
            });
            if (category) {
                categoryName = language === 'en' ? category.name_en : category.name_tw;
            }
        }
        
        // Create contact submission
        const contact = await prisma.contact.create({
            data: {
                name,
                email,
                company: company || null,
                phone: phone || null,
                categoryId: categoryId ? parseInt(categoryId) : null,
                message,
                agreeTerms: agreeTerms === 'on' || agreeTerms === true
            }
        });
        
        // Send email notification
        try {
            await transporter.sendMail({
                from: process.env.EMAIL_USER,
                to: process.env.EMAIL_RECIPIENT,
                subject: language === 'en' ? `New Contact Form Submission: ${name}` : `新的聯絡表單提交: ${name}`,
                html: `
                    <h2>${language === 'en' ? 'New Contact Form Submission' : '新的聯絡表單提交'}</h2>
                    <p><strong>${language === 'en' ? 'Name' : '姓名'}:</strong> ${name}</p>
                    <p><strong>${language === 'en' ? 'Email' : '電子信箱'}:</strong> ${email}</p>
                    ${company ? `<p><strong>${language === 'en' ? 'Company' : '公司名稱'}:</strong> ${company}</p>` : ''}
                    ${phone ? `<p><strong>${language === 'en' ? 'Phone' : '聯絡電話'}:</strong> ${phone}</p>` : ''}
                    ${categoryName ? `<p><strong>${language === 'en' ? 'Category' : '類別'}:</strong> ${categoryName}</p>` : ''}
                    <p><strong>${language === 'en' ? 'Message' : '訊息'}:</strong></p>
                    <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px;">
                        ${message.replace(/\n/g, '<br>')}
                    </div>
                    <p><strong>${language === 'en' ? 'Submitted at' : '提交時間'}:</strong> ${new Date().toLocaleString()}</p>
                    <p><a href="${req.protocol}://${req.get('host')}/admin/contact/view/${contact.id}">${language === 'en' ? 'View in admin panel' : '在管理面板中查看'}</a></p>
                `
            });
            logger.info(`Email notification sent for contact submission ID: ${contact.id}`);
            logger.info(`Email details: From: ${process.env.EMAIL_USER}, To: ${process.env.EMAIL_RECIPIENT}, Subject: ${language === 'en' ? `New Contact Form Submission: ${name}` : `新的聯絡表單提交: ${name}`}`);
        } catch (emailError) {
            logger.error(`Failed to send email notification for contact ID: ${contact.id}`, {
                errorCode: CONTACT_ERROR_CODES.EMAIL_ERROR,
                error: emailError
            });
            // Continue with the form submission even if email fails
        }
        
        // Render success page
        res.render('frontend/contact-success', {
            title: language === 'en' ? 'Message Sent' : '訊息已送出',
            layout: 'layouts/frontend'
        });
    } catch (error) {
        const language = req.params.language || 'en';
        try {
            const [categories, agreement] = await Promise.all([
                prisma.contactCategory.findMany({
                    where: { deletedAt: null },
                    orderBy: { order: 'asc' }
                }),
                prisma.contactUsAgreement.findFirst()
            ]);
            
            return res.status(500).render('frontend/contact', {
                title: language === 'en' ? 'Contact Us' : '聯絡我們',
                categories,
                agreement,
                error: language === 'en' ? 'Failed to submit your message. Please try again later.' : '無法提交您的訊息。請稍後再試。',
                formData: req.body,
                layout: 'layouts/frontend'
            });
        } catch (fetchError) {
            // If we can't fetch categories and agreement, use a more generic error page
            handleFrontendError(
                error,
                req,
                res,
                CONTACT_ERROR_CODES.SUBMISSION_FAILED,
                language === 'en' ? 'Failed to submit your message. Please try again later.' : '無法提交您的訊息。請稍後再試。',
                'frontend/error'
            );
        }
    }
};

// Admin: List all contact submissions
exports.listContacts = async (req, res) => {
    try {
        const { category, status, sort, order } = req.query;
        
        // Build where clause
        const where = {
            deletedAt: null
        };
        
        if (category) {
            where.categoryId = parseInt(category);
        }
        
        if (status) {
            where.status = status;
        }
        
        // Build order by
        let orderBy = {};
        if (sort) {
            orderBy[sort] = order === 'asc' ? 'asc' : 'desc';
        } else {
            orderBy = { createdAt: 'desc' };
        }
        
        // Get contacts with pagination
        const page = parseInt(req.query.page) || 1;
        const limit = 20;
        const skip = (page - 1) * limit;
        
        const [contacts, totalCount, categories] = await Promise.all([
            prisma.contact.findMany({
                where,
                include: {
                    category: true
                },
                orderBy,
                skip,
                take: limit
            }),
            prisma.contact.count({ where }),
            prisma.contactCategory.findMany({
                where: { deletedAt: null },
                orderBy: { order: 'asc' }
            })
        ]);
        
        const totalPages = Math.ceil(totalCount / limit);
        
        res.render('admin/contact/index', {
            title: 'Contact Submissions',
            contacts,
            categories,
            currentCategory: category,
            currentStatus: status,
            currentSort: sort,
            currentOrder: order,
            pagination: {
                page,
                limit,
                totalCount,
                totalPages
            }
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CONTACT_ERROR_CODES.LIST_FAILED,
            '載入聯絡訊息失敗',
            '/admin/dashboard'
        );
    }
};

// Admin: View contact details
exports.viewContact = async (req, res) => {
    try {
        const { id } = req.params;
        
        const contact = await prisma.contact.findUnique({
            where: { id: parseInt(id) },
            include: {
                category: true
            }
        });
        
        if (!contact) {
            return handleControllerError(
                new Error('Contact submission not found'),
                req,
                res,
                CONTACT_ERROR_CODES.NOT_FOUND,
                '找不到聯絡訊息',
                '/admin/contact'
            );
        }
        
        res.render('admin/contact/view', {
            title: 'View Contact Submission',
            contact
        });
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CONTACT_ERROR_CODES.VIEW_FAILED,
            '載入聯絡訊息失敗',
            '/admin/contact'
        );
    }
};

// Admin: Update contact status
exports.updateContactStatus = async (req, res) => {
    try {
        const { id } = req.params;
        const { status } = req.body;
        
        await prisma.contact.update({
            where: { id: parseInt(id) },
            data: {
                status,
                updatedAt: new Date()
            }
        });
        
        req.flash('success_msg', '聯絡狀態已成功更新');
        res.redirect(`/admin/contact/view/${id}`);
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CONTACT_ERROR_CODES.STATUS_UPDATE_ERROR,
            '更新聯絡狀態失敗',
            `/admin/contact/view/${req.params.id}`
        );
    }
};

// Admin: Delete contact
exports.deleteContact = async (req, res) => {
    try {
        const { id } = req.params;
        
        // Soft delete the contact
        await prisma.contact.update({
            where: { id: parseInt(id) },
            data: {
                deletedAt: new Date()
            }
        });
        
        req.flash('success_msg', '聯絡訊息已成功刪除');
        res.redirect('/admin/contact');
    } catch (error) {
        handleControllerError(
            error,
            req,
            res,
            CONTACT_ERROR_CODES.DELETE_FAILED,
            '刪除聯絡訊息失敗',
            '/admin/contact'
        );
    }
}; 