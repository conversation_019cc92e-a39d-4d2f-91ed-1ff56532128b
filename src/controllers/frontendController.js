const prisma = require('../lib/prisma');
const logger = require('../config/logger');
const { FRONTEND_ERROR_CODES } = require('../config/errorCodes');
const { handleFrontendError } = require('../utils/errorHandler');

// Display links page
exports.links = async (req, res) => {
    try {
        const language = req.language || 'en';
        const pageTitle = language === 'tw' ? '相關連結' : 'Related Links';

        // Get active links ordered by display order
        const links = await prisma.link.findMany({
            where: {
                active: true
            },
            orderBy: {
                order: 'asc'
            }
        });

        res.render('frontend/links', {
            title: pageTitle,
            links,
            currentLanguage: language
        });
    } catch (error) {
        handleFrontendError(
            error,
            req,
            res,
            FRONTEND_ERROR_CODES.DATA_FETCH_FAILED, 
            language === 'en' ? 'Failed to load links' : '載入相關連結失敗',
            'frontend/error',
            { currentLanguage: language }
        );
    }
};

// Get frontpage items for the homepage
exports.getFrontpageItems = async (language = 'tw') => {
    try {
        logger.info('Getting frontpage items for language:', language);
        
        // Get published frontpage items ordered by their order field
        const items = await prisma.frontpageItem.findMany({
            where: {
                status: 'published',
                deletedAt: null
            },
            include: {
                category: true,
                images: {
                    where: {
                        OR: [
                            { language: language }, // Get images for the current language
                            { language: null }      // Include images without language specification (for backward compatibility)
                        ]
                    },
                    orderBy: {
                        order: 'asc'
                    }
                }
            },
            orderBy: {
                order: 'asc'
            }
        });

        // Group items by type
        const plainTextItems = items.filter(item => item.type === 'plain_text');
        const pictureItems = items.filter(item => item.type === 'picture');
        
        // Log picture items info at info level, not debug level
        logger.info(`Found ${pictureItems.length} picture items for language: ${language}`);
        pictureItems.forEach((item, index) => {
            logger.debug(`Picture item ${index+1}: ${item.title_tw} (${item.id}) has ${item.images.length} images`);
            item.images.forEach((img, i) => {
                logger.debug(`  Image ${i+1}: ${img.path}, language: ${img.language || 'null'}, URL: ${img.url || 'none'}`);
            });
        });

        return {
            plainTextItems,
            pictureItems
        };
    } catch (error) {
        logger.error(`${FRONTEND_ERROR_CODES.DATA_FETCH_FAILED}: Error fetching frontpage items:`, { 
            error, 
            language,
            errorMessage: error.message,
            stack: error.stack
        });
        return {
            plainTextItems: [],
            pictureItems: []
        };
    }
}; 