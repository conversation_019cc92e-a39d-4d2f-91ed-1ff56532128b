const prisma = require('../../src/lib/prisma');

async function createVisitorRole() {
  try {
    // Check if visitor role already exists
    const existingRole = await prisma.role.findUnique({
      where: { name: 'visitor' }
    });

    if (existingRole) {
      console.log('Visitor role already exists. Updating permissions...');
      const updatedRole = await prisma.role.update({
        where: { name: 'visitor' },
        data: {
          description: 'Visitor user with read-only access to visits page',
          permissions: [
            'view:visits'
          ]
        }
      });
      console.log('Visitor role updated successfully:', updatedRole);
      return;
    }

    // Create the visitor role with limited permissions (view-only)
    const visitorRole = await prisma.role.create({
      data: {
        name: 'visitor',
        description: 'Visitor user with read-only access to visits page',
        permissions: [
          'view:visits'
        ]
      }
    });

    console.log('Visitor role created successfully:', visitorRole);
  } catch (error) {
    console.error('Error creating visitor role:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createVisitorRole()
  .then(() => console.log('<PERSON>ript completed'))
  .catch(e => console.error('<PERSON><PERSON><PERSON> failed:', e)); 