const prisma = require('../../src/lib/prisma');
const bcrypt = require('bcryptjs');

async function createVisitorAccount() {
  try {
    // Check if visitor role exists
    const visitorRole = await prisma.role.findUnique({
      where: { name: 'visitor' }
    });

    if (!visitorRole) {
      console.error('Visitor role does not exist! Please run create-visitor-role.js first.');
      return;
    }

    // Check if visitor account already exists
    const existingVisitor = await prisma.user.findUnique({
      where: { username: 'visitor' }
    });

    if (existingVisitor) {
      console.log('Visitor account already exists. Updating to visitor role...');
      const updatedUser = await prisma.user.update({
        where: { username: 'visitor' },
        data: {
          roleId: visitorRole.id,
          isActive: true,
          lastLogin: null
        }
      });
      console.log('Visitor account updated successfully:', updatedUser);
      return;
    }

    // Create visitor account
    const hashedPassword = await bcrypt.hash('visitor', 10);
    const visitorUser = await prisma.user.create({
      data: {
        username: 'visitor',
        email: '<EMAIL>',
        password: hashedPassword,
        roleId: visitorRole.id,
        contactName_zh: '僅限查看訪客',
        contactName_en: 'Visitor (Read-only)',
        isActive: true
      }
    });

    console.log('Visitor account created successfully:', visitorUser);
  } catch (error) {
    console.error('Error creating visitor account:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createVisitorAccount()
  .then(() => console.log('Script completed'))
  .catch(e => console.error('Script failed:', e)); 