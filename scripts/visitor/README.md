# Visitor Module Scripts

This directory contains scripts for setting up and managing the visitor role and accounts.

## Quick Setup

To create both the visitor role and a default visitor account in one step:

```bash
node scripts/visitor/setup-visitor.js
```

This will:
1. Create or update the visitor role with read-only permissions
2. Create or update a visitor account with the following credentials:
   - Username: `visitor`
   - Password: `visitor`

## Individual Scripts

If you need to run steps separately:

### 1. Create/Update Visitor Role

Creates or updates the visitor role with appropriate permissions:

```bash
node scripts/visitor/create-visitor-role.js
```

### 2. Create/Update Visitor Account

Creates or updates a visitor account with default credentials:

```bash
node scripts/visitor/create-visitor-account.js
```

## About the Visitor Role

The visitor role is designed for users who need read-only access to the visits module. Unlike the guest role which can view and add visits, visitor users can only view existing visits without the ability to create, edit, or delete them.

## Related Files

- SQL Migration: `prisma/migrations/20250501000000_add_visitor_role/migration.sql`  
- Database seeder updates: `prisma/seed.js` (includes visitor role and account creation) 