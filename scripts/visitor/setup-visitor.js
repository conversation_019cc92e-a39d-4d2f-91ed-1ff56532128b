const prisma = require('../../src/lib/prisma');
const bcrypt = require('bcryptjs');

async function setupVisitor() {
  try {
    console.log('Setting up visitor role and account...');

    // Step 1: Create or update visitor role
    console.log('Creating/updating visitor role...');
    const visitorRole = await prisma.role.upsert({
      where: { name: 'visitor' },
      update: {
        description: 'Visitor user with read-only access to visits page',
        permissions: [
          'view:visits'
        ]
      },
      create: {
        name: 'visitor',
        description: 'Visitor user with read-only access to visits page',
        permissions: [
          'view:visits'
        ]
      }
    });
    console.log('Visitor role created/updated:', visitorRole);

    // Step 2: Create or update visitor account
    console.log('Creating/updating visitor account...');
    const hashedPassword = await bcrypt.hash('visitor', 10);
    const visitorUser = await prisma.user.upsert({
      where: { username: 'visitor' },
      update: {
        roleId: visitorRole.id,
        isActive: true,
        email: '<EMAIL>',
        contactName_zh: '僅限查看訪客',
        contactName_en: 'Visitor (Read-only)'
      },
      create: {
        username: 'visitor',
        email: '<EMAIL>',
        password: hashedPassword,
        roleId: visitorRole.id,
        contactName_zh: '僅限查看訪客',
        contactName_en: 'Visitor (Read-only)',
        isActive: true
      }
    });
    console.log('Visitor account created/updated:', visitorUser);

    console.log('Visitor setup complete!');
    console.log('You can now log in with:');
    console.log('Username: visitor');
    console.log('Password: visitor');
  } catch (error) {
    console.error('Error setting up visitor:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setupVisitor()
  .then(() => console.log('Script completed'))
  .catch(e => console.error('Script failed:', e)); 