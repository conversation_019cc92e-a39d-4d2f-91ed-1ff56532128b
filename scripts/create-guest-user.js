const bcrypt = require('bcryptjs');
const prisma = require('../src/lib/prisma');

async function createGuestUser() {
  try {
    // Find guest role
    const guestRole = await prisma.role.findUnique({
      where: { name: 'guest' }
    });

    if (!guestRole) {
      console.log('Guest role not found. Please run create-guest-role.js first.');
      return;
    }

    // Check if guest user already exists
    const existingUser = await prisma.user.findFirst({
      where: { 
        username: 'guest_user',
        roleId: guestRole.id
      }
    });

    if (existingUser) {
      console.log('Guest user already exists:', existingUser.username);
      return;
    }

    // Create a default guest user
    const password = 'guest'; // Change this to a secure password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Find a project to associate with the guest user
    const project = await prisma.project.findFirst({
      where: { isActive: true }
    });

    const guestUser = await prisma.user.create({
      data: {
        username: 'guest_user',
        email: '<EMAIL>',
        password: hashedPassword,
        roleId: guestRole.id,
        projectId: project?.id,
        contactName_zh: '訪客用戶',
        contactName_en: 'Guest User',
        contactPhone: '',
        isActive: true
      }
    });

    console.log('Guest user created successfully:');
    console.log('Username:', guestUser.username);
    console.log('Password:', password);
    console.log('Role:', 'guest');
    console.log('Project ID:', guestUser.projectId);
  } catch (error) {
    console.error('Error creating guest user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createGuestUser(); 