const prisma = require('../src/lib/prisma');

async function createGuestRole() {
  try {
    // Check if guest role already exists
    const existingRole = await prisma.role.findUnique({
      where: { name: 'guest' }
    });

    if (existingRole) {
      console.log('Guest role already exists. Updating permissions...');
      const updatedRole = await prisma.role.update({
        where: { name: 'guest' },
        data: {
          description: 'Guest user with access only to visits page',
          permissions: [
            'access:dashboard',
            'view:visits'
          ]
        }
      });
      console.log('Guest role updated successfully:', updatedRole);
      return;
    }

    // Create the guest role with limited permissions
    const guestRole = await prisma.role.create({
      data: {
        name: 'guest',
        description: 'Guest user with access only to visits page',
        permissions: [
          'access:dashboard',
          'view:visits'
        ]
      }
    });

    console.log('Guest role created successfully:', guestRole);
  } catch (error) {
    console.error('Error creating guest role:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createGuestRole(); 