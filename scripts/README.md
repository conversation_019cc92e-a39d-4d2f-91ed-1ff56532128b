# Scripts Directory

This directory contains utility scripts for database management and system maintenance.

## Modules

### [Visitor Module](./visitor/README.md)

Scripts for creating and managing the visitor role and accounts with read-only access.

```bash
# Quick setup for visitor role and account
node scripts/visitor/setup-visitor.js
```

See the [visitor module README](./visitor/README.md) for more details.

## Other Scripts

For other database management tasks, the following scripts are available:

- Database seeding (creates all default roles, users, and content):
```bash
npx prisma db seed
``` 