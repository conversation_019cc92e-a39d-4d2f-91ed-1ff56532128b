/* Base styles (Mobile first) */
:root {
    --header-height: 64px; /* 導覽列高度 */
    --color-custom-lightblue: #3AB5E0;
    --color-custom-blue: #417C95;
    --color-custom-lightgreen: #9CFFE6;
    --color-custom-green: #1E662D;
    --color-custom-darkgreen: #37b7a2;
    --color-custom-yellow: #FFE082;
    --color-custom-darkyellow: #cdb46a;
}

.bg-custom-blue {
    background-color: var(--color-custom-blue);
}
.bg-custom-green {
    background-color: var(--color-custom-green);
}
.bg-custom-lightblue {
    background-color: var(--color-custom-lightblue);
}
.bg-custom-lightgreen {
    background-color: var(--color-custom-lightgreen);
}
.bg-custom-yellow {
    background-color: var(--color-custom-yellow);
}
.text-custom-blue {
    color: var(--color-custom-blue);
}
.text-custom-green {
    color: var(--color-custom-green);
}
.text-custom-yellow {
    color: var(--color-custom-yellow);
}

.border-custom-blue {
    border-color: var(--color-custom-blue);
}
.border-custom-green {
    border-color: var(--color-custom-green);
}
.border-custom-yellow {
    border-color: var(--color-custom-yellow);
}

/* Button blue */
.custom-button-blue {
    background-color: transparent;
    color: #000;
    font-size: 0.875rem;
    letter-spacing: 3.2px;
    line-height: 30px;
}
.custom-button-blue.active {
    background-color: var(--color-custom-blue);
    color: #fff;
}
/* Button green */
.custom-button-green {
    background-color: transparent;
    border-color: var(--color-custom-green);
    color: var(--color-custom-green);
}
.custom-button-green:hover {
    background-color: var(--color-custom-green);
    border-color: var(--color-custom-green);
    color: #fff;
}
.custom-button-green.disabled {
    background-color: #f4f4f4;
    border-color: #f4f4f4;
    color: #c1c1c1;
}
/* Button yellow */
.custom-button-yellow {
    background-color: transparent;
    color: #000;
    font-size: 0.875rem;
    letter-spacing: 3.2px;
    line-height: 30px;
}
.custom-button-yellow.active {
    background-color: var(--color-custom-yellow);
    color: #000;
}

body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: 'Noto Sans TC', sans-serif;
    background: #fff;
    overflow-x: hidden;
}

/* tw font */
h1, .h1 {
    font-size: 2rem;
    font-weight: 700;
    letter-spacing: 5.6px;
    line-height: 1.3;
}
h2, .h2 {
    font-size: 1.25rem;
    font-weight: 700;
    line-height: normal;
    letter-spacing: 5.6px;
}
h3, .h3 {
    font-size: 1.25rem;
    font-weight: 600;
    line-height: normal;
    letter-spacing: 4.2px;
    
}
h4, .h4 {
    font-size: 1.25rem;
    font-weight: 500;
    line-height: normal;
    letter-spacing: 2.8px;
    color: var(--color-custom-blue);
}
h5, .h5 {
    font-size: 0.875rem;
    color: var(--color-custom-blue);
}
p, label {
    font-size: 0.875rem;
    letter-spacing: 2.4px;
    line-height: 20px;
}

.splide span {
    font-size: 5vw;
}
.slide h1, .slide .h1 {
    font-size: 7vw;
    font-weight: 700;
}
.slide h3, .slide .h3 {
    font-size: 4vw;
    font-weight: 500;
}

.accordion .h4 {
    font-size: 1.25rem;
}
footer span,  footer p {
    font-size: 0.625rem;
    letter-spacing: 0;
    line-height: 1.5rem;
}

/* en font */
.en h1, .en .h1,
.en h2, .en .h2,
.en h3, .en .h3,
.en h4, .en .h4,
.en h5, .en .h5,
.en h6, .en .h6,
.en p, .en span,
.en a, .en label {
    letter-spacing: 0.05rem;
}

main {
    padding-top: var(--header-height);
}

/* custom splide */
.splide__arrow {
    background: #fff;
    opacity: 0.5;
}
.splide__pagination {
    bottom: 7rem;
}
.splide__slide,
.slide {
    height: calc(100vh - var(--header-height)) !important;
}
.banner-title {
    width: 90vw;
}
.banner-button-yellow {
    background-color: var(--color-custom-yellow);
}
.banner-button-yellow:hover {
    background-color: var(--color-custom-darkyellow);
}
.banner-button-lightgreen {
    background-color: var(--color-custom-lightgreen);
}
.banner-button-lightgreen:hover {
    background-color: var(--color-custom-darkgreen);
}

/* custom scrollbar */
.scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
}
.scrollbar-hide::-webkit-scrollbar {
    display: none;
}
.scroll-smooth {
    scroll-behavior: smooth;
}

/* custom quill */
.ql-snow .ql-editor h4 {
    font-size: 1.25rem;
}

/* custom bg */
.gradient-line {
    width: 70px;  /* 或設定具體寬度，例如 200px */
    height: 8px;  /* 調整線條粗細 */
    border-radius: 4px;  /* 讓線條兩端變圓 */
    background: linear-gradient(to right, var(--color-custom-lightgreen), var(--color-custom-lightgreen));
}
.yellow-line {
    width: 160px;  /* 或設定具體寬度，例如 200px */
    height: 8px;  /* 調整線條粗細 */
    border-radius: 4px;  /* 讓線條兩端變圓 */
    background: var(--color-custom-yellow);
}
.img-shadow {
    /* border-radius: 50px; */
    box-shadow: 20px 20px var(--color-custom-yellow);
}
.bg-promotion {
    background-image: url(/images/desktop/aboutbglg.png);
}
.bg-plan {
    background-image: url(/images/mobile/planbg_mobile.jpg);
}
.bg-news {
    background-image: url(/images/mobile/newsBG.png);
}
.bg-partner {
    background-image: url(/images/mobile/partner_mobile.png);
}

.headerLink {
    display: block;
    padding: 0.75rem 0.5rem;
    width: 100%;
    font-size: 0.875rem;
    color: #4b5563;           /* text-gray-600 */
    text-align: center;       /* text-center */
    letter-spacing: 0.1rem;    /* tracking-widest */
}
.headerLink.active {
    background-color: #E6EAEB;
    color: var(--color-custom-blue);
}

/* banner buttin hover 動畫 */
.banner-button {
    position: relative;
    overflow: hidden;
}

/* 設置兩個不同的箭頭，一個用於滑鼠進入，一個用於滑鼠離開 */
.banner-button .arrow-icon {
    position: relative;
    margin-left: 16px;
    transition: opacity 0.01s;
}

/* 滑鼠進入時，箭頭從左側進入 */
.banner-button .arrow-icon {
    transform: translateX(-150%);
    opacity: 0;
}

.banner-button:hover .arrow-icon {
    animation: enter-from-left 0.4s forwards;
}

/* 滑鼠離開時，箭頭從右側進入 */
.banner-button:not(:hover) .arrow-icon {
    animation: enter-from-right 0.4s forwards;
}

@keyframes enter-from-left {
    0% {
        transform: translateX(-150%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes enter-from-right {
    0% {
        transform: translateX(150%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

.promotion-link:hover {
    filter: hue-rotate(-90deg)
}

/* 自定義動畫 - 從左中移動到右上 */
@keyframes moveDiagonal {
    0% {
        transform: translate(-150%, -75%) scale(0.8);
        opacity: 0.5;
    }
    100% {
        transform: translate(75%, -150%) scale(1);
        opacity: 1;
    }
}
  
.animate-move-diagonal {
    animation: moveDiagonal 4s ease-out infinite;
}

/* Tablet breakpoint */
@media (min-width: 640px) {
    h1 {
        font-size: 2.75rem;
    }
    h2, .h2 {
        font-size: 2.5rem;
    }
    h3, .h3 {
        font-size: 1.875rem;
        font-weight: 700;
    }
    h4, .h4 {
        font-size: 1.875rem;
    }
    h5, .h5 {
        font-size: 1.25rem;
    }
    p, label {
        font-size: 1rem;
        letter-spacing: 3.2px;
        line-height: 30px;
    }

    .splide span {
        font-size: 2.2vw;
    }
    .slide h1, .slide .h1 {
        font-size: 5vw;
    }
    .slide h3, .slide .h3 {
        font-size: 3vw;
    }
    
    .accordion .h4 {
        font-size: 1.4rem;
    }
    footer span,  footer p {
        font-size: 0.875em;
    }

    .custom-button-blue {
        font-size: 1rem;
    }

    /* custom splide */
    .splide__pagination {
        bottom: 4rem;
    }
    .banner-title {
        width: 50vw;
    }

    /* custom quill */
    .ql-snow .ql-editor h4 {
        font-size: 1.875rem;
    }

    .bg-news {
        background-image: none;
    }
    .bg-plan {
        background-image: url(/images/tablet/planbg_tablet.jpg);
    }
    .bg-partner {
        background-image: url(/images/tablet/partner_tablet.png);
    }
}


/* Desktop breakpoint */
@media (min-width: 1024px) {
    :root {
        --header-height: 72px; /* 導覽列高度 */
    }

    h1 {
        font-size: 2.75rem;
    }
    h2, .h2 {
        font-size: 2.5rem;
    }
    h3, .h3 {
        font-size: 2rem;
        font-weight: 700;
    }
    h4, .h4 {
        font-size: 1.875rem;

    }
    h5, .h5 {
        font-size: 1.25rem;
    }
    p, label {
        font-size: 1rem;
    }

    .splide span {
        font-size: 2vw;
    }
    .slide h1, .slide .h1 {
        font-size: 3vw;
    }
    .slide h3, .slide .h3 {
        font-size: 1.72vw;
    }
    
    .accordion .h4 {
        font-size: 1.4rem;
    }
    footer span,  footer p {
        font-size: 1rem;
    }

    .custom-button-blue {
        font-size: 1rem;
    }

    .banner-title {
        width: 50vw;
    }

    /* custom quill */
    .ql-snow .ql-editor h4 {
        font-size: 1.875rem;
    }

    .bg-plan {
        background-image: url(/images/desktop/planbg_desktop.jpg);
    }
    .bg-partner {
        background-image: url(/images/desktop/partner_desktop.png);
    }
    
    .headerLink {
        padding-left: 0;        /* lg:px-0 */
        padding-right: 0;       /* lg:px-0 */
        width: auto;
        letter-spacing: 0.1rem;
    }
    
    .headerLink.active {
        background-color: transparent;
        color: var(--color-custom-blue);
        border-color: var(--color-custom-blue);
        border-bottom: 4px solid;
    }
}